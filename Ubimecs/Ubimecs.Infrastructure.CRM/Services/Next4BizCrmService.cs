using System.Text.RegularExpressions;
using Microsoft.Extensions.Options;
using Ubimecs.Infrastructure.CRM.Configuration;
using Ubimecs.Infrastructure.CRM.Constants;
using Ubimecs.Infrastructure.CRM.Constants.Next4Biz.Person;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Converters;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Mappings;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses.Dto;

namespace Ubimecs.Infrastructure.CRM.Services;

public class Next4BizCrmService: ICrmService
{
    private readonly INext4BizCrmRequestService _next4BizCrmRequestService;
    private readonly Next4BizCrmSettings _settings;
    
    public Next4BizCrmService(INext4BizCrmRequestService next4BizCrmRequestService, IOptions<Next4BizCrmSettings> settings)
    {
        _next4BizCrmRequestService = next4BizCrmRequestService;
        _settings = settings.Value;
    }
    public async Task<AuthResponseDto> Auth()
    {
        var authRequest = new AuthRequest
        {
            SecretKey = _settings.SecretKey,
            TenantSlug = _settings.TenantSlug
        };
        var response = await _next4BizCrmRequestService.AuthAsync(authRequest);
        return new AuthResponseDto
        {
           IsSuccess = response.Code == Next4BizConstants.ResponseCodes.Success,
           Token = response.Result.AccessToken,
           Message = response.Status
        };
    }

    public async Task<PersonResponseDto> GetPerson(PersonRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        
        var getPersonRequest = new GetPersonRequest
        {
            TenantSlug = _settings.TenantSlug,
            AccessToken = accessToken,
            Channel = _settings.ChannelId,
            Persons = new List<GetPerson>
            {
                new GetPerson
                {
                    Email = request.Email
                }
            }
        };
        var response = await _next4BizCrmRequestService.GetPersonAsync(getPersonRequest);

        return new PersonResponseDto
        {
            IsSuccess = (response.Code == Next4BizConstants.ResponseCodes.Success && response.Result.Any()),
            Message = response.Status,
            Person = response.Result
        };
    }

    public async Task<CreatePersonResponseDto> CreatePerson(CreatePersonRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var personCustomFields = PersonCustomFieldService.GetCustomFieldDictionary(_settings.Environment);
        bool isValidPhone = Regex.IsMatch(request.Phone, @"^[1-9]\d{6,14}$"); 
        //TODO: Automapper can be used
        var person = new Person
        {
            Name = request.Name,
            Surname = request.Surname,
            Birthdate = request.DateOfBirth.ToString("yyyy-MM-dd"),
            Gender = new Gender
            {
                GenderId = PersonConverter.ConvertGenderToId(request.Gender),
            },
            Emails = new List<Email>
            {
                new Email
                {
                    EmailAddress = request.Email
                }
            },
            Addresses = new List<Address>
            {
                new Address
                {
                    Country = new Country
                    {
                        CountryName = request.Nationality
                    }
                }
            }
        };
        if (!string.IsNullOrEmpty(request.IbsProfileId))
        {
            person.CustomData = new List<CustomData>
            {
                new CustomData
                {
                    Id = personCustomFields[PersonCustomField.IbsProfileId],
                    Value = request.IbsProfileId
                }
            };
        }

        if (isValidPhone)
        {
            person.Phones = new List<Phone>
            {
                new Phone
                {
                    PhoneNumber = request.Phone,
                    PhoneKind = new PhoneKind
                    {
                        Kind = Next4BizConstants.PhoneTypes.GSM
                    }
                }
            };
        }
        var createPersonRequest = new CreatePersonRequest()
        {
                TenantSlug = _settings.TenantSlug,
                Channel = _settings.ChannelId, 
                AccessToken = accessToken,
                Persons = new List<Person>
                {
                    person
                }
                
        };

      
        
        var response = await _next4BizCrmRequestService.CreatePersonAsync(createPersonRequest);
        
        var data = response.Result.FirstOrDefault();
        return new CreatePersonResponseDto
        {
            PersonId = data.PersonId
        };
    }

    public async Task<UpdatePersonDateOfBirthResponseDto> UpdatePersonDateOfBirth(UpdatePersonDateOfBirthRequestDto request)
    {
        string accessToken = (await Auth()).Token;

        var updatePersonRequest = new UpdatePersonDateOfBirthRequest
        {
            TenantSlug = _settings.TenantSlug,
            Channel = _settings.ChannelId,
            AccessToken = accessToken,
            Persons = new List<UpdatePerson>
            {
                new UpdatePerson
                {
                    PersonId = request.PersonId,
                    Birthdate = request.DateOfBirth.ToString("yyyy-MM-dd"),
                }
            }
        };
        
        var response = await _next4BizCrmRequestService.UpdatePersonDateOfBirthAsync(updatePersonRequest);
        return new UpdatePersonDateOfBirthResponseDto
        {
            Success = response.Code == "200"
        };
    }

    public async Task<UpdatePersonNationalityResponseDto> UpdatePersonNationality(UpdatePersonNationalityRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        
        var personCustomFields = PersonCustomFieldService.GetCustomFieldDictionary(_settings.Environment);


        var updatePersonNationalityRequest = new UpdatePersonNationalityRequest
        {
            TenantSlug = _settings.TenantSlug,
            Channel = _settings.ChannelId,
            AccessToken = accessToken,
            Persons = new List<UpdatePersonNationality>
            {
                new UpdatePersonNationality
                {
                    PersonId = request.PersonId,
                    CustomData = new List<CustomData>
                    {
                        new CustomData
                        {
                            Id = personCustomFields[PersonCustomField.CustomerNationality],
                            Value = request.Nationality
                        }
                    }
                }
            }
        };
        
        var response = await _next4BizCrmRequestService.UpdatePersonNationalityAsync(updatePersonNationalityRequest);
        return new UpdatePersonNationalityResponseDto
        {
            Success = response.Code == "200"
        };
    }

    public async Task<UpdatePersonIbsProfileIdResponseDto> UpdatePersonIbsProfile(UpdatePersonIbsProfileIdRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        
        var personCustomFields = PersonCustomFieldService.GetCustomFieldDictionary(_settings.Environment);
        
        var updatePersonNationalityRequest = new UpdatePersonIbsProfileIdRequest
        {
            TenantSlug = _settings.TenantSlug,
            Channel = _settings.ChannelId,
            AccessToken = accessToken,
            Persons = new List<UpdatePersonIbsProfile>
            {
                new UpdatePersonIbsProfile
                {
                    PersonId = request.PersonId,
                    CustomData = new List<CustomData>
                    {
                        new CustomData
                        {
                            Id = personCustomFields[PersonCustomField.IbsProfileId],
                            Value = request.IbsProfileId
                        }
                    }
                }
            }
        };
        
        var response = await _next4BizCrmRequestService.UpdatePersonIbsProfileAsync(updatePersonNationalityRequest);
        return new UpdatePersonIbsProfileIdResponseDto
        {
            Success = response.Code == "200"
        };
    }

    public async Task<CreateTravelCompanionResponse> CreateTravelCompanion(CreateTravelCompanionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var travelCompanionRequest = request.Map(_settings,accessToken);
        var response = await _next4BizCrmRequestService.CreateTravelCompanionAsync(travelCompanionRequest);
        return new CreateTravelCompanionResponse
        {
            Status = response.Status
        };
    }

    public async Task<RemoveTravelCompanionResponseDto> RemoveTravelCompanion(RemoveTravelCompanionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var removeTravelCompanionRequest = request.Map(_settings,accessToken);
        var response = await _next4BizCrmRequestService.RemoveTravelCompanionAsync(removeTravelCompanionRequest);
        return new RemoveTravelCompanionResponseDto{Status = response.Status == "Success"};
    }

    public async Task<PersonResponseDto> GetOrCreatePerson(string email, CreatePersonRequestDto request)
    {
        var personCustomFields = PersonCustomFieldService.GetCustomFieldDictionary(_settings.Environment);
        string ibsProfileCustomFieldId = "cf_"+personCustomFields[PersonCustomField.IbsProfileId];
        string subscriptionCustomFieldId = "cf_" + personCustomFields[PersonCustomField.CustomerCardSource];
        var person = await GetPerson(new PersonRequestDto { Email = email });
        if (!person.IsSuccess)
        {
            var createPersonResponse = await CreatePerson(request);
            if (!string.IsNullOrEmpty(createPersonResponse.PersonId))
            {
                person = await GetPerson(new PersonRequestDto { Email = email });
            }
        }

        var personData = person.Person.FirstOrDefault();
        string? ibsProfileIdValue = personData?.CustomData[ibsProfileCustomFieldId]?.ToString();
        string? subscriptionIdValue = personData?.CustomData != null && personData.CustomData.TryGetValue(subscriptionCustomFieldId, out var data) ? data?.ToString()
                : null; //personData?.CustomData[subscriptionCustomFieldId]?.ToString();
        if (string.IsNullOrEmpty(ibsProfileIdValue))
        {
           var ibsProfileUpdateResponse = await UpdatePersonIbsProfile(new UpdatePersonIbsProfileIdRequestDto
                { PersonId = personData.PersonId, IbsProfileId = request.IbsProfileId });
        }
        if (string.IsNullOrEmpty(subscriptionIdValue))
        {
            var subscriptionUpdateResponse = await UpdatePersonSubscription(new UpdatePersonSubscriptionRequestDto
                { PersonId = personData.PersonId, CustomerCardSource = "1" });
        }
        return person;
    }

    public async Task<CloseAccountResponseDto> CloseAccount(CloseAccountRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var closeAccountRequest = request.Map(_settings,accessToken);
        var response = await _next4BizCrmRequestService.CloseAccountAsync(closeAccountRequest);
        return new CloseAccountResponseDto{Status = response.Status == "Success"};
    }
    
    public async Task<PersonEmailPermissionResponseDto> GetPersonEmailPermission(GetCommunicationPermissionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var personEmailPermissionRequest = request.Map(_settings,accessToken);
        var response = await _next4BizCrmRequestService.GetPersonEmailPermissionAsync(personEmailPermissionRequest);
        return new PersonEmailPermissionResponseDto
            { IsAuthorized = response.Result.FirstOrDefault()?.IsAuthorized ?? false };
    }

    public async Task<PersonPhonePermissionResponseDto> GetPersonPhonePermission(GetCommunicationPermissionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var personPhonePermissionRequest = request.MapToPhonePermission(_settings,accessToken);
        var response = await _next4BizCrmRequestService.GetPersonPhonePermissionAsync(personPhonePermissionRequest);
        var person = response.Result.FirstOrDefault();
        return new PersonPhonePermissionResponseDto()
            { IsCallAuthorized = person?.IsCallAuthorized ?? false,IsSmsAuthorized = person?.IsSmsAuthorized ?? false};
    }

    public async Task<PersonEmailPermissionResponseDto> UpdatePersonEmailPermission(UpdateCommunicationPermissionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var updatePersonMailPermissionRequest = request.MapToUpdateEmailPermission(_settings,accessToken);
        var response =  await _next4BizCrmRequestService.UpdatePersonEmailPermissionAsync(updatePersonMailPermissionRequest);
        return new PersonEmailPermissionResponseDto
        {
            IsAuthorized = response.Result?.FirstOrDefault()?.IsAuthorized ?? false
        };
    }

    public async Task<PersonPhonePermissionResponseDto> UpdatePersonPhonePermission(UpdateCommunicationPermissionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var updatePersonPhonePermissionRequest = request.MapToUpdatePhonePermission(_settings,accessToken);
        var response =
            await _next4BizCrmRequestService.UpdatePersonPhonePermissionAsync(updatePersonPhonePermissionRequest);
        return new PersonPhonePermissionResponseDto
        {
            IsSmsAuthorized = response.Result.FirstOrDefault()?.IsSmsAuthorized ?? false,
            IsCallAuthorized = response.Result.FirstOrDefault()?.IsCallAuthorized ?? false,
            
        };
    }

    public async Task<FlightResponseDto> CreateBooking(List<FlightRequestDto> request)
    {
        try
        {
            string accessToken = (await Auth()).Token;
            var createBookingRequest = request.Map(_settings,accessToken);
            var response = await _next4BizCrmRequestService.CreateBookingAsync(createBookingRequest);
            return new FlightResponseDto{Status = response.Status == "Success", Id = response.Result?.Id};
        }
        catch (Exception e)
        {
            return null;
        }
    }

    public async Task<FlightResponseDto> ModifyBooking(List<FlightRequestDto> request)
    {
        try
        {
            string accessToken = (await Auth()).Token;
            var modifyBookingRequest = request.Map(_settings,accessToken);
            var response = await _next4BizCrmRequestService.ModifyBookingAsync(modifyBookingRequest);
            return new FlightResponseDto{Status = response.Status == "Success", Id = response.Result?.Id};
        }
        catch (Exception e)
        {
            return null;
        }
    }

    public async Task<FlightResponseDto> GetFlight(GetFlightRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var getFlightRequest = request.Map(_settings, accessToken);
        var response = await _next4BizCrmRequestService.GetFlightAsync(getFlightRequest);
        return new FlightResponseDto{Status = response.Status == "Success", Id = response.Result?.Id};
    }

    public async Task<List<PnrInfoResponseDto>> GetPnrInfo(GetPnrInfoRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var pnrInfoRequest = request.Map(_settings,accessToken);
        var response = await _next4BizCrmRequestService.GetPnrInfoAsync(pnrInfoRequest);
        return response.Result.Select(r => new PnrInfoResponseDto
        {
            Id = r.Id
        }).ToList();
    }

    public async Task<UpdatePersonSubscriptionResponseDto> UpdatePersonSubscription(UpdatePersonSubscriptionRequestDto request)
    {
        string accessToken = (await Auth()).Token;
        var personSubscriptionRequest = request.MapToSubscription(_settings,accessToken);
        var response = await _next4BizCrmRequestService.UpdatePersonSubscriptionAsync(personSubscriptionRequest);
        return new UpdatePersonSubscriptionResponseDto { Success = true };
    }
}