using System.Net;
using System.Text;
using System.Text.Json;
using Ubimecs.Infrastructure.CRM.Constants;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Exceptions;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Common;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses;

namespace Ubimecs.Infrastructure.CRM.Services;

public class Next4BizCrmRequestService: INext4BizCrmRequestService
{
    private readonly HttpClient _httpClient;
    public Next4BizCrmRequestService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }
    public async Task<BaseNext4BizCrmResponse<AuthResponse>> AuthAsync(AuthRequest request)
    {
        string result = string.Empty;
        try
        {
            string endpointUrl = Endpoints.Auth;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
        
            var response = await _httpClient.PostAsync(endpointUrl, content);
            result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<AuthResponse>>(result);
                return data;
            }
            
            throw new Next4BizCrmException(response.StatusCode, result);
            
        }
        catch (Exception e)
        {
            string exMessage = string.Format("Exception:{0}-Next4BizResponse:{1}", e.ToString(), result);
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, exMessage);
        }
        
    }

    public async Task<BaseNext4BizCrmResponse<List<CreatePersonResponse>>> CreatePersonAsync(CreatePersonRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.Person;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<CreatePersonResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<UpdatePersonDateOfBirthResponse>>> UpdatePersonDateOfBirthAsync(UpdatePersonDateOfBirthRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.Person;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<UpdatePersonDateOfBirthResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<UpdatePersonNationalityResponse>>> UpdatePersonNationalityAsync(UpdatePersonNationalityRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.Person;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<UpdatePersonNationalityResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }    
    }

    public async Task<BaseNext4BizCrmResponse<List<UpdatePersonIbsProfileResponse>>> UpdatePersonIbsProfileAsync(UpdatePersonIbsProfileIdRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.Person;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<UpdatePersonIbsProfileResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }  
    }

    public async Task<BaseNext4BizCrmResponse<List<GetPersonResponse>>> GetPersonAsync(GetPersonRequest request)
    {
        try
        {
            string jsonRequest = JsonSerializer.Serialize(request);

            string encodedJson = Uri.EscapeDataString(jsonRequest);

            string endpointUrl = $"{Endpoints.Person}?{encodedJson}";

            var response = await _httpClient.GetAsync(endpointUrl);

            var result = await response.Content.ReadAsStringAsync();

            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<GetPersonResponse>>>(result);
                return data;
            }

            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
        
    }

    public async Task<BaseNext4BizCrmResponse<List<GetPersonPhonePermissionResponse>>> GetPersonPhonePermissionAsync(GetPersonPhonePermissionRequest request)
    {
        try
        {
            string jsonRequest = JsonSerializer.Serialize(request);

            string encodedJson = Uri.EscapeDataString(jsonRequest);

            string endpointUrl = $"{Endpoints.PersonPhonePermission}?{encodedJson}";

            var response = await _httpClient.GetAsync(endpointUrl);

            var result = await response.Content.ReadAsStringAsync();

            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<GetPersonPhonePermissionResponse>>>(result);
                return data;
            }

            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }

    }

    public async Task<BaseNext4BizCrmResponse<List<UpdatePersonPhonePermissionResponse>>> UpdatePersonPhonePermissionAsync(UpdatePersonPhonePermissionRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.PersonPhonePermission;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<UpdatePersonPhonePermissionResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<UpdatePersonEmailPermissionResponse>>> UpdatePersonEmailPermissionAsync(UpdatePersonEmailPermissionRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.PersonEmailPermission;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<UpdatePersonEmailPermissionResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<GetPersonEmailPermissionResponse>>> GetPersonEmailPermissionAsync(GetPersonEmailPermissionPermissionRequest request)
    {
        try
        {
            string jsonRequest = JsonSerializer.Serialize(request);

            string encodedJson = Uri.EscapeDataString(jsonRequest);

            string endpointUrl = $"{Endpoints.PersonEmailPermission}?{encodedJson}";

            var response = await _httpClient.GetAsync(endpointUrl);

            var result = await response.Content.ReadAsStringAsync();

            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<GetPersonEmailPermissionResponse>>>(result);
                return data;
            }

            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<CreateTravelCompanionResponse>>> CreateTravelCompanionAsync(CreateTravelCompanionRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.VTableRow;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<CreateTravelCompanionResponse>>>(result);
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<RemoveTravelCompanionResponse>>> RemoveTravelCompanionAsync(RemoveTravelCompanionRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.VTableRow;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var requestBody = new HttpRequestMessage(HttpMethod.Delete, endpointUrl)
            {
                Content = content
            };
            var response = await _httpClient.SendAsync(requestBody);            
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<RemoveTravelCompanionResponse>>>(result);
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<List<CloseAccountResponse>>> CloseAccountAsync(CloseAccountRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.Person;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<CloseAccountResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<FlightResponse>> CreateBookingAsync(FlightRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.VTableRow;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<FlightResponse>>(result);
                return data;
            }
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public async Task<BaseNext4BizCrmResponse<FlightResponse>> ModifyBookingAsync(FlightRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.VTableRow;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<FlightResponse>>(result);
                return data;
            }
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }
    }

    public Task<BaseNext4BizCrmResponse<FlightResponse>> GetFlightAsync(GetFlightRequest request)
    {
        throw new NotImplementedException();
    }

    public async Task<BaseNext4BizCrmResponse<List<PnrInfoResponse>>> GetPnrInfoAsync(PnrInfoRequest request)
    {
        try
        {
            string jsonRequest = JsonSerializer.Serialize(request);
            
            string encodedJson = Uri.EscapeDataString(jsonRequest);

            string endpointUrl = $"{Endpoints.VTableRow}?{encodedJson}";

            var response = await _httpClient.GetAsync(endpointUrl);

            var result = await response.Content.ReadAsStringAsync();

            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<PnrInfoResponse>>>(result);
                return data;
            }

            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }

    }

    public async Task<BaseNext4BizCrmResponse<List<UpdatePersonSubscriptionResponse>>> UpdatePersonSubscriptionAsync(UpdatePersonSubscriptionRequest request)
    {
        try
        {
            string endpointUrl = Endpoints.Person;
            string jsonContent = JsonSerializer.Serialize(request);
            StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpointUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = JsonSerializer.Deserialize<BaseNext4BizCrmResponse<List<UpdatePersonSubscriptionResponse>>>(result); 
                return data;
            }
        
            throw new Next4BizCrmException(response.StatusCode, result);
        }
        catch (Exception e)
        {
            throw new Next4BizCrmException(HttpStatusCode.BadRequest, e.ToString());
        }  
    }
}