using Microsoft.AspNetCore.Mvc;
using Ubimecs.Application.Features.Rebooking;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.API.Controllers.v1
{
    [Route("api/v1/[controller]/[action]")]
    [ApiController]
    public class RebookingController : ControllerBase
    {
        private readonly ICrmService _crmService;
        public RebookingController(ICrmService crmService)
        {
            _crmService = crmService;
        }
        [HttpPost]
        public ServiceResponse GetPnrInfo(MainRequest request)
        {
            return new GetPnrInfoService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetTourOperatorPnrInfo(MainRequest request)
        {
            return new GetTourOperatorPnrInfoService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SearchFlight(MainRequest request)
        {
            return new RebookingSearchFlightService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SelectFlight(MainRequest request)
        {
            return new RebookingSelectFlightService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SelectBundle(MainRequest request)
        {
            return new RebookingSelectBundleService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetFlightSeats(MainRequest request)
        {
            return new Application.Features.Rebooking.GetFlightSeatsService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SelectSeat(MainRequest request)
        {
            return new RebookingSelectSeatService(request).Work();
        }

        [HttpPost]
        public ServiceResponse ReSelectSeat(MainRequest request)
        {
            return new RebookingReSelectSeatService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SelectIfe(MainRequest request)
        {
            return new RebookingSelectIfeService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SelectGolfBundle(MainRequest request)
        {
            return new RebookingSelectGolfBundleService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetBaggageAllowance(MainRequest request)
        {
            return new RebookingGetBaggageAllowanceService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SetExtraBaggage(MainRequest request)
        {
            return new RebookingSetExtraBaggageService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SetSportEquipment(MainRequest request)
        {
            return new RebookingSetSportEquipmentService(request).Work();
        }

        [HttpPost]
        public ServiceResponse ReSetSportEquipment(MainRequest request)
        {

            return new RebookingReSetSportEquipmentService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SetCorona(MainRequest request)
        {
            return new RebookingSetCoronaService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetMeals(MainRequest request)
        {
            return new RebookingGetMealsService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SelectMeal(MainRequest request)
        {
            return new RebookingSelectMealService(request).Work();
        }

        [HttpPost]
        public ServiceResponse ReSelectMeal(MainRequest request)
        {
            return new RebookingReSelectMealService(request).Work();
        }

        [HttpPost]
        public ServiceResponse CancelFlight(MainRequest request)
        {
            return new CancelFlightService(request).Work();
        }       
        
        [HttpPost]
        public ServiceResponse CompleteCancelFlight(MainRequest request)
        {
            return new CompleteCancelFlightService(request).Work();
        }

        [HttpPost]
        public ServiceResponse CancelPnr(MainRequest request)
        {
            return new CancelPnrService(request).Work();
        }
        
        [HttpPost]
        public ServiceResponse CancelBooking(MainRequest request)
        {
            return new CancelBookingService(request).Work();
        }

        [HttpPost]
        public ServiceResponse CompleteCancelPnr(MainRequest request)
        {
            return new CompleteCancelPnrService(request).Work();
        }
        
        [HttpPost]
        public ServiceResponse CompleteCancelBooking(MainRequest request)
        {
            return new CompleteCancelBookingService(request).Work();
        }

        [HttpPost]
        public ServiceResponse AddExtrasGetServices(MainRequest request)
        {
            return new RebookingAddExtrasGetServicesService(request).Work();
        }

        [HttpPost]
        public ServiceResponse UpgradeBundleGetServices(MainRequest request)
        {
            return new RebookingUpgradeBundleGetServicesService(request).Work();
        }

        [HttpPost]
        public ServiceResponse NameChange(MainRequest request)
        {
            return new RebookingNameChangeService(request).Work();
        }

        [HttpPost]
        public ServiceResponse TravelDocumentChange(MainRequest request)
        {
            return new RebookingTravelDocumentChangeService(request).Work();
        }

        [HttpPost]
        public ServiceResponse ContactInfoChange(MainRequest request)
        {
            return new RebookingContactInfoChangeService(request).Work();
        }

        [HttpPost]
        public ServiceResponse OrderChange(MainRequest request)
        {
            return new RebookingOrderChangeService(request, _crmService).Work();
        }

        [HttpPost]
        public ServiceResponse ChangeCurrency(MainRequest request)
        {
            return new RebookingChangeCurrencyService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SearchGuest(MainRequest request)
        {
            return new SearchGuestService(request).Work();
        }

        [HttpPost]
        public ServiceResponse CheckIn(MainRequest request)
        {
            return new CheckInService(request).Work();
        }

        [HttpPost]
        public ServiceResponse PrintBoardingPass(MainRequest request)
        {
            return new PrintBoardingPassService(request).Work();
        }

        [HttpPost]
        public async Task<ServiceResponse> SmsPrintBoardingPass(MainRequest request)
        {
            return await (new SmsPrintBoardingPassService(request,true).WorkAsync());
        }

        [HttpPost]
        public ServiceResponse AddToWallet(MainRequest request)
        {
            return new AddToWalletService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetPaymentBalance(MainRequest request)
        {
            return new GetPaymentBalanceService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetInstallmentFee(MainRequest request)
        {
            return new RebookingGetInstallmentFeeService(request).Work();
        }

        [HttpPost]
        public ServiceResponse AddGroupProfile(MainRequest request)
        {
            return new AddGroupProfileService(request).Work();
        }

        [HttpPost]
        public ServiceResponse RemoveGroupProfile(MainRequest request)
        {
            return new RemoveGroupProfileService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetBundle(MainRequest request)
        {
            return new RebookingGetBundleService(request).Work();
        }

        [HttpPost]
        public ServiceResponse SendBoardingPass(MainRequest request)
        {
            return new RebookingSendBoardingPassService(request).Work();
        }

        [HttpPost]
        public ServiceResponse RebookingGetExtras(MainRequest request)
        {
            return new RebookingGetExtrasService(request).Work();
        }
        
        [HttpPost]
        public ServiceResponse GetSportEquipments(MainRequest request)
        {
            return new RebookingGetSportEquipmentsService(request).Work();
        }
        
        [HttpPost]
        public ServiceResponse SetExtraServices(MainRequest request)
        {
            return new RebookingSetExtraServicesService(request).Work();
        }
        
        [HttpPost]
        public ServiceResponse GetFlexes(MainRequest request)
        {
            return new RebookingGetFlexesService(request).Work();
        }
        
        [HttpPost]
        public ServiceResponse SetFlex(MainRequest request)
        {
            return new RebookingSetFlexService(request).Work();
        }

        [HttpPost]
        public ServiceResponse GetTripInformation(MainRequest request)
        {
            return new RebookingGetTripInformationService(request).Work();  
        }

        [HttpPost]
        public ServiceResponse ChangeRebookingTravelDocument(MainRequest request)
        {
            return new ChangeRebookingTravelDocumentService(request).Work();
        }

        [HttpPost]
        public ServiceResponse PrintItinary(MainRequest request)
        {
            return new PrintItinaryService(request).Work();
        }

        [HttpPost]
        public ServiceResponse CancelCheckIn(MainRequest request)
        {
            return new CancelCheckInService(request).Work();
        }
        
        [HttpPost]
        public async Task<ServiceResponse> MakePayment(MainRequest request)
        {
            return await (new MakePaymentService(request).WorkAsync());
        }

        [HttpPost]
        public async Task<ServiceResponse> AddBookingToProfile(MainRequest request)
        {
            return await (new AddBookingToProfileService(request,_crmService).WorkAsync());
        }
    }
}
