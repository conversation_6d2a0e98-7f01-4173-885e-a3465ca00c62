using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Ubimecs.Application.Features.Crm;
using Ubimecs.Infrastructure.Communication.Contracts;
using Ubimecs.Infrastructure.CRM.Configuration;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.API.Controllers.v1;

[Route("api/v1/[controller]/[action]")]
[ApiController]
public class CrmController : ControllerBase
{
    private readonly ICrmService _crmService;
    private readonly Next4BizCrmSettings _next4BizCrmSettings;
    private readonly ICommunicationService _communicationService;                               
    //TODO: GDA implementation should be added here.

    public CrmController(ICrmService crmService, IOptions<Next4BizCrmSettings> next4BizCrmSettings,ICommunicationService communicationService)
    {
        _crmService = crmService;
        _next4BizCrmSettings = next4BizCrmSettings.Value;
        _communicationService = communicationService;
    }
    
    /// <summary>
    /// Sign Up 
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public ServiceResponse CreatePerson(MainRequest request)
    {
        return new CreatePersonService(request,_crmService).Work();
    }

    [HttpPost]
    public async Task<ServiceResponse> UpdatePersonDateOfBirth(MainRequest request)
    {
        return await (new UpdatePersonDateOfBirthService(request,_crmService).WorkAsync());
    }
    
    [HttpPost]
    public async Task<ServiceResponse> UpdatePersonNationality(MainRequest request)
    {
        return await (new UpdatePersonNationalityService(request,_crmService).WorkAsync());
    }

    [HttpPost]
    public ServiceResponse SignIn(MainRequest request)
    {
        return new SignInService(request,_crmService).Work();
    }

    [HttpPost]
    public async Task<ServiceResponse >PastFlights(MainRequest request)
    {
        return await (new PastFlightService(request,_crmService,_next4BizCrmSettings).WorkAsync());
    }

    [HttpPost]
    public ServiceResponse CreateTravelCompanion(MainRequest request)
    {
        return new CreateTravelCompanionService(request, _crmService, _next4BizCrmSettings).Work();
    }

    [HttpPost]
    public ServiceResponse GetTravelCompanions(MainRequest request)
    {
        return new GetTravelCompanionService(request,_crmService,_next4BizCrmSettings).Work();
    }

    [HttpPost]
    public ServiceResponse RemoveTravelCompanion(MainRequest request)
    {
        return new RemoveTravelCompanionService(request,_crmService,_next4BizCrmSettings).Work();
    }

    [HttpPost]
    public async Task<ServiceResponse> GetCommunicationPermissions(MainRequest request)
    {
        return await (new GetCommunicationPermissionsService(request, _crmService, _next4BizCrmSettings).WorkAsync());
    }

    [HttpPost]
    public async Task<ServiceResponse> UpdateCommunicationPermissions(MainRequest request)
    {
        return await (new UpdateCommunicationPermissionService(request, _crmService, _next4BizCrmSettings,_communicationService).WorkAsync());
    }

    [HttpPost]
    public async Task<ServiceResponse> GetProfileUpcomingFlights(MainRequest request)
    {
        return await (new GetProfileUpcomingFlightsService(request).WorkAsync());
    }

    /// <summary>
    /// Process PNR List - Bulk PNR processing for CRM operations
    /// </summary>
    /// <param name="request">MainRequest containing ProcessPnrListRequest data</param>
    /// <returns>ServiceResponse with ProcessPnrListResponse data</returns>
    [HttpPost]
    public async Task<ServiceResponse> ProcessPnrList(MainRequest request)
    {
        return await (new ProcessPnrListService(request, _crmService).WorkAsync());
    }
}