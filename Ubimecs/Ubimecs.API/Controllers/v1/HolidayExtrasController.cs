using Microsoft.AspNetCore.Mvc;
using Ubimecs.Application.Features.HolidayExtras;
using Ubimecs.Infrastructure.HolidayExtras.Contracts;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.API.Controllers.v1;

[Route("api/v1/[controller]/[action]")]
[ApiController]
public class HolidayExtrasController : ControllerBase
{
    private readonly IHolidayExtrasService _holidayExtrasService;
    
    public HolidayExtrasController(IHolidayExtrasService holidayExtrasService)
    {
        _holidayExtrasService = holidayExtrasService;
    }
    
    [HttpPost]
    public async Task<ServiceResponse> GetCarParking(MainRequest request)
    {
        return await (new GetCarParkingService(request,_holidayExtrasService).WorkAsync());
    }
    
    [HttpPost]
    public async Task<ServiceResponse> GetCarParkingFilters(MainRequest request)
    {
        return await (new GetCarParkingFiltersService(request, _holidayExtrasService).WorkAsync());
    }

    [HttpPost]
    public ServiceResponse SelectCarParking(MainRequest request)
    {
        return new SelectCarParkingService(request).Work();
    }
    
    
    [HttpPost]
    public async Task<ServiceResponse> GetGroundTransfers(MainRequest request)
    {
        return await (new GetGroundTransferService(request,_holidayExtrasService).WorkAsync());
    }

    [HttpPost]
    public ServiceResponse SelectGroundTransfer(MainRequest request)
    {
        return new SelectGroundTransferService(request).Work();
    }
    
}