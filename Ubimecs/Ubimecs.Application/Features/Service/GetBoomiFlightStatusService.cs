using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Service
{
    public class GetBoomiFlightStatusService : BaseService<GetBoomiFlightStatusRequest, GetBoomiFlightStatusResponse>
    {
        public GetBoomiFlightStatusService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.GetBoomiFlightStatus(RequestData);
        }

        internal override void ValidationControl()
        {
        }
    }
}
