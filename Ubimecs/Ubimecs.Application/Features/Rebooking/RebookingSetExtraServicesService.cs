using Ubimecs.Application.Common;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.ExtraServices;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class RebookingSetExtraServicesService : BaseService<SetExtraServicesRequest, BasePriceResponse>
    {
        public RebookingSetExtraServicesService(MainRequest request) : base(request)
        {
        }

        internal override void ValidationControl()
        {
            foreach (var extraService in RequestData.ExtraServices)
            {
                if (extraService.ServiceId == NativeAPIAncillaryConstants.PET_IN_CABIN && string.IsNullOrEmpty(extraService.PetWeight))
                {
                    throw new TmobException("Pet weight is required for PETC service.");
                }
            }
        }

        internal override void FlowOperations()
        {
            if (RequestData.ExtraServices.Count == 0)
            {
                Session.CurrentFlow.PNR?.ExtraServices?.RemoveAll(e => e.ServiceId != null);
            }
            else
            {
                // First, remove all existing extra services for the given passengerId and SegmentTmobId
                foreach (var extraService in RequestData.ExtraServices)
                {
                    Session.CurrentFlow.PNR.ExtraServices.RemoveAll(t => t.PassengerId == extraService.PassengerId && t.TmobId == RequestData.TmobId);
                }

                // Then, add new extra services
                foreach (var extraService in RequestData.ExtraServices)
                {
                    if (extraService.ServiceId != null)
                    {
                        Session.CurrentFlow.PNR.ExtraServices.Add(new PassengerFlightExtraService
                        {
                            PassengerId = extraService.PassengerId,
                            TmobId = RequestData.TmobId,
                            ServiceId = extraService.ServiceId,
                            PetWeight = extraService.PetWeight
                        });
                    }
                }
            }
        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.RebookingSetExtraServices(RequestData);
        }
    }
}