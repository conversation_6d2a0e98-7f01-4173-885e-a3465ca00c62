using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking;

public class AddBookingToProfileService: BaseService<AddBookingToProfileRequest,AddBookingToProfileResponse>
{
    private readonly ICrmService _crmService;
    public AddBookingToProfileService(MainRequest request, ICrmService crmService): base(request)
    {
        _crmService = crmService;
    }

    internal override void FlowOperations()
    {
        
    }

    internal override void ValidationControl()
    {
        
    }

    internal override void InternalWork()
    {
        
    }

    internal override async Task InternalWorkAsync()
    {
        
        var pnrInfoResponse = ServiceProvider.GetPnrInfo(new GetPnrInfoRequest
        {
            OrderId = RequestData.OrderId,
            Surname = RequestData.Surname
        });

        bool isValidPnr = await ValidatePnrInfo(pnrInfoResponse);
        
        if (isValidPnr)
        {
            //modify booking (önce sadece save yapmayı dene olmazsa modify a cık.)
            
            //save modify booking
            
            var saveModifyBookingResponse = await ServiceProvider.RebookingAddFlightInProfile(RequestData);
            
            ResponseData = new AddBookingToProfileResponse { IsSuccessful = true, PnrInfo = saveModifyBookingResponse};
        }
        
    }
    
    private async Task<bool> ValidatePnrInfo(GetPnrInfoResponse pnrInfoResponse)
    {
        if (pnrInfoResponse == null || pnrInfoResponse.StatusCode != "ACTIVE")
            throw new TmobException("Pnr status is not ACTIVE");
        
        if (pnrInfoResponse.FlightDestionationList.Any(x => x.SegmentStatus != "CONFIRMED"))
            throw new TmobException("Flight segment status is not CONFIRMED");
        
        var now = DateTime.UtcNow;
        if (!pnrInfoResponse.FlightDestionationList.Any(fd => fd.Segments.Any(s =>s.DepartureDate > now)))
            throw new TmobException("Flight segment is not available");

        var contactEmail = pnrInfoResponse.ContactList
            .FirstOrDefault(c => c.ContactType == "H")?.EmailAddress;

        var crmMail = Session.IdentityInfo.Email;
        
        if (string.IsNullOrEmpty(contactEmail) || !string.Equals(contactEmail.Trim(), crmMail.Trim(), StringComparison.OrdinalIgnoreCase))
            throw new TmobException("Contact email is not valid");

        var crmPnrList = await _crmService.GetPnrInfo(new GetPnrInfoRequestDto { PnrNumber = RequestData.OrderId });
        
        if(!crmPnrList.Any())
            throw new TmobException("Pnr is not found in CRM");
        return true;

    }
}