using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class RebookingSendBoardingPassService : BaseService<SendBoardingPassRequest, SendBoardingPassResponse>
    {
        public RebookingSendBoardingPassService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {

        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.SendBoardingPass(RequestData);
        }

        internal override void ValidationControl()
        {
            
        }
    }
}
