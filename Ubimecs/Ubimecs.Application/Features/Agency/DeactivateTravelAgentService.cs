using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response.Agency;

namespace Ubimecs.Application.Features.Agency
{
    public class DeactivateTravelAgentService : BaseService<ChangeTravelAgentStatusRequest, ChangeTravelAgentStatusResponse>
    {
        public DeactivateTravelAgentService(MainRequest request) : base(request) { }

        internal override void ValidationControl()
        {
            if (RequestData.UserIds == null || !RequestData.UserIds.Any())
                throw new TmobException("User list is required");
        }

        internal override void FlowOperations() { }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.DeactivateTravelAgents(RequestData);
        }
    }
}
