using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response.Agency;

namespace Ubimecs.Application.Features.Agency
{
    public class AcceptDisruptedFlightService : BaseService<ProcessDisruptedFlightRequest, ProcessDisruptedFlightResponse>
    {
        public AcceptDisruptedFlightService(MainRequest request) : base(request) { }

        internal override void ValidationControl()
        {
            if (string.IsNullOrEmpty(RequestData?.PNRNumber))
                throw new TmobException("PNR number is required.");

            if (RequestData.OldSegmentIds == null || !RequestData.OldSegmentIds.Any())
                throw new TmobException("At least one OldSegmentId is required.");
        }

        internal override void FlowOperations() { }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.AcceptDisruptedFlight(RequestData);
        }
    }

}
