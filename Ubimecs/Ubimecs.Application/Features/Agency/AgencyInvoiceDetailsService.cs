using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response.Agency;

namespace Ubimecs.Application.Features.Agency;

public class AgencyInvoiceDetailsService : BaseService<AgencyGetInvoiceDetailsRequest, AgencyGetInvoiceDetailsResponse>
{
    public AgencyInvoiceDetailsService(MainRequest request) : base(request)
    {
        
    }
    internal override void ValidationControl()
    {
        var agencyCode = Session.AgencyInfo?.AgencyCode;
        if (string.IsNullOrEmpty(agencyCode))
        {
            throw new TmobException("Agency login is required to access invoice details");
        }
    }

    internal override void FlowOperations()
    {
        
    }

    internal override void InternalWork()
    {
        ResponseData = ServiceProvider.GetAgencyInvoiceDetails(RequestData);
    }
}