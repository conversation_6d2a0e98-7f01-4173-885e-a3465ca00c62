using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Contracts.InvoicePortal;
using Ubimecs.Infrastructure.Models.Other.InvoicePortal.Request;
using Ubimecs.Infrastructure.Models.Other.InvoicePortal.Response;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.Ticket;

public class GetTicketListService: BaseService<TicketListRequest, List<TicketListResponse>>
{
    private readonly IInvoicePortalService _invoicePortalService;
    public GetTicketListService(MainRequest request, IInvoicePortalService invoicePortalService): base(request)
    {
        _invoicePortalService = invoicePortalService;
    }

    internal override void FlowOperations()
    {
        
    }

    internal override void ValidationControl()
    {
        
    }

    internal override void InternalWork()
    {
        
    }

    internal override async Task InternalWorkAsync()
    {
        ResponseData = await ServiceProvider.GetTicketListAsync(_invoicePortalService, RequestData);
    }
}