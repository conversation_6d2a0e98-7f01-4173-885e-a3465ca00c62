using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Enums;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Logging;

namespace Ubimecs.Application.Features.Crm
{
    public class ProcessPnrListService : BaseService<ProcessPnrListRequest, ProcessPnrListResponse>
    {
        private readonly ICrmService _crmService;

        public ProcessPnrListService(MainRequest request, ICrmService crmService) : base(request, false, true)
        {
            _crmService = crmService;
        }

        internal override void FlowOperations()
        {
            // No flow operations needed for this service
        }

        internal override void ValidationControl()
        {
            if (RequestData?.PnrNumbers == null || !RequestData.PnrNumbers.Any())
            {
                throw new ArgumentException("PNR numbers list cannot be empty");
            }
        }

        internal override void InternalWork()
        {
            // This method is not used, we use InternalWorkAsync instead
        }

        internal override async Task InternalWorkAsync()
        {
            ResponseData = new ProcessPnrListResponse
            {
                TotalProcessed = RequestData.PnrNumbers.Count,
                SuccessCount = 0,
                ErrorsByStep = new Dictionary<string, List<string>>
                {
                    ["GetPnrInfo"] = new List<string>(),
                    ["GetOrCreatePerson"] = new List<string>(),
                    ["CreateBooking"] = new List<string>(),
                    ["CreateTravelCompanion"] = new List<string>()
                }
            };

            Logger.Instance.FileLog(Request.SessionId, ServiceName, $"Processing {RequestData.PnrNumbers.Count} PNRs");

            foreach (var pnrNumber in RequestData.PnrNumbers)
            {
                try
                {
                    await ProcessSinglePnrAsync(pnrNumber);
                    ResponseData.SuccessCount++;
                    Logger.Instance.FileLog(Request.SessionId, ServiceName, $"Successfully processed PNR: {pnrNumber}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.FileLog(Request.SessionId, ServiceName, $"Failed to process PNR {pnrNumber}: {ex.Message}");
                    // Error is already added to ErrorsByStep in ProcessSinglePnrAsync
                }
            }

            Logger.Instance.FileLog(Request.SessionId, ServiceName,
                $"Processing completed. Success: {ResponseData.SuccessCount}/{ResponseData.TotalProcessed}");
        }

        private async Task ProcessSinglePnrAsync(string pnrNumber)
        {
            GetPnrInfoResponse pnrInfo = null;
            string personId = null;

            // Step 1: Get PNR Info
            try
            {
                var getPnrInfoRequest = new GetPnrInfoRequest { OrderId = pnrNumber };
                pnrInfo = ServiceProvider.GetPnrInfo(getPnrInfoRequest);
                
                if (pnrInfo == null || string.IsNullOrEmpty(pnrInfo.EmailAddress))
                {
                    throw new Exception("PNR info not found or email address missing");
                }
            }
            catch (Exception ex)
            {
                ResponseData.ErrorsByStep["GetPnrInfo"].Add(pnrNumber);
                throw new Exception($"GetPnrInfo failed: {ex.Message}");
            }

            // Step 2: Get or Create Person
            try
            {
                var createPersonRequest = MapToCreatePersonRequest(pnrInfo);
                var personResponse = await _crmService.GetOrCreatePerson(pnrInfo.EmailAddress, createPersonRequest);
                
                if (personResponse?.Person?.Any() == true)
                {
                    personId = personResponse.Person.First().PersonId;
                }
                else
                {
                    throw new Exception("Failed to get or create person");
                }
            }
            catch (Exception ex)
            {
                ResponseData.ErrorsByStep["GetOrCreatePerson"].Add(pnrNumber);
                throw new Exception($"GetOrCreatePerson failed: {ex.Message}");
            }

            // Step 3: Create Booking
            try
            {
                var flightRequests = MapToFlightRequestDto(pnrInfo, personId);
                await _crmService.CreateBooking(flightRequests);
            }
            catch (Exception ex)
            {
                ResponseData.ErrorsByStep["CreateBooking"].Add(pnrNumber);
                throw new Exception($"CreateBooking failed: {ex.Message}");
            }

            // Step 4: Create Travel Companions
            try
            {
                if (pnrInfo.PassengerList?.Any() == true)
                {
                    foreach (var passenger in pnrInfo.PassengerList)
                    {
                        var travelCompanionRequest = MapToCreateTravelCompanionRequest(passenger, personId);
                        await _crmService.CreateTravelCompanion(travelCompanionRequest);
                    }
                }
            }
            catch (Exception ex)
            {
                ResponseData.ErrorsByStep["CreateTravelCompanion"].Add(pnrNumber);
                throw new Exception($"CreateTravelCompanion failed: {ex.Message}");
            }
        }

        private CreatePersonRequestDto MapToCreatePersonRequest(GetPnrInfoResponse pnrInfo)
        {
            return new CreatePersonRequestDto
            {
                Email = pnrInfo.EmailAddress,
                Name = pnrInfo.Name ?? "",
                Surname = pnrInfo.Surname ?? "",
                Phone = pnrInfo.PhoneNumber ?? "",
                DateOfBirth = DateTime.Now.AddYears(-30), // Default if not available
                Nationality = "TR", // Default nationality
                Gender = "Male" // Default gender
            };
        }

        private List<FlightRequestDto> MapToFlightRequestDto(GetPnrInfoResponse pnrInfo, string personId)
        {
            var flightRequests = new List<FlightRequestDto>();

            if (pnrInfo.FlightDestionationList?.Any() == true)
            {
                var firstFlight = pnrInfo.FlightDestionationList.First();
                
                var flightRequest = new FlightRequestDto
                {
                    PersonId = personId,
                    PnrNumber = pnrInfo.PNRNumber,
                    TotalAmount = decimal.TryParse(pnrInfo.TotalAmount, out var amount) ? amount : 0,
                    Currency = pnrInfo.Currency ?? "TRY",
                    PaxCount = pnrInfo.PassengerList?.Count ?? 1,
                    DepartureCode = firstFlight.DepartureCode ?? "",
                    ArrivalCode = firstFlight.ArrivalCode ?? "",
                    DepartureDate = firstFlight.Segments?.FirstOrDefault()?.DepartureDate.ToString("yyyy-MM-dd") ?? "",
                    ArrivalDate = firstFlight.Segments?.LastOrDefault()?.ArrivalDate.ToString("yyyy-MM-dd") ?? "",
                    FlightNumber = int.TryParse(firstFlight.Segments?.FirstOrDefault()?.FlightNumber, out var flightNum) ? flightNum : 0,
                    AirlineId = "XQ", // SunExpress default
                    Channel = pnrInfo.OrderCreatedChannel ?? "WEB",
                    PnrStatus = "CONFIRMED",
                    PnrType = "NORMAL",
                    Passengers = MapPassengers(pnrInfo.PassengerList)
                };

                flightRequests.Add(flightRequest);
            }

            return flightRequests;
        }

        private List<FlightPassenger> MapPassengers(List<PassengerInformationDTO> passengers)
        {
            if (passengers == null) return new List<FlightPassenger>();

            return passengers.Select(p => new FlightPassenger
            {
                FirstName = p.GivenName ?? "",
                LastName = p.Surname ?? "",
                Email = "", // PassengerInformationDTO doesn't have EmailAddress
                Phone = "", // PassengerInformationDTO doesn't have PhoneNumber
                Gender = p.NameTitle == "MR" ? "Male" : "Female",
                Nationality = p.CitizenshipCountryCode ?? "TR",
                BirthDate = p.Birthdate?.ToString("yyyy-MM-dd") ?? "",
                PaxType = p.PassengerType ?? "ADULT",
                PaxId = int.TryParse(p.Id, out var id) ? id : 0
            }).ToList();
        }

        private CreateTravelCompanionRequestDto MapToCreateTravelCompanionRequest(PassengerInformationDTO passenger, string personId)
        {
            return new CreateTravelCompanionRequestDto
            {
                PersonId = personId,
                CustomerId = passenger.Id ?? "",
                FirstName = passenger.GivenName ?? "",
                LastName = passenger.Surname ?? "",
                Gender = passenger.NameTitle == "MR" ? TravelCompanionGender.Male : TravelCompanionGender.Female,
                BirthDate = passenger.Birthdate ?? DateTime.Now.AddYears(-30),
                Nationality = passenger.CitizenshipCountryCode ?? "TR"
            };
        }
    }
}
