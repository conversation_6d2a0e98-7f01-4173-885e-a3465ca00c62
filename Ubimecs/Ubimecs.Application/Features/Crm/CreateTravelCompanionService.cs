using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.CRM.Configuration;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.Crm;

public class CreateTravelCompanionService: BaseService<CreateTravelCompanionRequestDto,CreateTravelCompanionResponse>
{
    private readonly ICrmService _crmService;
    private readonly Next4BizCrmSettings _next4BizCrmSettings;
    public CreateTravelCompanionService(MainRequest request, ICrmService crmService,Next4BizCrmSettings next4BizCrmSettings): base(request)
    {
        _crmService = crmService;
        _next4BizCrmSettings = next4BizCrmSettings;
    }

    internal override void ValidationControl()
    {
        
    }

    internal override void FlowOperations()
    {
        
    }

    internal override void InternalWork()
    {
        ResponseData = ServiceProvider.CreateTravelCompanion(_crmService, _next4BizCrmSettings, RequestData);
    }
}