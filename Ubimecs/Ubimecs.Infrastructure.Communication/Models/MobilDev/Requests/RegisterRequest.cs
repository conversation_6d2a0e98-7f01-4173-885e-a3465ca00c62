namespace Ubimecs.Infrastructure.Communication.Models.MobilDev.Requests;

using System.Text.Json.Serialization;

public class RegisterRequest
{
    [JsonPropertyName("firstName")]
    public string FirstName { get; set; }

    [JsonPropertyName("lastName")]
    public string LastName { get; set; }

    [JsonPropertyName("msisdn")]
    public string Msisdn { get; set; }

    [JsonPropertyName("email")]
    public string Email { get; set; }

    [JsonPropertyName("language")]
    public string Language { get; set; }

    [JsonPropertyName("permSource")]
    public int PermSource { get; set; }

    [JsonPropertyName("accountType")]
    public int AccountType { get; set; }

    [JsonPropertyName("note")]
    public string Note { get; set; }

    [JsonPropertyName("etk")]
    public Etk Etk { get; set; }

    [JsonPropertyName("kvkk")]
    public Kvkk Kvkk { get; set; }

    [JsonPropertyName("extended")]
    public Extended Extended { get; set; }

    [Json<PERSON>ropertyName("coordinate")]
    public Coordinate Coordinate { get; set; }
}

public class Etk
{
    [JsonPropertyName("msisdn")]
    public int Msisdn { get; set; }

    [JsonPropertyName("msisdnFrequencyType")]
    public int MsisdnFrequencyType { get; set; }

    [JsonPropertyName("msisdnFrequency")]
    public int MsisdnFrequency { get; set; }

    [JsonPropertyName("call")]
    public int Call { get; set; }

    [JsonPropertyName("email")]
    public int Email { get; set; }

    [JsonPropertyName("emailFrequencyType")]
    public int EmailFrequencyType { get; set; }

    [JsonPropertyName("emailFrequency")]
    public int EmailFrequency { get; set; }

    [JsonPropertyName("share")]
    public int Share { get; set; }
}

public class Kvkk
{
    [JsonPropertyName("process")]
    public int Process { get; set; }

    [JsonPropertyName("share")]
    public int Share { get; set; }

    [JsonPropertyName("international")]
    public int International { get; set; }
}

public class Extended
{
    [JsonPropertyName("loyalty")]
    public int Loyalty { get; set; }
}

public class Coordinate
{
    [JsonPropertyName("lat")]
    public double Lat { get; set; }

    [JsonPropertyName("lon")]
    public double Lon { get; set; }
}
