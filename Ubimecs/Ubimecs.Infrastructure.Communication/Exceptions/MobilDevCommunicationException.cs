using System.Net;

namespace Ubimecs.Infrastructure.Communication.Exceptions;

public class MobilDevCommunicationException: Exception
{
    public HttpStatusCode StatusCode { get; set; }
    public string ResponseContent { get; set; }

    public MobilDevCommunicationException(HttpStatusCode statusCode, string responseContent): base($"MobilDev Error: {statusCode} - {responseContent}")
    {
        StatusCode = statusCode;
        ResponseContent = responseContent;
    }
}