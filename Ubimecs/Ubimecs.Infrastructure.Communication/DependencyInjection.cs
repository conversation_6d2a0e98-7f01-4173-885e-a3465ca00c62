using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Ubimecs.Infrastructure.Communication.Configuration;
using Ubimecs.Infrastructure.Communication.Contracts;
using Ubimecs.Infrastructure.Communication.Services;

namespace Ubimecs.Infrastructure.Communication;

public static class DependencyInjection
{
    public static IServiceCollection AddCommunicationServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMemoryCache();
        services.Configure<MobilDevCommunicationSettings>(options => configuration.GetSection("MobilDevCommunicationSettings").Bind(options));
        services.AddHttpClient<IMobilDevCommunicationRequestService,MobilDevCommunicationRequestService>();
        services.AddScoped<ICommunicationService,MobilDevCommunicationService>();
        var config = new MobilDevCommunicationSettings();
        configuration.GetSection("MobilDevCommunicationSettings").Bind(config);
        services.AddHttpClient("mobildev", (serviceProvider, client) =>
        {
            client.BaseAddress = new Uri(config.BaseUrl);
        });
        return services;
    }
}