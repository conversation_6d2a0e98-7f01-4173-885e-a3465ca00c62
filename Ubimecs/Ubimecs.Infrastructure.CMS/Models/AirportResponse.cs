namespace Ubimecs.Infrastructure.CMS.Models
{
    public class AirportResponse
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string ImageUrl { get; set; }
        public int Order { get; set; }
        public string Slug { get; set; }
        public FileResponse File { get; set; }
        public List<AlternativeAirport> AlternativeAirports { get; set; }
    }

    public class AlternativeAirport
    {
        public string Code { get; set; }
        
    }
}
