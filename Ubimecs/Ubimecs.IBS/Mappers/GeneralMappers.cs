using PricePortServiceReference;
using System.Xml;
using FlightPortServiceReference;
using ReservationsPortServiceReference;
using Ubimecs.IBS.Mappers.NativeApiMappers;
using Ubimecs.IBS.Models;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Meal;
using Ubimecs.Infrastructure.Models.DTO.Payment;
using Ubimecs.Infrastructure.Models.DTO.Service;
using Ubimecs.Infrastructure.Models.Flow;
using SSRInformationType = ReservationsPortServiceReference.SSRInformationType;

namespace Ubimecs.IBS.Mappers.RequestMappers
{
    public static class GeneralMappers
    {
        public static FlightSegmentDTO GetSegmentDTO(this ListOfFlightSegmentType segmentType, LanguageEnum language,
            ListOfDisclosureTypeDisclosures[] disclosures = null)
        {
            return new FlightSegmentDTO
            {
                AirlineId = segmentType.MarketingCarrier.AirlineID.Value,
                ArrivalCode = segmentType.Arrival.AirportCode.Value,
                ArrivalDate = segmentType.Arrival.Date,
                ArrivalTime = segmentType.Arrival.Time,
                DepartureCode = segmentType.Departure.AirportCode.Value,
                DepartureDate = segmentType.Departure.Date,
                DepartureTime = segmentType.Departure.Time,
                FlightNumber = segmentType.MarketingCarrier.FlightNumber.Value,
                Id = segmentType.SegmentKey,
                JourneyTime = IbsUtility.GetTimeSpan(segmentType.FlightDetail.FlightDuration.Value),
                FormattedDepartureDate = IbsUtility.FormatDate(language, segmentType.Departure.Date),
                DepartureCity = CachedData.AirPortList.Airports
                    .FirstOrDefault(f => f.AirportCode == segmentType.Departure.AirportCode.Value)
                    ?.CityNames[(int)language],
                ArrivalCity = CachedData.AirPortList.Airports
                    .FirstOrDefault(f => f.AirportCode == segmentType.Arrival.AirportCode.Value)
                    ?.CityNames[(int)language],
                IsInternational = IbsUtility.GetIsInternationalFlight(segmentType.Departure.AirportCode.Value,
                    segmentType.Arrival.AirportCode.Value),
                CarrierName = disclosures != null
                    ? disclosures.FirstOrDefault(t => t.ListKey.Equals(segmentType.refs))?.Description
                        .FirstOrDefault(row => row.Text.Value.Contains("Carrier Name"))?.Text?.Value?.Split(':')
                        ?.LastOrDefault(t => t != "X9")
                    : (CachedData.GetCMS(language, "default_operated_by") ?? "default_operated_by")
            };
        }


        public static List<ServiceDefinitionType> GetServices(object response)
        {
            List<ServiceDefinitionType> result = new List<ServiceDefinitionType>();

            if (response is OrderViewRS orderView)
            {
                result = ((OrderViewRSResponse)orderView?.Items?.FirstOrDefault(t =>
                    t.GetType() == typeof(OrderViewRSResponse)))?.DataLists?.ServiceDefinitionList?.ToList();
            }

            if (response is OfferPriceRS offerPrice)
            {
                result = ((OfferPriceRSDataLists)offerPrice?.Items?.FirstOrDefault(t =>
                    t.GetType() == typeof(OfferPriceRSDataLists)))?.ServiceDefinitionList?.ToList();
            }

            if (response is OrderReshopRS orderReshop)
            {
                result = ((OrderReshopRSResponse)orderReshop?.Items?.FirstOrDefault(t =>
                    t.GetType() == typeof(OrderReshopRSResponse)))?.DataLists?.ServiceDefinitionList?.ToList();
            }


            return result;
        }

        public static List<BaseSeatDTO> GetSeats(object response, SessionCache session)
        {
            List<BaseSeatDTO> result = new List<BaseSeatDTO>();
            var serviceDataList = GetServices(response);

            if (response is OrderViewRS orderView)
            {
                var seatServices =
                    ((OrderViewRSResponse)orderView?.Items?.FirstOrDefault(x =>
                        x.GetType() == typeof(OrderViewRSResponse)))
                    ?.Order
                    ?.SelectMany(t => t.OrderItems.SelectMany(x => x.Service))
                    ?.Where(s => s.Item is OrderItemTypeOrderItemServiceSelectedSeat)
                    ?.ToList() ?? new List<OrderItemTypeOrderItemService>();

                foreach (var seat in seatServices)
                {
                    var seatItem = seat.Item as OrderItemTypeOrderItemServiceSelectedSeat;
                    var seatServiceDataListItem = serviceDataList?.FirstOrDefault(t =>
                        t.ServiceDefinitionID == seatItem.ServiceDefinitionRef?.ToString());

                    var seatInfo = new BaseSeatDTO
                    {
                        Id = seatItem.ServiceDefinitionRef?.ToString(),
                        Name = seatServiceDataListItem?.Name?.Value,
                        Column = seatItem.Seat?.Column,
                        Code = seatServiceDataListItem?.Encoding?.Code?.Value,
                        PassengerIds = IbsUtility.SplitIds(seat.PassengerRef),
                        DescriptionText = seatServiceDataListItem?.Descriptions?.Description?.FirstOrDefault()?.Text
                            ?.Value?.ToString(),
                        SeatAttribute = IbsUtility.GetSeatType(seatServiceDataListItem?.Encoding?.Code?.Value),
                        SeatNumber = seatItem.Seat.Row,
                        SegmentIds = IbsUtility.SplitIds(seatItem.SegmentRef?.ToString())
                    };
                    result.Add(seatInfo);
                }
            }

            if (response is OfferPriceRS offerPrice)
            {
                var seatServices =
                    ((OfferPriceRSPricedOffer)offerPrice?.Items?.FirstOrDefault(x =>
                        x.GetType() == typeof(OfferPriceRSPricedOffer)))
                    ?.OfferItem
                    ?.SelectMany(t => t.Service)
                    ?.Where(s => s.Item is OfferItemTypeServiceSelectedSeat)
                    ?.ToList() ?? new List<OfferItemTypeService>();

                foreach (var item in session.CurrentFlow.PNR.Seats)
                {
                    var itemSegment = session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments)
                        .FirstOrDefault(f => f.TmobId == item.SegmentTmobId).Id;

                    var seat = seatServices.FirstOrDefault(f =>
                        (f.Item as OfferItemTypeServiceSelectedSeat).Seat.Column == item.Column &&
                        (f.Item as OfferItemTypeServiceSelectedSeat).Seat.Row == item.Number &&
                        f.PassengerRefs == item.PassengerId &&
                        (f.Item as OfferItemTypeServiceSelectedSeat).SegmentRef.ToString() == itemSegment);

                    if (seat != null)
                    {
                        var seatItem = seat.Item as OfferItemTypeServiceSelectedSeat;
                        var seatServiceDataListItem = serviceDataList.FirstOrDefault(t =>
                            t.ServiceDefinitionID == seatItem.ServiceDefinitionRef.ToString());

                        var seatInfo = new BaseSeatDTO
                        {
                            Id = seatItem.ServiceDefinitionRef.ToString(),
                            Name = seatServiceDataListItem.Name.Value,
                            Column = seatItem.Seat.Column,
                            Code = seatServiceDataListItem.Encoding?.Code?.Value,
                            PassengerIds = IbsUtility.SplitIds(seat.PassengerRefs),
                            DescriptionText = seatServiceDataListItem.Descriptions.Description.FirstOrDefault().Text
                                .Value.ToString(),
                            SeatAttribute = IbsUtility.GetSeatType(seatServiceDataListItem.Encoding?.Code?.Value),
                            SeatNumber = seatItem.Seat.Row,
                            SegmentIds = IbsUtility.SplitIds(seatItem.SegmentRef?.ToString())
                        };
                        result.Add(seatInfo);
                    }
                    else
                    {
                        var lastSeatAvailability =
                            session.CurrentFlow?.LatestSeatResponse?.Seats?.FirstOrDefault(t =>
                                t.SeatNumber == item.Number && t.Column == item.Column);

                        var seatInfo = new BaseSeatDTO
                        {
                            Id = "",
                            Name = "",
                            Column = item.Column,
                            Code = "",
                            PassengerIds = new string[] { item.PassengerId },
                            DescriptionText = "",
                            SeatAttribute = lastSeatAvailability.SeatType,
                            SeatNumber = item.Number,
                            SegmentIds = new string[] { itemSegment }
                        };
                        result.Add(seatInfo);
                    }
                }
            }

            if (response is OrderReshopRS orderReshop)
            {
                var seatServices = orderReshop.GetOffers()
                    ?.Where(w => w.AddOfferItem != null)
                    ?.SelectMany(s => s.AddOfferItem)
                    ?.SelectMany(t => t.Service)
                    ?.Where(s => s.Item is OfferItemTypeServiceSelectedSeat)
                    ?.ToList() ?? new List<OfferItemTypeService>();
                if ((seatServices != null && seatServices.Count > 0) || session.CurrentFlow.PNR.Seats.Count > 0)
                {
                    var newSelectedSeatCount = session.CurrentFlow.PNR?.Seats?.Count;
                    var serviceChangingFlightCount = session.CurrentFlow?.PNR?
                        .Flights
                        .Where(a => a.State != OfferRebookingStateEnum.NotChanged)
                        .SelectMany(t => t.Segments)
                        .ToList().Count;
                    var serviceChangingSeatCount = serviceChangingFlightCount *
                                                   (session.CurrentFlow?.PnrInfoResponse?.PassengerList?.Count());
                    if (newSelectedSeatCount > 0 && serviceChangingFlightCount > 0 && serviceChangingSeatCount > 0 &&
                        newSelectedSeatCount == serviceChangingSeatCount)
                    {
                        foreach (var item in session.CurrentFlow.PNR.Seats)
                        {
                            var itemSegment = session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments)
                                .FirstOrDefault(f => f.TmobId == item.SegmentTmobId).Id;

                            var seat = seatServices.FirstOrDefault(f =>
                                (f.Item as OfferItemTypeServiceSelectedSeat).Seat.Column == item.Column &&
                                (f.Item as OfferItemTypeServiceSelectedSeat).Seat.Row == item.Number &&
                                f.PassengerRefs == item.PassengerId &&
                                (f.Item as OfferItemTypeServiceSelectedSeat).SegmentRef.ToString() == itemSegment);

                            if (seat != null)
                            {
                                var seatItem = seat.Item as OfferItemTypeServiceSelectedSeat;
                                var seatServiceDataListItem = serviceDataList.FirstOrDefault(t =>
                                    t.ServiceDefinitionID == seatItem.ServiceDefinitionRef.ToString());

                                var seatInfo = new BaseSeatDTO
                                {
                                    Id = seatItem.ServiceDefinitionRef.ToString(),
                                    Name = seatServiceDataListItem.Name.Value,
                                    Column = seatItem.Seat.Column,
                                    Code = seatServiceDataListItem.Encoding?.Code?.Value,
                                    PassengerIds = IbsUtility.SplitIds(seat.PassengerRefs),
                                    DescriptionText = seatServiceDataListItem.Descriptions.Description.FirstOrDefault()
                                        .Text.Value.ToString(),
                                    SeatAttribute =
                                        IbsUtility.GetSeatType(seatServiceDataListItem.Encoding?.Code?.Value),
                                    SeatNumber = seatItem.Seat.Row,
                                    SegmentIds = IbsUtility.SplitIds(seatItem.SegmentRef?.ToString())
                                };
                                result.Add(seatInfo);
                            }
                            else
                            {
                                var lastSeatAvailability =
                                    session.CurrentFlow?.LatestSeatResponse?.Seats?.FirstOrDefault(t =>
                                        t.SeatNumber == item.Number && t.Column == item.Column);

                                var seatInfo = new BaseSeatDTO
                                {
                                    Id = "",
                                    Name = "",
                                    Column = item.Column,
                                    Code = "",
                                    PassengerIds = new string[] { item.PassengerId },
                                    DescriptionText = "",
                                    SeatAttribute = lastSeatAvailability.SeatType == null
                                        ? SeatTypeEnum.Normal
                                        : lastSeatAvailability.SeatType,
                                    SeatNumber = item.Number,
                                    SegmentIds = new string[] { itemSegment }
                                };
                                result.Add(seatInfo);
                            }
                        }
                    }
                    else
                    {
                        //if (session.CurrentFlow.RebookingType != Model.Flow.RebookingTypeEnum.AddFlight &&
                        ////session.CurrentFlow.RebookingType != Model.Flow.RebookingTypeEnum.ChangeFlight &&
                        //session.CurrentFlow.RebookingType != Model.Flow.RebookingTypeEnum.OptionalPNR)
                        //{
                        var passengers = session.CurrentFlow.PNR.Passengers;
                        var flights = session.CurrentFlow.PNR.Flights;
                        foreach (var pass in passengers)
                        {
                            foreach (var flightItem in flights)
                            {
                                foreach (var segment in flightItem.Segments)
                                {
                                    var servicesInAddOfferItems = orderReshop.GetOffers()
                                        ?.Where(w => w.AddOfferItem != null)
                                        ?.SelectMany(s => s.AddOfferItem)
                                        ?.SelectMany(t => t.Service)
                                        ?.Where(s => s.Item is OfferItemTypeServiceServiceDefinitionRef
                                                     && s.PassengerRefs.Contains(pass.Id)
                                                     && ((OfferItemTypeServiceServiceDefinitionRef)s.Item)?.SegmentRefs
                                                     ?.Contains(segment.Id) == true)
                                        ?.Select(k => ((OfferItemTypeServiceServiceDefinitionRef)k.Item).Value)
                                        .ToList() ?? new List<string>();
                                    if (servicesInAddOfferItems != null && servicesInAddOfferItems.Count > 0)
                                    {
                                        var serviceInDataList = orderReshop.GetDataList()?
                                            .ServiceDefinitionList
                                            .FirstOrDefault(t =>
                                                servicesInAddOfferItems.Any(k => k == t.ServiceDefinitionID)
                                                && Ubimecs.Infrastructure.Utilities.Statics.SeatTypes.Contains(
                                                    t.Encoding.Code.Value));

                                        if (!string.IsNullOrEmpty(serviceInDataList?.Encoding?.Code?.Value)
                                            && serviceInDataList?.Encoding?.Code?.Value is var encodingCode
                                            && Ubimecs.Infrastructure.Utilities.Statics.SeatTypes
                                                .Contains(encodingCode))
                                        {
                                            var selectedSeat = session.CurrentFlow.PNR.Seats.FirstOrDefault(t =>
                                                t.SegmentTmobId == segment.TmobId && t.PassengerId == pass.Id);
                                            if (selectedSeat != null)
                                            {
                                                var totalAmountOfTheSeat = orderReshop?.GetOffers()?
                                                    .Where(w => w.AddOfferItem != null)?
                                                    .SelectMany(s => s.AddOfferItem)?
                                                    .FirstOrDefault(t =>
                                                        ((OfferItemTypeServiceServiceDefinitionRef)t.Service
                                                            .FirstOrDefault().Item).Value ==
                                                        serviceInDataList.ServiceDefinitionID)
                                                    .TotalPriceDetail
                                                    .TotalAmount;
                                                var totalPriceCurrency =
                                                    ((CurrencyAmountOptType)
                                                        ((DetailCurrencyPriceType)totalAmountOfTheSeat.Item).Item).Code;
                                                var totalPriceAmount =
                                                    ((CurrencyAmountOptType)
                                                        ((DetailCurrencyPriceType)totalAmountOfTheSeat.Item).Item)
                                                    .Value;

                                                var newSeatItem = new BaseSeatDTO()
                                                {
                                                    Code = encodingCode,
                                                    Column = selectedSeat.Column,
                                                    SeatNumber = selectedSeat.Number,
                                                    PassengerIds = new string[] { pass.Id },
                                                    SeatAttribute =
                                                        Ubimecs.Infrastructure.Utilities.Statics.StandartSeatTypes
                                                            .Contains(encodingCode)
                                                            ? SeatTypeEnum.Normal
                                                            : SeatTypeEnum.Xleg,
                                                    Id = serviceInDataList.ServiceDefinitionID,
                                                    Count = 1,
                                                    DescriptionText = serviceInDataList.Descriptions.Description
                                                        .FirstOrDefault().Text.Value.ToString(),
                                                    Name = serviceInDataList.Name.Value.ToString(),
                                                    SegmentIds = new string[] { segment.Id },
                                                    TotalPrice = totalPriceAmount,
                                                    Currency = totalPriceCurrency
                                                };
                                                result.Add(newSeatItem);
                                            }
                                            else
                                            {
                                                var segmentIds = segment.Id;
                                                var alreadyBoughtSeats = session.CurrentFlow
                                                    .PnrInfoResponse?
                                                    .Services?
                                                    .Seats?
                                                    .Where(t => t.SegmentIds.Contains(segmentIds)
                                                                && t.PassengerIds.Contains(pass.Id))
                                                    .ToList();
                                                if (alreadyBoughtSeats != null && alreadyBoughtSeats.Count > 0)
                                                {
                                                    result.AddRange(alreadyBoughtSeats);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            var alreadyBoughtSeats = session.CurrentFlow
                                                .PnrInfoResponse?
                                                .Services?
                                                .Seats.Where(t => t.PassengerIds.Contains(pass.Id)
                                                                  && t.SegmentIds.Contains(segment.Id)).ToList();

                                            if (alreadyBoughtSeats != null && alreadyBoughtSeats.Count > 0)
                                            {
                                                result.AddRange(alreadyBoughtSeats);
                                            }
                                        }
                                    }
                                    else if (session.CurrentFlow.RebookingType ==
                                             Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ||
                                             session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow
                                                 .RebookingTypeEnum.ChangeFlight ||
                                             session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow
                                                 .RebookingTypeEnum.OptionalPNR)
                                    {
                                        var item = session.CurrentFlow.PNR.Seats.FirstOrDefault(t =>
                                            t.PassengerId == pass.Id && t.SegmentTmobId == segment.TmobId);
                                        if (item != null && !string.IsNullOrEmpty(item.SegmentTmobId))
                                        {
                                            var itemSegment =
                                                session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments)
                                                    .FirstOrDefault(f => f.TmobId == item.SegmentTmobId)?.Id ??
                                                string.Empty;

                                            var seat = seatServices.FirstOrDefault(f =>
                                                (f.Item as OfferItemTypeServiceSelectedSeat).Seat.Column ==
                                                item.Column &&
                                                (f.Item as OfferItemTypeServiceSelectedSeat).Seat.Row == item.Number &&
                                                f.PassengerRefs == item.PassengerId &&
                                                (f.Item as OfferItemTypeServiceSelectedSeat).SegmentRef.ToString() ==
                                                itemSegment);

                                            if (seat != null)
                                            {
                                                var seatItem = seat.Item as OfferItemTypeServiceSelectedSeat;
                                                var seatServiceDataListItem = serviceDataList.FirstOrDefault(t =>
                                                    t.ServiceDefinitionID == seatItem.ServiceDefinitionRef.ToString());

                                                var seatInfo = new BaseSeatDTO
                                                {
                                                    Id = seatItem.ServiceDefinitionRef.ToString(),
                                                    Name = seatServiceDataListItem.Name.Value,
                                                    Column = seatItem.Seat.Column,
                                                    Code = seatServiceDataListItem.Encoding?.Code?.Value,
                                                    PassengerIds = IbsUtility.SplitIds(seat.PassengerRefs),
                                                    DescriptionText = seatServiceDataListItem.Descriptions.Description
                                                        .FirstOrDefault().Text.Value.ToString(),
                                                    SeatAttribute =
                                                        IbsUtility.GetSeatType(seatServiceDataListItem.Encoding?.Code
                                                            ?.Value),
                                                    SeatNumber = seatItem.Seat.Row,
                                                    SegmentIds = IbsUtility.SplitIds(seatItem.SegmentRef?.ToString())
                                                };
                                                result.Add(seatInfo);
                                            }
                                            else
                                            {
                                                var lastSeatAvailability =
                                                    session.CurrentFlow?.LatestSeatResponse?.Seats?.FirstOrDefault(t =>
                                                        t.SeatNumber == item.Number && t.Column == item.Column);

                                                var seatInfo = new BaseSeatDTO
                                                {
                                                    Id = "",
                                                    Name = "",
                                                    Column = item.Column,
                                                    Code = "",
                                                    PassengerIds = new string[] { item.PassengerId },
                                                    DescriptionText = "",
                                                    SeatAttribute = lastSeatAvailability.SeatType == null
                                                        ? SeatTypeEnum.Normal
                                                        : lastSeatAvailability.SeatType,
                                                    SeatNumber = item.Number,
                                                    SegmentIds = new string[] { itemSegment }
                                                };
                                                result.Add(seatInfo);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        var alreadyBoughtSeats = session.CurrentFlow
                                            .PnrInfoResponse?
                                            .Services?
                                            .Seats.Where(t => t.PassengerIds.Contains(pass.Id)
                                                              && t.SegmentIds.Contains(segment.Id)).ToList();

                                        if (alreadyBoughtSeats != null && alreadyBoughtSeats.Count > 0)
                                        {
                                            result.AddRange(alreadyBoughtSeats);
                                        }
                                    }
                                }
                            }
                        }
                        //}
                    }
                }
                else
                {
                    if (session.CurrentFlow.RebookingType !=
                        Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight &&
                        session.CurrentFlow.RebookingType !=
                        Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight &&
                        session.CurrentFlow.RebookingType !=
                        Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.OptionalPNR)
                    {
                        var passengers = session.CurrentFlow.PNR.Passengers;
                        var flights = session.CurrentFlow.PNR.Flights;
                        foreach (var pass in passengers)
                        {
                            foreach (var flightItem in flights)
                            {
                                foreach (var segment in flightItem.Segments)
                                {
                                    var servicesInAddOfferItems = orderReshop.GetOffers()
                                        ?.Where(w => w.AddOfferItem != null)
                                        ?.SelectMany(s => s.AddOfferItem)
                                        ?.SelectMany(t => t.Service)
                                        ?.Where(s => s.Item is OfferItemTypeServiceServiceDefinitionRef
                                                     && s.PassengerRefs.Contains(pass.Id)
                                                     && ((OfferItemTypeServiceServiceDefinitionRef)s.Item)?.SegmentRefs
                                                     ?.Contains(segment.Id) == true)
                                        ?.Select(k => ((OfferItemTypeServiceServiceDefinitionRef)k.Item).Value)
                                        .ToList() ?? new List<string>();
                                    if (servicesInAddOfferItems != null && servicesInAddOfferItems.Count > 0)
                                    {
                                        var serviceInDataList = orderReshop.GetDataList()?
                                            .ServiceDefinitionList
                                            .FirstOrDefault(t =>
                                                servicesInAddOfferItems.Any(k => k == t.ServiceDefinitionID)
                                                && Ubimecs.Infrastructure.Utilities.Statics.SeatTypes.Contains(
                                                    t.Encoding.Code.Value));

                                        if (!string.IsNullOrEmpty(serviceInDataList?.Encoding?.Code?.Value)
                                            && serviceInDataList?.Encoding?.Code?.Value is var encodingCode
                                            && Ubimecs.Infrastructure.Utilities.Statics.SeatTypes
                                                .Contains(encodingCode))
                                        {
                                            var selectedSeat = session.CurrentFlow.PNR.Seats.FirstOrDefault(t =>
                                                t.SegmentTmobId == segment.TmobId && t.PassengerId == pass.Id);
                                            if (selectedSeat != null)
                                            {
                                                var totalAmountOfTheSeat = orderReshop?.GetOffers()?
                                                    .Where(w => w.AddOfferItem != null)?
                                                    .SelectMany(s => s.AddOfferItem)?
                                                    .FirstOrDefault(t =>
                                                        ((OfferItemTypeServiceServiceDefinitionRef)t.Service
                                                            .FirstOrDefault().Item).Value ==
                                                        serviceInDataList.ServiceDefinitionID)
                                                    .TotalPriceDetail
                                                    .TotalAmount;
                                                var totalPriceCurrency =
                                                    ((CurrencyAmountOptType)
                                                        ((DetailCurrencyPriceType)totalAmountOfTheSeat.Item).Item).Code;
                                                var totalPriceAmount =
                                                    ((CurrencyAmountOptType)
                                                        ((DetailCurrencyPriceType)totalAmountOfTheSeat.Item).Item)
                                                    .Value;

                                                var newSeatItem = new BaseSeatDTO()
                                                {
                                                    Code = encodingCode,
                                                    Column = selectedSeat.Column,
                                                    SeatNumber = selectedSeat.Number,
                                                    PassengerIds = new string[] { pass.Id },
                                                    SeatAttribute =
                                                        Ubimecs.Infrastructure.Utilities.Statics.StandartSeatTypes
                                                            .Contains(encodingCode)
                                                            ? SeatTypeEnum.Normal
                                                            : SeatTypeEnum.Xleg,
                                                    Id = serviceInDataList.ServiceDefinitionID,
                                                    Count = 1,
                                                    DescriptionText = serviceInDataList.Descriptions.Description
                                                        .FirstOrDefault().Text.Value.ToString(),
                                                    Name = serviceInDataList.Name.Value.ToString(),
                                                    SegmentIds = new string[] { segment.Id },
                                                    TotalPrice = totalPriceAmount,
                                                    Currency = totalPriceCurrency
                                                };
                                                result.Add(newSeatItem);
                                            }
                                            else
                                            {
                                                var segmentIds = segment.Id;
                                                var alreadyBoughtSeats = session.CurrentFlow
                                                    .PnrInfoResponse?
                                                    .Services?
                                                    .Seats?
                                                    .Where(t => t.SegmentIds.Contains(segmentIds)
                                                                && t.PassengerIds.Contains(pass.Id))
                                                    .ToList();
                                                if (alreadyBoughtSeats != null && alreadyBoughtSeats.Count > 0)
                                                {
                                                    result.AddRange(alreadyBoughtSeats);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            var alreadyBoughtSeats = session.CurrentFlow
                                                .PnrInfoResponse?
                                                .Services?
                                                .Seats.Where(t => t.PassengerIds.Contains(pass.Id)
                                                                  && t.SegmentIds.Contains(segment.Id)).ToList();

                                            if (alreadyBoughtSeats != null && alreadyBoughtSeats.Count > 0)
                                            {
                                                result.AddRange(alreadyBoughtSeats);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        public static List<BaseServiceDTO> GetServiceDTOs(object response, ServiceCategoryEnum serviceCategory,
            Dictionary<string, ServiceCategoryEnum> serviceCodeCategories, SessionCache session)
        {
            var services = new List<BaseServiceDTO>();
            var serviceDatalist = GetServices(response);

            if (serviceDatalist != null)
            {
                if (response is OrderViewRS orderView)
                {
                    var orders = orderView.GetOrders();

                    foreach (var serviceItem in serviceDatalist)
                    {
                        if (IbsUtility.GetCategory(serviceCodeCategories, serviceItem.Encoding.Code.Value.ToString()) ==
                            serviceCategory)
                        {
                            var serviceOrders = orders.Where(f =>
                                (f.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef)
                                ?.Value == serviceItem.ServiceDefinitionID).ToList();

                            BaseServiceDTO service = null;

                            if (serviceOrders?.Count > 0)
                            {
                                foreach (var item in serviceOrders.GroupBy(g => new
                                         {
                                             pass = g.Service?.FirstOrDefault()?.PassengerRef,
                                             segment =
                                                 (g.Service?.FirstOrDefault()?.Item as
                                                     OrderItemTypeOrderItemServiceServiceDefinitionRef)?.SegmentRef
                                         }))
                                {
                                    service = new BaseServiceDTO
                                    {
                                        Id = serviceItem.ServiceDefinitionID,
                                        PassengerIds = IbsUtility.SplitIds(item.Key.pass),
                                        SegmentIds = IbsUtility.SplitIds(item.Key.segment),
                                        Code = serviceItem.Encoding?.Code?.Value,
                                        DescriptionText = serviceItem.Descriptions?.Description?.FirstOrDefault()?.Text
                                            ?.Value?.ToString(),
                                        Name = CachedData.GetCMS(session.Language, serviceItem.Encoding?.Code?.Value) ==
                                               null
                                               || CachedData.GetCMS(session.Language,
                                                   serviceItem.Encoding?.Code?.Value) == "NO_CMS"
                                            ? serviceItem.Name?.Value?.ToString()
                                            : CachedData.GetCMS(session.Language, serviceItem.Encoding?.Code?.Value),
                                        TotalPrice = item.Sum(s =>
                                            ((s.PriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as
                                                CurrencyAmountOptType)?.Value ?? 0),
                                        Currency =
                                            ((item.FirstOrDefault()?.PriceDetail?.TotalAmount?.Item as
                                                DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Code,
                                        Count = item.Count()
                                    };

                                    services.Add(service);
                                }
                            }
                            else
                            {
                                var parentServices = serviceDatalist
                                    .Where(w => w.Item is ServiceDefinitionTypeServiceBundle)
                                    .Where(f => (f.Item as ServiceDefinitionTypeServiceBundle).ServiceDefinitionRef.Any(
                                        a => a.Value == serviceItem.ServiceDefinitionID))
                                    .ToList();

                                if (parentServices?.Count > 0)
                                {
                                    foreach (var parentService in parentServices)
                                    {
                                        var parentOffer = orders.FirstOrDefault(x =>
                                            parentService.ServiceDefinitionID ==
                                            (x?.Service?.FirstOrDefault()?.Item as
                                                OrderItemTypeOrderItemServiceServiceDefinitionRef)?.Value);
                                        var encodingCode = serviceItem.BookingInstructions?.SSRCode?.FirstOrDefault() ??
                                                           serviceItem.Encoding?.Code?.Value?.ToString();

                                        service = new BaseServiceDTO
                                        {
                                            Id = serviceItem.ServiceDefinitionID,
                                            PassengerIds =
                                                IbsUtility.SplitIds(parentOffer.Service.FirstOrDefault()?.PassengerRef),
                                            SegmentIds = IbsUtility.SplitIds(
                                                (parentOffer.Service?.FirstOrDefault()?.Item as
                                                    OrderItemTypeOrderItemServiceServiceDefinitionRef)?.SegmentRef),
                                            Code = encodingCode,
                                            DescriptionText = serviceItem.Descriptions?.Description?.FirstOrDefault()
                                                ?.Text?.Value?.ToString(),
                                            Name = CachedData.GetCMS(session.Language, encodingCode) == null
                                                   || CachedData.GetCMS(session.Language, encodingCode) == "NO_CMS"
                                                ? serviceItem.Name?.Value?.ToString()
                                                : CachedData.GetCMS(session.Language, encodingCode),
                                            TotalPrice = 0,
                                            Currency = ""
                                        };

                                        if (service.Code != null)
                                        {
                                            services.Add(service);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (response is OfferPriceRS offerPrice)
                {
                    var offers = offerPrice.GetOffers();

                    foreach (var serviceItem in serviceDatalist)
                    {
                        BaseServiceDTO service = null;
                        var serviceOffer = offers.Where(x =>
                                serviceItem.ServiceDefinitionID ==
                                (x.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.Value)
                            .ToList();

                        if (IbsUtility.GetCategory(serviceCodeCategories, serviceItem.Encoding.Code.Value.ToString()) ==
                            serviceCategory)
                        {
                            if (serviceOffer?.Count > 0)
                            {
                                foreach (var item in serviceOffer.GroupBy(g => new
                                         {
                                             pass = g.Service.FirstOrDefault().PassengerRefs,
                                             segment =
                                                 (g.Service?.FirstOrDefault()?.Item as
                                                     OfferItemTypeServiceServiceDefinitionRef).SegmentRefs
                                         }))
                                {
                                    service = new BaseServiceDTO
                                    {
                                        Id = serviceItem.ServiceDefinitionID,
                                        PassengerIds = IbsUtility.SplitIds(item.Key.pass),
                                        SegmentIds = IbsUtility.SplitIds(item.Key.segment),
                                        Code = serviceItem.Encoding?.Code?.Value,
                                        DescriptionText = serviceItem.Descriptions?.Description?.FirstOrDefault()?.Text
                                            ?.Value?.ToString(),
                                        Name = CachedData.GetCMS(session.Language, serviceItem.Encoding?.Code?.Value) ==
                                               null
                                               || CachedData.GetCMS(session.Language,
                                                   serviceItem.Encoding?.Code?.Value) == "NO_CMS"
                                            ? serviceItem.Name?.Value?.ToString()
                                            : CachedData.GetCMS(session.Language, serviceItem.Encoding?.Code?.Value),
                                        TotalPrice = item.Sum(s =>
                                            ((s.TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as
                                                CurrencyAmountOptType)?.Value ?? 0),
                                        Currency =
                                            ((item.FirstOrDefault()?.TotalPriceDetail?.TotalAmount?.Item as
                                                DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Code,
                                        Count = item.Count()
                                    };

                                    services.Add(service);
                                }
                            }
                            else
                            {
                                var parentServices = serviceDatalist
                                    .Where(w => w.Item is ServiceDefinitionTypeServiceBundle)
                                    .Where(f => (f.Item as ServiceDefinitionTypeServiceBundle).ServiceDefinitionRef.Any(
                                        a => a.Value == serviceItem.ServiceDefinitionID))
                                    .ToList();

                                if (parentServices?.Count > 0)
                                {
                                    foreach (var parentService in parentServices)
                                    {
                                        var parentOffer = offers.FirstOrDefault(x =>
                                            parentService.ServiceDefinitionID ==
                                            (x.Service?.FirstOrDefault()?.Item as
                                                OfferItemTypeServiceServiceDefinitionRef)?.Value);
                                        var encodingCode = serviceItem.BookingInstructions?.SSRCode?.FirstOrDefault() ??
                                                           serviceItem.Encoding?.Code?.Value?.ToString();

                                        service = new BaseServiceDTO
                                        {
                                            Id = serviceItem.ServiceDefinitionID,
                                            PassengerIds =
                                                IbsUtility.SplitIds(parentOffer.Service.FirstOrDefault()
                                                    ?.PassengerRefs),
                                            SegmentIds = IbsUtility.SplitIds(
                                                (parentOffer.Service?.FirstOrDefault()?.Item as
                                                    OfferItemTypeServiceServiceDefinitionRef).SegmentRefs),
                                            Code = encodingCode,
                                            DescriptionText = serviceItem.Descriptions?.Description?.FirstOrDefault()
                                                ?.Text?.Value?.ToString(),
                                            Name = CachedData.GetCMS(session.Language, encodingCode) == null
                                                   || CachedData.GetCMS(session.Language, encodingCode) == "NO_CMS"
                                                ? serviceItem.Name?.Value?.ToString()
                                                : CachedData.GetCMS(session.Language, encodingCode),
                                            TotalPrice = 0,
                                            Currency = ""
                                        };

                                        if (service.Code != null)
                                        {
                                            services.Add(service);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (response is OrderReshopRS orderReshop)
                {
                    var offers = orderReshop.GetOffers().Where(w => w.AddOfferItem != null)
                        .SelectMany(s => s.AddOfferItem);

                    foreach (var serviceItem in serviceDatalist)
                    {
                        BaseServiceDTO service = null;
                        var serviceOffer = offers.Where(x =>
                                serviceItem.ServiceDefinitionID ==
                                (x.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.Value)
                            .ToList();

                        if (IbsUtility.GetCategory(serviceCodeCategories, serviceItem.Encoding.Code.Value.ToString()) ==
                            serviceCategory)
                        {
                            if (serviceOffer?.Count > 0)
                            {
                                foreach (var item in serviceOffer.GroupBy(g => new
                                         {
                                             pass = g.Service.FirstOrDefault().PassengerRefs,
                                             segment =
                                                 (g.Service?.FirstOrDefault()?.Item as
                                                     OfferItemTypeServiceServiceDefinitionRef).SegmentRefs
                                         }))
                                {
                                    service = new BaseServiceDTO
                                    {
                                        Id = serviceItem.ServiceDefinitionID,
                                        PassengerIds = IbsUtility.SplitIds(item.Key.pass),
                                        SegmentIds = IbsUtility.SplitIds(item.Key.segment),
                                        Code = serviceItem.Encoding?.Code?.Value,
                                        DescriptionText = serviceItem.Descriptions?.Description?.FirstOrDefault()?.Text
                                            ?.Value?.ToString(),
                                        Name = CachedData.GetCMS(session.Language, serviceItem.Encoding?.Code?.Value) ==
                                               null
                                               || CachedData.GetCMS(session.Language,
                                                   serviceItem.Encoding?.Code?.Value) == "NO_CMS"
                                            ? serviceItem.Name?.Value?.ToString()
                                            : CachedData.GetCMS(session.Language, serviceItem.Encoding?.Code?.Value),
                                        TotalPrice = item.Sum(s =>
                                            ((s.TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as
                                                CurrencyAmountOptType)?.Value ?? 0),
                                        Currency =
                                            ((item.FirstOrDefault()?.TotalPriceDetail?.TotalAmount?.Item as
                                                DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Code,
                                        Count = item.Count()
                                    };

                                    services.Add(service);
                                    if (session.CurrentFlow.RebookingType ==
                                        Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras
                                        && serviceCategory == ServiceCategoryEnum.MEALS
                                        && session.CurrentFlow.PNR.Flights
                                            .FirstOrDefault(k => k.State == OfferRebookingStateEnum.ServiceChanging)
                                            ?.SelectedBundle == ServiceCategoryEnum.SUN_CLASSIC_BUNDLE)
                                    {
                                        var addExtrasFlight = session.CurrentFlow.PNR.Flights.FirstOrDefault(k =>
                                            k.State == OfferRebookingStateEnum.ServiceChanging);
                                        if (addExtrasFlight != null)
                                        {
                                            var addExtrasFlightSegmentIds =
                                                addExtrasFlight.Segments.Select(t => t.Id).ToList();
                                            var currentFlightIncludedMealEncoingCode = session.CurrentFlow
                                                .PnrInfoResponse
                                                .Services
                                                .Meals
                                                .FirstOrDefault(t =>
                                                    t.SegmentIds.Any(k => addExtrasFlightSegmentIds.Contains(k)))?.Code;
                                            if (!string.IsNullOrEmpty(currentFlightIncludedMealEncoingCode))
                                            {
                                                var includedMeal = session.CurrentFlow
                                                    .Services
                                                    .Services[ServiceCategoryEnum.MEALS]
                                                    .Select(t =>
                                                        Ubimecs.Infrastructure.Utilities.Utility
                                                            .DeserializeObject<BaseServiceDTO>(t))?
                                                    .FirstOrDefault(k => k.Code == currentFlightIncludedMealEncoingCode
                                                                         && k.PassengerIds.Contains(item.Key.pass));
                                                if (includedMeal != null)
                                                {
                                                    var includedMealService = new BaseServiceDTO()
                                                    {
                                                        PassengerIds = IbsUtility.SplitIds(item.Key.pass),
                                                        SegmentIds = IbsUtility.SplitIds(item.Key.segment),
                                                        Id = includedMeal.Id,
                                                        Code = includedMeal.Code,
                                                        Count = 1,
                                                        Currency = ((item.FirstOrDefault()?.TotalPriceDetail
                                                                ?.TotalAmount?.Item as DetailCurrencyPriceType)
                                                            ?.Item as CurrencyAmountOptType)?.Code,
                                                        DescriptionText = includedMeal.DescriptionText,
                                                        Name = includedMeal.Name,
                                                        TotalPrice = includedMeal.TotalPrice
                                                    };
                                                    services.Add(includedMealService);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                var parentServices = serviceDatalist
                                    .Where(w => w.Item is ServiceDefinitionTypeServiceBundle)
                                    .Where(f => (f.Item as ServiceDefinitionTypeServiceBundle).ServiceDefinitionRef.Any(
                                        a => a.Value == serviceItem.ServiceDefinitionID))
                                    .ToList();

                                if (parentServices?.Count > 0)
                                {
                                    foreach (var parentService in parentServices)
                                    {
                                        var parentOffer = offers.FirstOrDefault(x =>
                                            parentService.ServiceDefinitionID ==
                                            (x.Service?.FirstOrDefault()?.Item as
                                                OfferItemTypeServiceServiceDefinitionRef)?.Value);
                                        var encodingCode = serviceItem.BookingInstructions?.SSRCode?.FirstOrDefault() ??
                                                           serviceItem.Encoding?.Code?.Value?.ToString();

                                        service = new BaseServiceDTO
                                        {
                                            Id = serviceItem.ServiceDefinitionID,
                                            PassengerIds =
                                                IbsUtility.SplitIds(parentOffer.Service.FirstOrDefault()
                                                    ?.PassengerRefs),
                                            SegmentIds = IbsUtility.SplitIds(
                                                (parentOffer.Service?.FirstOrDefault()?.Item as
                                                    OfferItemTypeServiceServiceDefinitionRef).SegmentRefs),
                                            Code = encodingCode,
                                            DescriptionText = serviceItem.Descriptions?.Description?.FirstOrDefault()
                                                ?.Text?.Value?.ToString(),
                                            Name = CachedData.GetCMS(session.Language, encodingCode) == null
                                                   || CachedData.GetCMS(session.Language, encodingCode) == "NO_CMS"
                                                ? serviceItem.Name?.Value?.ToString()
                                                : CachedData.GetCMS(session.Language, encodingCode),
                                            TotalPrice = 0,
                                            Currency = ""
                                        };

                                        if (service.Code != null)
                                        {
                                            services.Add(service);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return services;
        }

        public static List<BaseBundleDTO> GetBundles(object response,
            Dictionary<string, ServiceCategoryEnum> serviceCodeCategories, SessionCache session)
        {
            List<BaseServiceDTO> baseServices = new List<BaseServiceDTO>();
            List<BaseBundleDTO> result = new List<BaseBundleDTO>();

            foreach (var item in IbsUtility.BundleCategories)
            {
                baseServices.AddRange(GetServiceDTOs(response, item, serviceCodeCategories, session));
            }

            foreach (var item in baseServices)
            {
                var tempItem = new BaseBundleDTO(item);
                tempItem.CategoryId = (int)IbsUtility.GetCategory(serviceCodeCategories, tempItem.Code);
                result.Add(tempItem);
            }

            return result;
        }

        private static List<BaseBaggageDTO> GetBaggages(object response, SessionCache session,
            Dictionary<string, ServiceCategoryEnum> serviceCodeCategories)
        {
            List<BaseBaggageDTO> baggages = new List<BaseBaggageDTO>();
            var serviceDataList = GetServices(response);

            if (response is OrderViewRS orderView)
            {
                var serviceOrderList = orderView.GetOrderViewOrders();
                var baggageAllowenceList = orderView.GetBaggageAllowenceList();
                foreach (var item in baggageAllowenceList)
                {
                    var baggage = new BaseBaggageDTO();

                    try
                    {
                        if (item.WeightAllowance?.refs != null &&
                            item.WeightAllowance?.ApplicableParty?.Contains("PAX") == true)
                        {
                            var _baggage = baggages.FirstOrDefault(t =>
                                t.SegmentIds.Contains(item.WeightAllowance.refs) &&
                                t.PassengerIds.Contains(item.WeightAllowance.ApplicableParty));
                            if (_baggage != null)
                            {
                                _baggage.MaximumWeight += decimal.Parse(item.WeightAllowance.MaximumWeight
                                    .FirstOrDefault().Value.ToString());
                            }
                            else
                            {
                                baggage.MaximumWeight = decimal.Parse(item.WeightAllowance.MaximumWeight
                                    .FirstOrDefault().Value.ToString());
                                baggage.PassengerIds = IbsUtility.SplitIds(item.WeightAllowance.ApplicableParty);
                                baggage.SegmentIds = IbsUtility.SplitIds(item.WeightAllowance.refs);
                                baggages.Add(baggage);
                            }
                        }
                        else
                        {
                            var ssr = serviceDataList.FirstOrDefault(t =>
                                t.Item?.ToString() == item.BaggageAllowanceID);
                            if (ssr != null)
                            {
                                var orderServiceItem = serviceOrderList.FirstOrDefault(t =>
                                    ((OrderItemTypeOrderItemServiceServiceDefinitionRef)t.Item)?.Value ==
                                    ssr.ServiceDefinitionID.ToString());
                                if (orderServiceItem != null)
                                {
                                    var _baggage = baggages.FirstOrDefault(t =>
                                        t.PassengerIds == IbsUtility.SplitIds(orderServiceItem.PassengerRef) &&
                                        t.SegmentIds == IbsUtility.SplitIds(orderServiceItem.ServiceRef));
                                    if (_baggage != null)
                                    {
                                        _baggage.Id = ssr.ServiceDefinitionID.ToString();
                                        _baggage.MaximumWeight +=
                                            Decimal.Parse(item.WeightAllowance.MaximumWeight.ToString());
                                        _baggage.ExtraBaggage +=
                                            Decimal.Parse(item.WeightAllowance.MaximumWeight.ToString());
                                        _baggage.DescriptionText = (ssr.Descriptions.Description.FirstOrDefault().Text)
                                            .Value.ToString();
                                        _baggage.Name = ssr.Name.Value.ToString();
                                    }
                                    else
                                    {
                                        baggage.Id = ssr.ServiceDefinitionID.ToString();
                                        if (item.WeightAllowance != null)
                                        {
                                            baggage.MaximumWeight =
                                                Decimal.Parse((item?.WeightAllowance?.MaximumWeight)[0]?.Value
                                                    .ToString());
                                            baggage.ExtraBaggage +=
                                                Decimal.Parse((item?.WeightAllowance?.MaximumWeight)[0]?.Value
                                                    .ToString());
                                        }

                                        baggage.SegmentIds =
                                            ((OrderItemTypeOrderItemServiceServiceDefinitionRef)orderServiceItem.Item)
                                            .SegmentRef.Split(' ').ToArray();
                                        baggage.PassengerIds = orderServiceItem.PassengerRef.Split(' ').ToArray();
                                        baggage.DescriptionText =
                                            ((DescriptionTypeText)ssr.Descriptions.Description.FirstOrDefault().Text)
                                            .Value.ToString();
                                        baggage.Name = ssr.Name.Value.ToString();
                                        baggages.Add(baggage);
                                    }
                                }
                            }
                        }
                    }
                    catch
                    {
                    }
                }

                return baggages;
            }
            else if (response is OfferPriceRS offerPrice)
            {
                var serviceOrderList = offerPrice.GetOffers();
                var baggageAllowenceList = offerPrice.GetBaggageAllowenceList();
                var passengers = offerPrice.GetPassengers();

                foreach (var pass in session.CurrentFlow.PNR.Passengers)
                {
                    foreach (var flight in session.CurrentFlow.PNR.Flights)
                    {
                        var baggage = new BaseBaggageDTO();

                        baggage.PassengerIds = new string[] { pass.Id };
                        baggage.SegmentIds = flight.Segments.Select(s => s.Id).ToArray();

                        try
                        {
                            var baggageServices = GetServiceDTOs(response, ServiceCategoryEnum.BAGGAGE,
                                    serviceCodeCategories, session)
                                .Where(w => w.PassengerIds.Contains(pass.Id) &&
                                            w.SegmentIds.Contains(flight.Segments.FirstOrDefault().Id))
                                .ToList();

                            var freeWeight = 0;

                            if (baggageServices.Count > 0)
                            {
                                foreach (var item in baggageServices)
                                {
                                    if (item.TotalPrice > 0)
                                    {
                                        var baggageSSR = item.Id;
                                        var chargeResponse =
                                            session.CurrentFlow.GetIbsData<BaggageChargesRS>(
                                                IbsDataTypeEnum.BaggageCharge.ToString() + "_" + flight.TmobId);

                                        if (chargeResponse != null)
                                        {
                                            var chargeResponseService = chargeResponse.GetServices()
                                                .FirstOrDefault(f => f.ServiceID.Value == baggageSSR);
                                            var weight = chargeResponse.GetGetCheckedBags()
                                                .FirstOrDefault(f => f.refs == chargeResponseService.ObjectKey)
                                                ?.WeightAllowance?.MaximumWeight?.FirstOrDefault()?.Value ?? 0;
                                            baggage.MaximumWeight += weight;
                                        }

                                        baggage.TotalPrice += baggageServices.FirstOrDefault().TotalPrice;
                                    }
                                    else
                                    {
                                        freeWeight += int.TryParse(item.Code.Replace("KG", ""), out int w) ? w : 0;
                                    }
                                }
                            }

                            if (freeWeight == 0)
                            {
                                var freeWeightItem = baggageAllowenceList
                                    .FirstOrDefault(f =>
                                        //Bu şekilde bir kontrol yapmamızın sebebi, IBS response'larında bazen WeightAllowance.refs değerinde aynı segment ID değerinin tekrarlanması.
                                        //Örnek olarak refs="V1_SEG.1633474934000 V1_SEG.1633474934000" şeklinde response alabiliyoruz, bu yüzden direkt equal kontrolü yapmak riskli.
                                        IbsUtility.CheckIdsEqual(
                                            IbsUtility.SplitIds(f.WeightAllowance.refs).Distinct().ToArray(),
                                            baggage.SegmentIds) &&
                                        f.WeightAllowance?.ApplicableParty == pass.PassengerType.ToString());

                                baggage.MaximumWeight +=
                                    freeWeightItem?.WeightAllowance?.MaximumWeight?.Sum(s => s?.Value ?? 0) ?? 0;
                            }
                            else
                            {
                                baggage.MaximumWeight += freeWeight;
                            }
                        }
                        catch
                        {
                        }

                        baggages.Add(baggage);
                    }
                }
            }
            else if (response is OrderReshopRS orderReshop)
            {
                var serviceOrderList = orderReshop.GetOffers();
                var baggageAllowenceList = orderReshop.GetBaggageAllowenceList();
                var passengers = orderReshop.GetPassengers();

                foreach (var pass in session.CurrentFlow.PNR.Passengers)
                {
                    foreach (var flight in session.CurrentFlow.PNR.Flights)
                    {
                        var baggage = new BaseBaggageDTO();

                        baggage.PassengerIds = new string[] { pass.Id };
                        baggage.SegmentIds = flight.Segments.Select(s => s.Id).ToArray();
                        baggage.Currency = session.CurrentFlow.PNR.Currency;
                        var addedWeight = session.CurrentFlow.PNR.ExtraBaggages
                            .FirstOrDefault(t => t.PassengerId == pass.Id && t.TmobId == flight.TmobId)?.ExtraWeight;
                        if (addedWeight != null)
                        {
                            baggage.MaximumWeight = (decimal)addedWeight;
                            if (flight.State == OfferRebookingStateEnum.Added && session.CurrentFlow.RebookingType ==
                                Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight)
                            {
                                var removedFlightSegmentIds = session.CurrentFlow.PNR.Flights
                                    .FirstOrDefault(t => t.State == OfferRebookingStateEnum.Removed)
                                    .Segments
                                    .Select(k => k.Id).ToList();
                                if (removedFlightSegmentIds.Count > 0 &&
                                    removedFlightSegmentIds.Any(t => !string.IsNullOrEmpty(t)))
                                {
                                    decimal alreadyTakenExtraBaggageValue = session.CurrentFlow.PnrInfoResponse.Services
                                                                                .Baggages
                                                                                .FirstOrDefault(t =>
                                                                                    t.SegmentIds.Any(k =>
                                                                                        removedFlightSegmentIds
                                                                                            .Contains(k))
                                                                                    && t.PassengerIds.Contains(pass.Id))
                                                                                ?.ExtraBaggage ??
                                                                            decimal.Zero;
                                    baggage.MaximumWeight -= alreadyTakenExtraBaggageValue;
                                }
                            }
                            else if (flight.State == OfferRebookingStateEnum.ServiceChanging ||
                                     flight.State == OfferRebookingStateEnum.Upgraded)
                            {
                                var extraBaggageExistForCurrenPassenger = session.CurrentFlow.PnrInfoResponse.Services
                                    .Baggages
                                    .FirstOrDefault(t =>
                                        t.SegmentIds.Any(a => flight.Segments.Select(k => k.Id).Contains(a))
                                        && t.PassengerIds.Contains(pass.Id));
                                if (extraBaggageExistForCurrenPassenger != null)
                                {
                                    baggage.MaximumWeight -= extraBaggageExistForCurrenPassenger.ExtraBaggage;
                                }
                            }
                        }

                        var baggageServices = GetServiceDTOs(response, ServiceCategoryEnum.BAGGAGE,
                                serviceCodeCategories, session)
                            .Where(w => w.PassengerIds.Contains(pass.Id) &&
                                        (
                                            w.SegmentIds.Contains(flight.Segments.FirstOrDefault().Id) ||
                                            w.SegmentIds.Contains(flight.Segments.FirstOrDefault().LocalId)
                                        )
                            )
                            .ToList();

                        if (baggageServices != null && baggageServices.Count > 0)
                        {
                            foreach (var item in baggageServices)
                            {
                                var baggageService = orderReshop.GetDataList().ServiceDefinitionList
                                    .FirstOrDefault(f => f.ServiceDefinitionID == item.Id);

                                if (item.TotalPrice == 0)
                                {
                                    #region Free Weight

                                    try
                                    {
                                        var augId = baggageService.Encoding.refs;

                                        var freeWeightXmlNode = orderReshop
                                            ?.GetReponseObject()
                                            ?.Metadata
                                            ?.PassengerMetadata
                                            ?.Select(s => s.AugmentationPoint)
                                            ?.SelectMany(s => s.AugPoint)
                                            ?.FirstOrDefault(f => f.Key == augId);

                                        if (freeWeightXmlNode != null)
                                        {
                                            string weightInfo = null;
                                            foreach (XmlNode wNode in freeWeightXmlNode.Any.ChildNodes[0].ChildNodes)
                                            {
                                                if (wNode.ChildNodes[0].ChildNodes[0].Value == "WEIGHT")
                                                {
                                                    weightInfo = wNode.ChildNodes[1].ChildNodes[0].Value;
                                                    break;
                                                }
                                            }

                                            var weight = int.TryParse(weightInfo, out int w) ? w : 0;

                                            baggage.MaximumWeight += weight;
                                        }
                                    }
                                    catch
                                    {
                                    }

                                    #endregion

                                    var alreadyTakenBaggage = session.CurrentFlow.PnrInfoResponse
                                        .Services
                                        .Baggages.FirstOrDefault(t => t.PassengerIds.Contains(pass.Id)
                                                                      && flight.Segments.Select(k => k.Id)
                                                                          .Any(w => t.SegmentIds.Contains(w)));
                                    if (alreadyTakenBaggage != null)
                                    {
                                        baggage.MaximumWeight += alreadyTakenBaggage.ExtraBaggage;
                                    }
                                }
                                else
                                {
                                    #region Extra Weight

                                    //var chargeResponse = session.CurrentFlow.GetIbsData<BaggageChargesRS>(IbsDataTypeEnum.BaggageCharge.ToString() + "_" + flight.TmobId);
                                    if (session.CurrentFlow.RebookingType != Ubimecs.Infrastructure.Models.Flow
                                            .RebookingTypeEnum.ChangeFlight)
                                    {
                                        var passengerBoughtPrevBaggage = session.CurrentFlow
                                            .PnrInfoResponse
                                            .Services
                                            .Baggages
                                            .FirstOrDefault(t => t.SegmentIds.Any(k =>
                                                flight.SegmentRefs.Split(' ').Contains(k)
                                                && t.PassengerIds.Contains(pass.Id)));
                                        if (passengerBoughtPrevBaggage.ExtraBaggage != decimal.Zero)
                                        {
                                            var serviceCategories = CachedData.ServiceCodeCategories;
                                            var deleteOfferItems = orderReshop.GetOffers()?
                                                .SelectMany(k => k.DeleteOfferItem)?
                                                .Select(q => q.OfferItemID)?
                                                .Where(a => a.Contains("SSR"))?
                                                .Select(z => z.Substring(z.LastIndexOf("SSR.")))
                                                .ToList();
                                            var baggageServiceDeleted = orderReshop.GetDataList()?.ServiceDefinitionList
                                                ?
                                                .FirstOrDefault(k =>
                                                    deleteOfferItems.Select(t => "V1_" + t).ToList()
                                                        .Contains(k.ServiceDefinitionID)
                                                    && (IbsUtility.GetCategory(serviceCategories,
                                                            k.Encoding?.Code?.Value) == ServiceCategoryEnum.BAGGAGE ||
                                                        k.Encoding?.Code?.Value == "XBAG"));
                                            var deletedBaggageOfferItem = orderReshop.GetOffers()?
                                                .SelectMany(w => w.DeleteOfferItem)?
                                                .FirstOrDefault(k =>
                                                    k.OfferItemID.Contains(
                                                        baggageServiceDeleted.ServiceDefinitionID.Replace("V1_", "")));
                                            if (deletedBaggageOfferItem != null)
                                            {
                                                var newPriceWillBePaid =
                                                    deletedBaggageOfferItem.ReshopDifferential.NewOfferItem.Total.Amount
                                                        .Value - deletedBaggageOfferItem.ReshopDifferential
                                                        .OriginalOrderItem.Total.Amount.Value;
                                                baggage.TotalPrice += newPriceWillBePaid;
                                            }
                                            else
                                            {
                                                baggage.TotalPrice += item.TotalPrice;
                                            }
                                        }
                                        else
                                        {
                                            baggage.TotalPrice += item.TotalPrice;
                                        }
                                    }
                                    else
                                    {
                                        baggage.TotalPrice += item.TotalPrice;
                                    }

                                    //if (chargeResponse != null && (addedWeight == null || addedWeight == decimal.Zero))
                                    //{
                                    //    var chargeResponseService = chargeResponse.GetServices().FirstOrDefault(f =>
                                    //    f.Encoding.Code.Value == baggageService.Encoding.Code.Value &&
                                    //    f.Encoding.SubCode.Value == baggageService.Encoding.SubCode.Value
                                    //    );
                                    //    var weight = chargeResponse.GetGetCheckedBags().FirstOrDefault(f => f.refs == chargeResponseService.ObjectKey)?.WeightAllowance?.MaximumWeight?.FirstOrDefault()?.Value ?? 0;
                                    //    baggage.MaximumWeight += weight;
                                    //}

                                    #endregion
                                }
                            }
                        }


                        if (session.CurrentFlow.RebookingType ==
                            Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras)
                        {
                            //var freeBaggageFromPnrInfo = session.CurrentFlow.PnrInfoResponse.Services.Baggages.FirstOrDefault(f =>
                            //IbsUtility.CheckIdsEqual(f.PassengerIds, baggage.PassengerIds) &&
                            //IbsUtility.CheckIdsEqual(f.SegmentIds, baggage.SegmentIds));
                            var freeBaggageFromPnrInfo =
                                session.CurrentFlow.PnrInfoResponse.Services.Baggages.FirstOrDefault(f =>
                                    baggage.PassengerIds.Any(t => f.PassengerIds.Contains(t)) &&
                                    baggage.SegmentIds.Any(t => f.SegmentIds.Contains(t)));

                            baggage.MaximumWeight += (freeBaggageFromPnrInfo?.MaximumWeight) ?? 0;
                        }

                        if (session.CurrentFlow.RebookingType ==
                            Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight &&
                            flight.State == OfferRebookingStateEnum.Added)
                        {
                            var removedFlight =
                                session.CurrentFlow.PNR.Flights?.FirstOrDefault(t =>
                                    t.State == OfferRebookingStateEnum.Removed);
                            var removedFlightBundle = removedFlight?.SelectedBundle;
                            if (removedFlightBundle != null &&
                                removedFlightBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE &&
                                flight.SelectedBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE)
                            {
                                var freeBaggageFromPnrInfo = session.CurrentFlow.PnrInfoResponse.Services.Baggages
                                    .FirstOrDefault(f =>
                                        f.SegmentIds.Any(t => removedFlight.Segments.Select(k => k.Id).Contains(t))
                                        && f.PassengerIds.Contains(pass.Id));
                                if (freeBaggageFromPnrInfo != null)
                                {
                                    baggage.MaximumWeight += freeBaggageFromPnrInfo.MaximumWeight;
                                }
                                else
                                {
                                    freeBaggageFromPnrInfo = session.CurrentFlow.PnrInfoResponse.Services.Baggages
                                        .FirstOrDefault(f =>
                                            f.SegmentIds.Any(t =>
                                                removedFlight.Segments.Select(k => k.LocalId).Contains(t)) &&
                                            f.PassengerIds.Contains(pass.Id));
                                    if (freeBaggageFromPnrInfo != null)
                                    {
                                        baggage.MaximumWeight += freeBaggageFromPnrInfo.MaximumWeight;
                                    }
                                }
                            }
                        }

                        if (session.CurrentFlow.RebookingType ==
                            Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.TourOperator &&
                            flight.State == OfferRebookingStateEnum.ServiceChanging)
                        {
                            var serviceChangingFlight =
                                session.CurrentFlow.PNR.Flights?.FirstOrDefault(t =>
                                    t.State == OfferRebookingStateEnum.ServiceChanging);
                            var serviceChangingFlightBundle = serviceChangingFlight?.SelectedBundle;
                            if (serviceChangingFlightBundle != null &&
                                serviceChangingFlightBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE)
                            {
                                var freeBaggageFromPnrInfo = session.CurrentFlow.PnrInfoResponse.Services.Baggages
                                    .FirstOrDefault(f => f.SegmentIds.Any(t =>
                                        serviceChangingFlight.Segments.Select(k => k.Id).Contains(t)));
                                if (freeBaggageFromPnrInfo != null)
                                {
                                    baggage.MaximumWeight += freeBaggageFromPnrInfo.MaximumWeight;
                                }
                                else
                                {
                                    freeBaggageFromPnrInfo = session.CurrentFlow.PnrInfoResponse.Services.Baggages
                                        .FirstOrDefault(f => f.SegmentIds.Any(t =>
                                            serviceChangingFlight.Segments.Select(k => k.LocalId).Contains(t)));
                                    if (freeBaggageFromPnrInfo != null)
                                    {
                                        baggage.MaximumWeight += freeBaggageFromPnrInfo.MaximumWeight;
                                    }
                                }
                            }
                        }

                        if (session.CurrentFlow.RebookingType ==
                            Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight &&
                            flight.State == OfferRebookingStateEnum.Added)
                        {
                            var currentBaggage = session.CurrentFlow.PnrInfoResponse.Services.Baggages
                                .FirstOrDefault(t => baggage.SegmentIds.Any(k => t.SegmentIds.Contains(k))
                                                     && baggage.PassengerIds.Any(q => t.PassengerIds.Contains(q))
                                                     && t.MaximumWeight != 0);
                            baggage.MaximumWeight +=
                                (currentBaggage?.MaximumWeight - currentBaggage?.ExtraBaggage) ?? 0;
                        }

                        baggages.Add(baggage);
                    }
                }
            }


            return baggages;
        }

        public static AllServices GetAllServices(object response, SessionCache session)
        {
            var serviceCategories = CachedData.ServiceCodeCategories;

            var Services = new AllServices();
            Services.Bundles = GeneralMappers.GetBundles(response, serviceCategories, session);
            Services.Seats = GeneralMappers.GetSeats(response, session);
            Services.Baggages = GeneralMappers.GetBaggages(response, session, serviceCategories);
            Services.SportEquipments = GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.SPORTS_EQUIPMENTS,
                serviceCategories, session);
            Services.Meals =
                GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.MEALS, serviceCategories, session);
            Services.Others =
                GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.OTHERS, serviceCategories, session);
            Services.FlexServices =
                GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.FLEX, serviceCategories, session);
            Services.CoronaServices =
                GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.CORONA, serviceCategories, session);
            Services.GolfBundles =
                GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.GOLF_BUNDLE, serviceCategories, session);
            Services.IfeServices =
                GeneralMappers.GetServiceDTOs(response, ServiceCategoryEnum.IFE, serviceCategories, session);

            return Services;
        }

        public static AllServices GetAllServicesNative(object response, SessionCache session,IEnumerable<dynamic> itinary)
        {
            var services = new AllServices();
            List<BaseBundleDTO> bundles = new();
            List<BaseSeatDTO> seats = new();
            List<BaseBaggageDTO> baggages = new();
            List<BaseServiceDTO> sportEquipments = new();
            List<BaseServiceDTO> meals = new();
            List<BaseServiceDTO> flexServices = new();
            List<BaseServiceDTO> others = new();
            foreach (var itinaryValue in itinary)
            {
                var bundlesData = GetBundles(response,itinaryValue, session);
                bundles.AddRange(bundlesData);
                var seatsData = GetSeatsNative(response, session,itinaryValue);
                seats.AddRange(seatsData);
                var baggagesData = GetBaggagesNative(response,itinaryValue, session);
                baggages.AddRange(baggagesData);
                var sportEquipmentsData = GetServiceDTOs(response, NativeAPIAncillaryConstants.SPORTS_EQUIPMENTS,itinaryValue, session);
                sportEquipments.AddRange(sportEquipmentsData);
                var mealsData = GetServiceDTOs(response, NativeAPIAncillaryConstants.MEALS,itinaryValue, session);
                meals.AddRange(mealsData);
                var flexData = GetServiceDTOs(response, NativeAPIAncillaryConstants.FLEX,itinaryValue, session);
                flexServices.AddRange(flexData);
                var flexExtrasData = GetServiceDTOs(response, NativeAPIAncillaryConstants.FLEXEXTRA,itinaryValue, session);
                flexServices.AddRange(flexExtrasData);
                var othersData =GetOtherServices(response,itinaryValue, session);
                others.AddRange(othersData);
            }

            services.Bundles = bundles;
            services.Seats = seats;
            services.Baggages = baggages;
            services.SportEquipments = sportEquipments;
            services.Meals = meals;
            services.FlexServices = flexServices;
            services.Others = others;
            
            return services;
        }

        public static List<BaseBundleDTO> GetBundles(object response,dynamic itinary, SessionCache session)
        {
            string segmentId = itinary.SegmentId;
            int stops = itinary.stops;
            
            if (response.GetType() == typeof(confirmPriceResponse))
            {
                if ((response as confirmPriceResponse)?.ConfirmPriceRS.SSRDetails == null)
                {
                    return new List<BaseBundleDTO>();
                }

                return (response as confirmPriceResponse)?.ConfirmPriceRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BUNDLE && ssr.SegmentId.Any(s =>s == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as confirmPriceResponse)?.ConfirmPriceRS.FeeInformation?.FirstOrDefault(f =>
                            f.AncillaryDetailsType != null &&
                            f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseBundleDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.modifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS.SSRDetails == null)
                {
                    return new List<BaseBundleDTO>();
                }

                return (response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BUNDLE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseBundleDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.retrieveBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS?.SSRDetails == null)
                {
                    return new List<BaseBundleDTO>();
                }

                return (response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS
                    .SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BUNDLE && ssr.SegmentId.Any(sId =>sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.retrieveBookingResponse)
                            ?.RetrieveBookingRS.FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseBundleDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.saveCreateBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS?.SSRDetails == null)
                {
                    return new List<BaseBundleDTO>();
                }

                return (response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS
                    .SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BUNDLE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.saveCreateBookingResponse)
                            ?.CreateBookingRS.FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null && 
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseBundleDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveModifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS?.SSRDetails == null)
                {
                    return new List<BaseBundleDTO>();
                }

                return (response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS
                    .SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BUNDLE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.saveModifyBookingResponse)
                            ?.SaveModifyBookingRS.FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseBundleDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }

            return new List<BaseBundleDTO>();
        }

        public static List<BaseSeatDTO> GetSeatsNative(object response, SessionCache session,dynamic itinary)
        {
            string segmentId = itinary.SegmentId;
            int stops = itinary.stops;

            if (response.GetType() == typeof(confirmPriceResponse))
            {
                if ((response as confirmPriceResponse)?.ConfirmPriceRS.SeatAssignmentDetails is null)
                {
                    return new List<BaseSeatDTO>(); 
                }
                var seats = (response as confirmPriceResponse)?.ConfirmPriceRS.SeatAssignmentDetails.Where(x=>x.SegmentId == segmentId)
                    .SelectMany(seatAssignment => seatAssignment.GuestSeatDetails.Select(guestSeat =>
                    {
                        var guestId = Convert.ToInt64(guestSeat.GuestId);
                        var guestSeatDetail = seatAssignment.GuestSeatDetails.FirstOrDefault(x => x.GuestId == guestSeat.GuestId);

                        var seatNumber = guestSeatDetail?.SeatNumbers;
                        // Sayısal ve harf kısmını ayırmak için
                        string number = new string(seatNumber?.TakeWhile(char.IsDigit).ToArray()); // Sayılar
                        string column = new string(seatNumber?.SkipWhile(char.IsDigit).ToArray()); // Harfler
                        var lastSeatAvailability =
                            session.CurrentFlow?.LatestSeatResponse?.Seats?.FirstOrDefault(t =>
                                t.SeatNumber == number && t.Column == column);
                        var seatSection = IbsUtility.GetSeatSection(lastSeatAvailability.SeatType);

                        var seatDetail = (response as confirmPriceResponse)?.ConfirmPriceRS.SSRDetails
                            .FirstOrDefault(x =>
                                x.ssrType == NativeAPIAncillaryConstants.SEAT && x.GuestId == guestId &&
                                x.ssrCode == seatSection && x.ParentSSRId == 0 && x.SegmentId.Any(sId=>sId == segmentId));

                        var seatPrice = (response as confirmPriceResponse)?.ConfirmPriceRS.FeeInformation?
                            .FirstOrDefault(x =>
                                x.FeeCode == NativeAPIAncillaryConstants.SEAT &&
                                x.AncillaryDetailsType != null &&
                                x.AncillaryDetailsType.Any(y => y.AncillaryId == seatDetail.SSRId));

                        return new BaseSeatDTO
                        {
                            Id = IbsUtility.GetFullSsrId(seatDetail.SSRId.ToString()),
                            SeatId = guestSeatDetail.SeatId.ToString(),
                            Name = seatDetail.ssrName,
                            DescriptionText = seatDetail.ssrDescription,
                            Code = seatDetail.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(seatDetail.GuestId.ToString()) },
                            TotalPrice = (decimal)seatPrice.FeeAmount,
                            Currency = seatPrice.FeeCurrency,
                            Count = seatDetail.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(seatDetail.SegmentId.FirstOrDefault()), stops),
                            SegmentIds = seatDetail.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            Column = new string(guestSeat.SeatNumbers.Where(char.IsLetter).Last().ToString() ??
                                                string.Empty),
                            SeatNumber = new string(guestSeat.SeatNumbers.Where(char.IsDigit).ToArray()),
                            SeatAttribute = lastSeatAvailability.SeatType,
                        };
                    }))
                    .ToList();

                return seats;
            }

            if (response is ReservationsPortServiceReference.modifyBookingResponse modifyBookingResponse)
            {
                var seatAssignmentDetails = modifyBookingResponse.ModifyBookingRS?.SeatAssignmentDetails;
                if (seatAssignmentDetails == null || !seatAssignmentDetails.Any())
                {
                    return new List<BaseSeatDTO>();
                }
                
                var seats = seatAssignmentDetails
                    .Where(seatAssignment => seatAssignment.GuestSeatDetails != null && seatAssignment.SegmentId == segmentId)
                    .SelectMany(seatAssignment => seatAssignment.GuestSeatDetails.Select(guestSeat =>
                    {
                        var guestId = Convert.ToInt64(guestSeat.GuestId);
                        var guestSeatDetail = seatAssignment.GuestSeatDetails?
                            .FirstOrDefault(x => x.GuestId == guestSeat.GuestId);

                        if (guestSeatDetail == null || string.IsNullOrEmpty(guestSeatDetail.SeatNumbers))
                        {
                            return null;
                        }

                        var seatNumber = guestSeatDetail.SeatNumbers;
                        string number = new string(seatNumber.TakeWhile(char.IsDigit).ToArray());
                        string column = new string(seatNumber.SkipWhile(char.IsDigit).ToArray());
                        
                        string seatSection = string.Empty;
                        SeatTypeEnum seatType = SeatTypeEnum.Normal;
                        var seats = session.CurrentFlow.PnrInfoResponse.Services.Seats;
                        var lastPnrSeats = seats
                                .FirstOrDefault(t => t.SeatNumber == number && t.Column == column);
                       
                        if (lastPnrSeats == null)
                        {
                            var latestSeatType = session.CurrentFlow?.LatestSeatResponse?.Seats.FirstOrDefault(t => t.SeatNumber == number && t.Column == column)?.SeatType;
                            if (latestSeatType != null)
                            {
                                seatSection = IbsUtility.GetSeatSection(latestSeatType); 
                                seatType = latestSeatType ?? SeatTypeEnum.Normal;
                            }
                        }
                        else
                        {
                            seatSection = IbsUtility.GetSeatSection(IbsUtility.GetSeatTypeByCode(lastPnrSeats.Code));
                            seatType = IbsUtility.GetSeatTypeByCode(lastPnrSeats.Code);
                        }

                        var seatDetail = modifyBookingResponse.ModifyBookingRS?.SSRDetails?
                            .FirstOrDefault(x =>
                                x.ssrType == NativeAPIAncillaryConstants.SEAT && x.GuestId == guestId &&
                                x.ssrCode == seatSection && x.ParentSSRId == 0 && x.SegmentId.Any(sId=> sId == segmentId));

                        if (seatDetail == null)
                        {
                            return null;
                        }

                        var seatPrice = modifyBookingResponse.ModifyBookingRS?.FeeInformation?
                            .FirstOrDefault(x =>
                                x.FeeCode == NativeAPIAncillaryConstants.SEAT 
                                && x.AncillaryDetailsType != null && x.AncillaryDetailsType.Any(y => y.AncillaryId == seatDetail.SSRId));

                        return new BaseSeatDTO
                        {
                            Id = IbsUtility.GetFullSsrId(seatDetail.SSRId.ToString()),
                            SeatId = seatDetail.SeatId.ToString(),
                            Name = seatDetail.ssrName,
                            DescriptionText = seatDetail.ssrDescription,
                            Code = seatDetail.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(seatDetail.GuestId.ToString()) },
                            TotalPrice = (decimal?)seatPrice?.FeeAmount ?? 0,
                            Currency = seatPrice?.FeeCurrency ?? string.Empty,
                            Count = seatDetail.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(seatDetail.SegmentId.FirstOrDefault()), stops),
                            SegmentIds = seatDetail.SegmentId?.Select(id => IbsUtility.GetFullSegmentId(id.ToString())).ToArray(),
                            Column = column,
                            SeatNumber = number,
                            SeatAttribute = seatType,
                        };
                    }))
                    .Where(seat => seat != null) // Null sonuçları filtrele
                    .ToList();

                if (!seats.Any())
                {
                    var latestSeats = session.CurrentFlow.LatestSeatResponse?.Seats;
                    if (latestSeats == null)
                    {
                        return session.CurrentFlow?.PnrInfoResponse?.Services.Seats;
                    }
                    
                    var fullSegmentId = IbsUtility.GetFullSegmentId(segmentId);
                    var segmentTmobId = session.CurrentFlow.PNR.Flights
                        .FirstOrDefault(x => x.Segments.Any(s => s.Id == fullSegmentId))?.Segments.FirstOrDefault(s => s.Id == fullSegmentId)?.TmobId; 
                    var providerData = session.CurrentFlow.GetProviderData(IbsDataTypeEnum.Seats + "_" + segmentTmobId);
                    if (!string.IsNullOrEmpty(providerData))
                    {
                        var ibsSeats = session.CurrentFlow.GetIbsData<ShowSeatMapRS>(IbsDataTypeEnum.Seats+"_"+segmentTmobId).GetSeats();
                        return session.CurrentFlow.PNR.Seats.Select(s => new BaseSeatDTO()
                        {
                            Column = s.Column,
                            SeatNumber = s.Number,
                            PetWeight = null,
                            Code =  IbsUtility.GetSeatSection(latestSeats.FirstOrDefault(x=>x.SeatNumber == s.Number && x.Column == s.Column)?.SeatType).ToString(), 
                            Count = 1,
                            Currency = session.CurrentFlow.PNR.Currency,
                            PassengerIds = new string[]{s.PassengerId},
                            DescriptionText = "",
                            Id = "", //IbsUtility.GetFullSsrId(seatDetail.SSRId.ToString()),
                            SeatId = "",
                            Name = "",
                            TotalPrice = (decimal)ibsSeats.FirstOrDefault(x=>x.Column == s.Column && x.SeatNumber == s.Number)?.Price,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(segmentId), stops),
                            SegmentIds = new string[]{fullSegmentId},
                            SeatAttribute =  latestSeats.FirstOrDefault(x=>x.Column == s.Column && x.SeatNumber == s.Number).SeatType,
                        }).ToList();
                    }
                }
                return seats;
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.retrieveBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS
                    ?.SeatAssignmentDetails == null)
                {
                    return new List<BaseSeatDTO>();
                }

                var seats = (response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS
                    .SeatAssignmentDetails
                    .Where(seatAssignment => seatAssignment.GuestSeatDetails != null && seatAssignment.SegmentId == segmentId)
                    .SelectMany(seatAssignment => seatAssignment.GuestSeatDetails.Select(guestSeat =>
                    {
                        var seatDetails = (response as ReservationsPortServiceReference.retrieveBookingResponse)
                            ?.RetrieveBookingRS.SSRDetails
                            .FirstOrDefault(x => x.SeatId == guestSeat.SeatId && x.SegmentId.Any(sId =>sId == segmentId));
                       
                        if (seatDetails == null)
                        {
                            return null;
                        }
                        
                        var seatPrice = (response as ReservationsPortServiceReference.retrieveBookingResponse)
                            ?.RetrieveBookingRS.FeeInformation?
                            .FirstOrDefault(x =>
                                x.FeeCode == NativeAPIAncillaryConstants.SEAT &&
                                x.AncillaryDetailsType != null &&
                                x.AncillaryDetailsType.Any(y => y.AncillaryId == seatDetails.SSRId));

                        return new BaseSeatDTO
                        {
                            Id = IbsUtility.GetFullSsrId(seatDetails.SSRId.ToString()),
                            SeatId = seatDetails.SeatId.ToString(),
                            Name = seatDetails.ssrName,
                            DescriptionText = seatDetails.ssrDescription,
                            Code = seatDetails.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(seatDetails.GuestId.ToString()) },
                            TotalPrice = (decimal)seatPrice.FeeAmount,
                            Currency = seatPrice.FeeCurrency,
                            Count = seatDetails.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(seatDetails.SegmentId.FirstOrDefault()), stops),
                            SegmentIds = seatDetails.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            Column = new string(guestSeat.SeatNumbers.Where(char.IsLetter).Last().ToString() ??
                                                string.Empty),
                            SeatNumber = new string(guestSeat.SeatNumbers.Where(char.IsDigit).ToArray()),
                            SeatAttribute = IbsUtility.GetSeatTypeByCode(seatDetails.ssrCode),
                        };
                    }))
                    .Where(seat => seat != null)
                    .ToList();
                return seats;
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveCreateBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS
                    ?.SeatAssignmentDetails == null)
                {
                    return new List<BaseSeatDTO>();
                }

                var seats = (response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS
                    .SeatAssignmentDetails
                    .Where(seatAssignment => seatAssignment.GuestSeatDetails != null && seatAssignment.SegmentId == segmentId)
                    .SelectMany(seatAssignment => seatAssignment.GuestSeatDetails.Select(guestSeat =>
                    {
                        var seatDetails = (response as ReservationsPortServiceReference.saveCreateBookingResponse)
                            ?.CreateBookingRS.SSRDetails
                            .FirstOrDefault(x => x.SeatId == guestSeat.SeatId && x.SegmentId.Any(sId => sId == segmentId));

                        var seatPrice = (response as ReservationsPortServiceReference.saveCreateBookingResponse)
                            ?.CreateBookingRS.FeeInformation?
                            .FirstOrDefault(x =>
                                x.FeeCode == NativeAPIAncillaryConstants.SEAT &&
                                x.AncillaryDetailsType != null &&
                                x.AncillaryDetailsType.Any(y => y.AncillaryId == seatDetails.SSRId));

                        return new BaseSeatDTO
                        {
                            Id = IbsUtility.GetFullSsrId(seatDetails.SSRId.ToString()),
                            SeatId = seatDetails.SeatId.ToString(),
                            Name = seatDetails.ssrName,
                            DescriptionText = seatDetails.ssrDescription,
                            Code = seatDetails.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(seatDetails.GuestId.ToString()) },
                            TotalPrice = (decimal)seatPrice.FeeAmount,
                            Currency = seatPrice.FeeCurrency,
                            Count = seatDetails.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(seatDetails.SegmentId.FirstOrDefault()), stops),
                            SegmentIds = seatDetails.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            Column = new string(guestSeat.SeatNumbers.Where(char.IsLetter).Last().ToString() ??
                                                string.Empty),
                            SeatNumber = new string(guestSeat.SeatNumbers.Where(char.IsDigit).ToArray()),
                            SeatAttribute = IbsUtility.GetSeatTypeByCode(seatDetails.ssrCode),
                        };
                    }))
                    .ToList();
                return seats;
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveModifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS
                    ?.SeatAssignmentDetails == null)
                {
                    return new List<BaseSeatDTO>();
                }

                var seats = (response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS
                    .SeatAssignmentDetails
                    .Where(seatAssignment => seatAssignment.GuestSeatDetails != null && seatAssignment.SegmentId == segmentId)
                    .SelectMany(seatAssignment => seatAssignment.GuestSeatDetails.Select(guestSeat =>
                    {
                        var seatDetails = (response as ReservationsPortServiceReference.saveModifyBookingResponse)
                            ?.SaveModifyBookingRS.SSRDetails
                            .FirstOrDefault(x => x.SeatId == guestSeat.SeatId && x.SegmentId.Any(sId => sId == segmentId));
                        if (seatDetails == null)
                        {
                            return null;
                        }
                        var seatPrice = (response as ReservationsPortServiceReference.saveModifyBookingResponse)
                            ?.SaveModifyBookingRS.FeeInformation?
                            .FirstOrDefault(x =>
                                x.FeeCode == NativeAPIAncillaryConstants.SEAT &&
                                x.AncillaryDetailsType != null &&
                                x.AncillaryDetailsType.Any(y => y.AncillaryId == seatDetails.SSRId));

                        return new BaseSeatDTO
                        {
                            Id = IbsUtility.GetFullSsrId(seatDetails.SSRId.ToString()),
                            SeatId = seatDetails.SeatId.ToString(),
                            Name = seatDetails.ssrName,
                            DescriptionText = seatDetails.ssrDescription,
                            Code = seatDetails.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(seatDetails.GuestId.ToString()) },
                            TotalPrice = (decimal)seatPrice.FeeAmount,
                            Currency = seatPrice.FeeCurrency,
                            Count = seatDetails.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(seatDetails.SegmentId.FirstOrDefault()), stops),
                            SegmentIds = seatDetails.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            Column = new string(guestSeat.SeatNumbers.Where(char.IsLetter).Last().ToString() ??
                                                string.Empty),
                            SeatNumber = new string(guestSeat.SeatNumbers.Where(char.IsDigit).ToArray()),
                            SeatAttribute = IbsUtility.GetSeatTypeByCode(seatDetails.ssrCode),
                        };
                    }))
                    .Where(seat => seat != null)
                    .ToList();
                return seats;
            }

            return new List<BaseSeatDTO>();
        }

        
        public static List<BaseBaggageDTO> GetBaggagesNative(object response,dynamic itinary, SessionCache session)
        {
            string segmentId = itinary.SegmentId;
            int stops = itinary.stops;

            if (response.GetType() == typeof(confirmPriceResponse))
            {
                if ((response as confirmPriceResponse)?.ConfirmPriceRS?.SSRDetails == null)
                {
                    return new List<BaseBaggageDTO>();
                }

                var baggageGroups = (response as confirmPriceResponse).ConfirmPriceRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BAGGAGE && ssr.ssrCode != NativeAPIAncillaryConstants.CABIN_BAGGAGE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .GroupBy(ssr => new
                    {
                        PassengerId = IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()),
                        SegmentIds = string.Join(",",
                            ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString())).OrderBy(x => x))
                    });

                var baggages = new List<BaseBaggageDTO>();

                foreach (var group in baggageGroups)
                {
                    int totalWeightFromCode = 0;
                    int totalExtraBaggage = 0;
                    decimal totalPrice = 0;
                    string? currency = null;

                    foreach (var ssr in group)
                    {
                        var fee = (response as confirmPriceResponse).ConfirmPriceRS.FeeInformation?.FirstOrDefault(f =>
                            f.AncillaryDetailsType != null &&
                            f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice += parsedAmount;
                            currency = fee.FeeCurrency;
                        }

                        if (ssr.ssrCode != null)
                        {
                            if (ssr.ssrCode.EndsWith("KG"))
                            {
                                string numberPart = new string(ssr.ssrCode.TakeWhile(char.IsDigit).ToArray());
                                int.TryParse(numberPart, out int weightFromCode);
                                totalWeightFromCode += weightFromCode;
                            }
                            else if (ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE)
                            {
                                var ssrFieldValue = ssr.SsrFieldDetailsType?.FirstOrDefault()?.SsrFieldValue;
                                int.TryParse(ssrFieldValue, out int extraBaggage);
                                totalExtraBaggage += extraBaggage;
                            }
                        }

                        baggages.Add(new BaseBaggageDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = currency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            ExtraBaggage = totalExtraBaggage,
                            MaximumWeight = totalWeightFromCode + totalExtraBaggage
                        });
                    }
                }
                
                
                var groupedBaggages = baggages
                    .SelectMany(b => b.SegmentIds.Select(segmentId => new { Baggage = b, SegmentId = segmentId }))
                    .GroupBy(x => new { x.SegmentId, PassengerId = x.Baggage.PassengerIds.FirstOrDefault() })
                    .Select(g => new BaseBaggageDTO
                    {
                        Id = string.Join("_", g.Select(x => x.Baggage.Id)),
                        Name = g.First().Baggage.Name,
                        DescriptionText = g.First().Baggage.DescriptionText,
                        Code = g.First().Baggage.Code,
                        PassengerIds = new[] { g.Key.PassengerId },
                        TotalPrice = g.Max(x => x.Baggage.TotalPrice), 
                        Currency = g.First().Baggage.Currency,
                        Count = g.Max(x => x.Baggage.Count), 
                        SegmentIds = g.Select(x => x.SegmentId).Distinct().ToArray(),
                        Status = g.Max(x => x.Baggage.Status),
                        ExtraBaggage = g.Max(x => x.Baggage.ExtraBaggage),
                        MaximumWeight = g.Max(x => x.Baggage.MaximumWeight),
                        FlightIds = g.First().Baggage.FlightIds
                    })
                    .ToList();
                return groupedBaggages;
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.modifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS?.SSRDetails == null)
                    return new List<BaseBaggageDTO>();

                var baggageGroups = (response as ReservationsPortServiceReference.modifyBookingResponse)
                    ?.ModifyBookingRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BAGGAGE && ssr.ssrCode != NativeAPIAncillaryConstants.CABIN_BAGGAGE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .GroupBy(ssr => new
                    {
                        PassengerId = IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()),
                        SegmentIds = string.Join(",", ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString())).OrderBy(x => x))
                    });

                var baggages = new List<BaseBaggageDTO>();

                foreach (var group in baggageGroups)
                {
                    int totalWeightFromCode = 0;
                    int totalExtraBaggage = 0;
                    decimal totalPrice = 0;
                    string? currency = null;
                    string? id = null;
                    string? name = null;
                    string? description = null;
                    string? ssrSegmentId = null;
                    SsrStatusEnum ssrStatus = SsrStatusEnum.NONE;

                    foreach (var ssr in group)
                    {
                        var fee = (response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice += parsedAmount;
                            currency = fee.FeeCurrency;
                        }

                        if (ssr.ssrCode != null)
                        {
                            id = ssr.SSRId.ToString();
                            name = ssr.ssrName;
                            description = ssr.ssrDescription;
                            ssrSegmentId = IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault());
                            ssrStatus = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE;
                            if (ssr.ssrCode.EndsWith("KG"))
                            {
                                string numberPart = new string(ssr.ssrCode.TakeWhile(char.IsDigit).ToArray());
                                int.TryParse(numberPart, out int weightFromCode);
                                totalWeightFromCode = weightFromCode;
                            }
                            else if (ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE)
                            {
                                var ssrFieldValue = ssr.SsrFieldDetailsType?.FirstOrDefault()?.SsrFieldValue;
                                int.TryParse(ssrFieldValue, out int extraBaggage);
                                totalExtraBaggage = extraBaggage;
                            }
                        }
                    }

                    decimal bookingExtraWeight = 0;
                    decimal bookingExtraBaggagePrice = 0;

                    var orderRetrieveRS = session.CurrentFlow.GetIbsData<RetrieveBookingRS>(IbsDataTypeEnum.OrderRetrieve);
                    var pnrInfoResponseBaggageServices = new List<SSRInformationType>();
                    if (session.CurrentFlow.RebookingType != RebookingTypeEnum.ChangeFlightNative)
                    {
                        pnrInfoResponseBaggageServices = orderRetrieveRS.SSRDetails
                            .Where(ssr => ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE && 
                                          ssr.GuestId == Convert.ToInt64(IbsUtility.ParseFullId(group.Key.PassengerId)) && 
                                          ssr.SegmentId.Any(x => group.Key.SegmentIds.Split(',').Contains(IbsUtility.GetFullSegmentId(x.ToString()))))
                            .ToList(); 
                    }
                    else
                    {
                        var oldTmobId = session.CurrentFlow.ChangeFlightOldTmobId;
                        var fullSegmentId = session.CurrentFlow.PNR.Flights.FirstOrDefault(x => x.TmobId == oldTmobId)?.Segments.FirstOrDefault()?.Id;
                        pnrInfoResponseBaggageServices = orderRetrieveRS.SSRDetails
                            .Where(ssr => ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE && 
                                          ssr.GuestId == Convert.ToInt64(IbsUtility.ParseFullId(group.Key.PassengerId)) && 
                                          ssr.SegmentId.Any(x => x == IbsUtility.ParseFullId(fullSegmentId)))
                            .ToList();
                    }

                    if (pnrInfoResponseBaggageServices.Any())
                    {
                        var ssrFieldValue = pnrInfoResponseBaggageServices.FirstOrDefault()?.SsrFieldDetailsType?.FirstOrDefault()?.SsrFieldValue;
                        decimal.TryParse(ssrFieldValue, out bookingExtraWeight);

                        var fee = orderRetrieveRS.FeeInformation?.FirstOrDefault(f =>
                            f.AncillaryDetailsType != null &&
                            f.AncillaryDetailsType.Any(y => y.AncillaryId == pnrInfoResponseBaggageServices.FirstOrDefault()?.SSRId));

                        if (fee != null)
                        {
                            decimal.TryParse(fee.FeeAmount.ToString(), out bookingExtraBaggagePrice);
                        }
                    }

                    decimal maximumWeight = totalWeightFromCode + totalExtraBaggage;
                    decimal extraBaggageToShow = totalExtraBaggage - bookingExtraWeight;
                    decimal totalPriceToShow = totalPrice - bookingExtraBaggagePrice;

                    /*if (session.CurrentFlow.IsBundleUpgrade)
                    {
                        maximumWeight = bookingExtraWeight + totalExtraBaggage + totalWeightFromCode;
                        extraBaggageToShow = totalExtraBaggage;
                        totalPriceToShow = totalPrice;
                    }*/

                    baggages.Add(new BaseBaggageDTO
                    {
                        Id = IbsUtility.GetFullSsrId(id ?? string.Empty),
                        Name = name ?? "LUGGAGE",
                        DescriptionText = description ?? "CHECKED BAGGAGE",
                        Code = totalWeightFromCode > 0 ? $"{totalWeightFromCode}KG" : NativeAPIAncillaryConstants.EXTRA_BAGGAGE,
                        PassengerIds = new[] { group.Key.PassengerId },
                        TotalPrice = totalPriceToShow,
                        Currency = currency,
                        Count = 1,
                        SegmentIds = group.Key.SegmentIds.Split(','),
                        Status = ssrStatus,
                        FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, ssrSegmentId, stops),
                        ExtraBaggage = extraBaggageToShow,
                        MaximumWeight = maximumWeight
                    });
                }

                return baggages;
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.retrieveBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS?.SSRDetails == null)
                {
                    return new List<BaseBaggageDTO>();
                }

                var baggageGroups = (response as ReservationsPortServiceReference.retrieveBookingResponse)
                    ?.RetrieveBookingRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BAGGAGE && ssr.ssrCode != NativeAPIAncillaryConstants.CABIN_BAGGAGE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .GroupBy(ssr => new
                    {
                        PassengerId = IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()),
                        SegmentIds = string.Join(",", ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString())).OrderBy(x => x))
                    });

                var baggages = new List<BaseBaggageDTO>();

                foreach (var group in baggageGroups)
                {
                    int totalWeightFromCode = 0;
                    int totalExtraBaggage = 0;
                    decimal totalPrice = 0;
                    string? currency = null;
                    string? id = null;
                    string? name = null;
                    string? description = null;
                    string? ssrSegmentId = null;
                    SsrStatusEnum ssrStatus = SsrStatusEnum.NONE;

                    foreach (var ssr in group)
                    {
                        var fee = (response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice += parsedAmount;
                            currency = fee.FeeCurrency;
                        }

                        if (ssr.ssrCode != null)
                        {
                            id = ssr.SSRId.ToString();
                            name = ssr.ssrName;
                            description = ssr.ssrDescription;
                            ssrSegmentId = IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault());
                            ssrStatus = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE;
                            if (ssr.ssrCode.EndsWith("KG"))
                            {
                                string numberPart = new string(ssr.ssrCode.TakeWhile(char.IsDigit).ToArray());
                                int.TryParse(numberPart, out int weightFromCode);
                                totalWeightFromCode += weightFromCode;
                            }
                            else if (ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE)
                            {
                                var ssrFieldValue = ssr.SsrFieldDetailsType?.FirstOrDefault()?.SsrFieldValue;
                                int.TryParse(ssrFieldValue, out int extraBaggage);
                                totalExtraBaggage += extraBaggage;
                            }
                        }
                    }
                    
                    baggages.Add(new BaseBaggageDTO
                    {
                        Id = IbsUtility.GetFullSsrId(id ?? string.Empty),
                        Name = name ?? "LUGGAGE",
                        DescriptionText = description ?? "CHECKED BAGGAGE",
                        Code = totalWeightFromCode > 0 ? $"{totalWeightFromCode}KG" : NativeAPIAncillaryConstants.EXTRA_BAGGAGE,
                        PassengerIds = new[] { group.Key.PassengerId },
                        TotalPrice = 0, //GetPnrInfo ekranında pakete dahil ve satın alınmış extra baggage ücreti görünmemesi için yapıldı
                        Currency = currency,
                        Count = 1,
                        SegmentIds = group.Key.SegmentIds.Split(','),
                        Status = ssrStatus,
                        FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, ssrSegmentId, stops),
                        ExtraBaggage = 0, //GetPnrInfo ekranında pakete dahil ve satın alınmış extra baggage ücreti görünmemesi için yapıldı
                        MaximumWeight = totalWeightFromCode + totalExtraBaggage
                    });
                }

                return baggages;
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveCreateBookingResponse))
            {
                 if ((response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS?.SSRDetails == null)
                {
                    return new List<BaseBaggageDTO>();
                }

                var baggageGroups = (response as ReservationsPortServiceReference.saveCreateBookingResponse)
                    ?.CreateBookingRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BAGGAGE && ssr.ssrCode != NativeAPIAncillaryConstants.CABIN_BAGGAGE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .GroupBy(ssr => new
                    {
                        PassengerId = IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()),
                        SegmentIds = string.Join(",", ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString())).OrderBy(x => x))
                    });

                var baggages = new List<BaseBaggageDTO>();

                foreach (var group in baggageGroups)
                {
                    int totalWeightFromCode = 0;
                    int totalExtraBaggage = 0;
                    decimal totalPrice = 0;
                    string? currency = null;
                    string? id = null;
                    string? name = null;
                    string? description = null;
                    string? ssrSegmentId = null;
                    SsrStatusEnum ssrStatus = SsrStatusEnum.NONE;

                    foreach (var ssr in group)
                    {
                        var fee = (response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice += parsedAmount;
                            currency = fee.FeeCurrency;
                        }

                        if (ssr.ssrCode != null)
                        {
                            id = ssr.SSRId.ToString();
                            name = ssr.ssrName;
                            description = ssr.ssrDescription;
                            ssrSegmentId = IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault());
                            ssrStatus = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE;
                            if (ssr.ssrCode.EndsWith("KG"))
                            {
                                string numberPart = new string(ssr.ssrCode.TakeWhile(char.IsDigit).ToArray());
                                int.TryParse(numberPart, out int weightFromCode);
                                totalWeightFromCode += weightFromCode;
                            }
                            else if (ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE)
                            {
                                var ssrFieldValue = ssr.SsrFieldDetailsType?.FirstOrDefault()?.SsrFieldValue;
                                int.TryParse(ssrFieldValue, out int extraBaggage);
                                totalExtraBaggage += extraBaggage;
                            }
                        }
                    }
                    
                    baggages.Add(new BaseBaggageDTO
                    {
                        Id = IbsUtility.GetFullSsrId(id ?? string.Empty),
                        Name = name ?? "LUGGAGE",
                        DescriptionText = description ?? "CHECKED BAGGAGE",
                        Code = totalWeightFromCode > 0 ? $"{totalWeightFromCode}KG" : NativeAPIAncillaryConstants.EXTRA_BAGGAGE,
                        PassengerIds = new[] { group.Key.PassengerId },
                        TotalPrice = totalPrice,
                        Currency = currency,
                        Count = 1,
                        SegmentIds = group.Key.SegmentIds.Split(','),
                        Status = ssrStatus,
                        FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, ssrSegmentId, stops),
                        ExtraBaggage = totalExtraBaggage,
                        MaximumWeight = totalWeightFromCode + totalExtraBaggage
                    });
                }

                return baggages;
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveModifyBookingResponse))
            {
                 if ((response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS?.SSRDetails == null)
                {
                    return new List<BaseBaggageDTO>();
                }

                var baggageGroups = (response as ReservationsPortServiceReference.saveModifyBookingResponse)
                    ?.SaveModifyBookingRS.SSRDetails
                    .Where(ssr => ssr.ssrType == NativeAPIAncillaryConstants.BAGGAGE && ssr.ssrCode != NativeAPIAncillaryConstants.CABIN_BAGGAGE && ssr.SegmentId.Any(sId => sId == segmentId))
                    .GroupBy(ssr => new
                    {
                        PassengerId = IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()),
                        SegmentIds = string.Join(",", ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString())).OrderBy(x => x))
                    });

                var baggages = new List<BaseBaggageDTO>();

                foreach (var group in baggageGroups)
                {
                    int totalWeightFromCode = 0;
                    int totalExtraBaggage = 0;
                    decimal totalPrice = 0;
                    string? currency = null;
                    string? id = null;
                    string? name = null;
                    string? description = null;
                    string? ssrSegmentId = null;

                    foreach (var ssr in group)
                    {
                        var fee = (response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice += parsedAmount;
                            currency = fee.FeeCurrency;
                        }

                        if (ssr.ssrCode != null)
                        {
                            id = ssr.SSRId.ToString();
                            name = ssr.ssrName;
                            description = ssr.ssrDescription;
                            ssrSegmentId = IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault());
                            if (ssr.ssrCode.EndsWith("KG"))
                            {
                                string numberPart = new string(ssr.ssrCode.TakeWhile(char.IsDigit).ToArray());
                                int.TryParse(numberPart, out int weightFromCode);
                                totalWeightFromCode += weightFromCode;
                            }
                            else if (ssr.ssrCode == NativeAPIAncillaryConstants.EXTRA_BAGGAGE)
                            {
                                var ssrFieldValue = ssr.SsrFieldDetailsType?.FirstOrDefault()?.SsrFieldValue;
                                int.TryParse(ssrFieldValue, out int extraBaggage);
                                totalExtraBaggage += extraBaggage;
                            }
                        }
                    }
                    
                    baggages.Add(new BaseBaggageDTO
                    {
                        Id = IbsUtility.GetFullSsrId(id ?? string.Empty),
                        Name = name ?? "LUGGAGE",
                        DescriptionText = description ?? "CHECKED BAGGAGE",
                        Code = totalWeightFromCode > 0 ? $"{totalWeightFromCode}KG" : NativeAPIAncillaryConstants.EXTRA_BAGGAGE,
                        PassengerIds = new[] { group.Key.PassengerId },
                        TotalPrice = totalPrice,
                        Currency = currency,
                        Count = 1,
                        FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, ssrSegmentId, stops),
                        SegmentIds = group.Key.SegmentIds.Split(','),
                        ExtraBaggage = totalExtraBaggage,
                        MaximumWeight = totalWeightFromCode + totalExtraBaggage
                    });
                }

                return baggages;
            }

            return new List<BaseBaggageDTO>();
        }

        public static List<BaseServiceDTO> GetOtherServices(object response,dynamic itinary, SessionCache session)
        {
            string segmentId = itinary.SegmentId;
            int stops = itinary.stops;
            var knownTypes = IbsUtility.GetExtraServiceCodes();
            
            if (response.GetType() == typeof(confirmPriceResponse))
            {
                if ((response as confirmPriceResponse)?.ConfirmPriceRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var otherServices = (response as confirmPriceResponse)?.ConfirmPriceRS.SSRDetails
                    .Where(ssr => knownTypes.Contains(ssr.ssrCode) && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var feeInformations = (response as confirmPriceResponse)?.ConfirmPriceRS.FeeInformation == null
                            ? new List<PricePortServiceReference.FeeInformationType>()
                            : (response as confirmPriceResponse)?.ConfirmPriceRS.FeeInformation.ToList();
                        var fee = feeInformations?.FirstOrDefault(f =>
                            f.AncillaryDetailsType != null &&
                            f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }
                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            SsrType = (ssr.ssrType ?? "NONE").Replace(" ","_"),
                            PetWeight = ssr.ssrCode == NativeAPIAncillaryConstants.PET_IN_CABIN ? GetPetWeight(ssr.SsrFieldDetailsType) : null,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            ServiceComment = ssr.SSRComments
                        };
                    }).ToList();

                return otherServices;
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.modifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }
            
                var otherServices = (response as ReservationsPortServiceReference.modifyBookingResponse)
                    ?.ModifyBookingRS.SSRDetails
                    .Where(ssr => knownTypes.Contains(ssr.ssrCode) && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            SsrType = (ssr.ssrType ?? "NONE").Replace(" ","_"),
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            ServiceComment = ssr.SSRComments,
                            PetWeight = ssr.ssrCode == NativeAPIAncillaryConstants.PET_IN_CABIN ? GetPetWeight(ssr.SsrFieldDetailsType) : null,
                        };
                    }).ToList();

                return otherServices;
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.retrieveBookingResponse))
            {
                 if ((response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var otherServices = (response as ReservationsPortServiceReference.retrieveBookingResponse)
                    ?.RetrieveBookingRS.SSRDetails
                    .Where(ssr => knownTypes.Contains(ssr.ssrCode) && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            SsrType = (ssr.ssrType ?? "NONE").Replace(" ","_"),
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            ServiceComment = ssr.SSRComments,
                            PetWeight = ssr.ssrCode == NativeAPIAncillaryConstants.PET_IN_CABIN ? GetPetWeight(ssr.SsrFieldDetailsType) : null,
                        };
                    }).ToList();

                return otherServices;
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveCreateBookingResponse))
            {
                 if ((response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var otherServices = (response as ReservationsPortServiceReference.saveCreateBookingResponse)
                    ?.CreateBookingRS.SSRDetails
                    .Where(ssr => knownTypes.Contains(ssr.ssrCode) && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            SsrType = (ssr.ssrType ?? "NONE").Replace(" ","_"),
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            ServiceComment = ssr.SSRComments,
                            PetWeight = ssr.ssrCode == NativeAPIAncillaryConstants.PET_IN_CABIN ? GetPetWeight(ssr.SsrFieldDetailsType) : null,
                        };
                    }).ToList();

                return otherServices;
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveModifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var otherServices = (response as ReservationsPortServiceReference.saveModifyBookingResponse)
                    ?.SaveModifyBookingRS.SSRDetails
                    .Where(ssr => knownTypes.Contains(ssr.ssrCode) && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fee = (response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS
                            .FeeInformation?.FirstOrDefault(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        if (fee != null && decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                        {
                            totalPrice = parsedAmount;
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.ssrCode,
                            SsrType = (ssr.ssrType ?? "NONE").Replace(" ","_"),
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fee?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray(),
                            ServiceComment = ssr.SSRComments,
                            PetWeight = ssr.ssrCode == NativeAPIAncillaryConstants.PET_IN_CABIN ? GetPetWeight(ssr.SsrFieldDetailsType) : null,
                        };
                    }).ToList();

                return otherServices;
            }

            return new List<BaseServiceDTO>();
        }

        public static PetWeight GetPetWeight(object ssrFieldDetails)
        {
            if (ssrFieldDetails == null) return null;

            if (ssrFieldDetails is PricePortServiceReference.SsrFieldDetailsType[] pricePortFields)
            {
                return new PetWeight
                {
                    Weight = pricePortFields
                        .FirstOrDefault(x => x.SsrFieldName == PricePortServiceReference.SsrFieldNameDetails_Type.WEIGHT)
                        ?.SsrFieldValue,
                    WeightType = pricePortFields
                        .FirstOrDefault(x => x.SsrFieldName == PricePortServiceReference.SsrFieldNameDetails_Type.UNITSKGLB)
                        ?.SsrFieldValue
                };
            }

            if (ssrFieldDetails is ReservationsPortServiceReference.SsrFieldDetailsType[] reservationPortFields)
            {
                return new PetWeight
                {
                    Weight = reservationPortFields
                        .FirstOrDefault(x => x.SsrFieldName == ReservationsPortServiceReference.SsrFieldNameDetails_Type.WEIGHT)
                        ?.SsrFieldValue,
                    WeightType = reservationPortFields
                        .FirstOrDefault(x => x.SsrFieldName == ReservationsPortServiceReference.SsrFieldNameDetails_Type.UNITSKGLB)
                        ?.SsrFieldValue
                };
            }

            return null;
        }
        
        public static List<BaseServiceDTO> GetServiceDTOs(object response, string serviceCategory,dynamic itinary, SessionCache session)
        {
            string segmentId = itinary.SegmentId;
            int stops = itinary.stops;

            if (response.GetType() == typeof(confirmPriceResponse))
            {
                if ((response as confirmPriceResponse)?.ConfirmPriceRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var ssrDetails = (response as confirmPriceResponse)?.ConfirmPriceRS?.SSRDetails;
                return ssrDetails
                    .Where(ssr =>
                    {
                        if (serviceCategory != NativeAPIAncillaryConstants.FLEX)
                        {
                            if (ssr.ssrType == serviceCategory && ssr.SubSsrCode == null) 
                            {
                                return !ssrDetails.Any(parentSsr =>
                                    parentSsr.ChildSsrs != null &&
                                    parentSsr.ChildSsrs.Any(child => child.SSRId == ssr.SSRId));
                            }
                        }
                        return true;
                    })
                    .Where(ssr => ssr.ssrType == serviceCategory && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fees = (response as confirmPriceResponse).ConfirmPriceRS.FeeInformation?.Where(f =>
                            f.AncillaryDetailsType != null &&
                            f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        foreach (var fee in fees)
                        {
                            if (decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                            {
                                totalPrice += parsedAmount;
                            }
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.SubSsrCode ?? ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fees.FirstOrDefault()?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }

            if (response.GetType() == typeof(ReservationsPortServiceReference.modifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS?.SSRDetails ==
                    null)
                {
                    return new List<BaseServiceDTO>();
                }
                var ssrDetails = (response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS?.SSRDetails;
                return ssrDetails
                    .Where(ssr =>
                    {
                        if (serviceCategory != NativeAPIAncillaryConstants.FLEX)
                        {
                            if (ssr.ssrType == serviceCategory && ssr.SubSsrCode == null) 
                            {
                                return !ssrDetails.Any(parentSsr =>
                                    parentSsr.ChildSsrs != null &&
                                    parentSsr.ChildSsrs.Any(child => child.SSRId == ssr.SSRId));
                            }
                        }
                        return true;
                    })
                    .Where(ssr => ssr.ssrType == serviceCategory && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fees = (response as ReservationsPortServiceReference.modifyBookingResponse)?.ModifyBookingRS
                            .FeeInformation?.Where(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        foreach (var fee in fees)
                        {
                            if (decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                            {
                                totalPrice += parsedAmount;
                            }
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.SubSsrCode ?? ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fees.FirstOrDefault()?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.retrieveBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var ssrDetails = (response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS?.SSRDetails;
                return ssrDetails
                    .Where(ssr =>
                    {
                        if (serviceCategory != NativeAPIAncillaryConstants.FLEX)
                        {
                            if (ssr.ssrType == serviceCategory && ssr.SubSsrCode == null)
                            {
                                return !ssrDetails.Any(parentSsr =>
                                    parentSsr.ChildSsrs != null &&
                                    parentSsr.ChildSsrs.Any(child => child.SSRId == ssr.SSRId));
                            }
                        }
                        return true;
                    })
                    .Where(ssr => ssr.ssrType == serviceCategory && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fees = (response as ReservationsPortServiceReference.retrieveBookingResponse)?.RetrieveBookingRS
                            .FeeInformation?.Where(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        foreach (var fee in fees)
                        {
                            if (decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                            {
                                totalPrice += parsedAmount;
                            }
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.SubSsrCode ?? ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fees.FirstOrDefault()?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveCreateBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var ssrDetails = (response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS?.SSRDetails;
                return ssrDetails
                    .Where(ssr =>
                    {
                        if (serviceCategory != NativeAPIAncillaryConstants.FLEX)
                        {
                            if (ssr.ssrType == serviceCategory && ssr.SubSsrCode == null)
                            {
                                return !ssrDetails.Any(parentSsr =>
                                    parentSsr.ChildSsrs != null &&
                                    parentSsr.ChildSsrs.Any(child => child.SSRId == ssr.SSRId));
                            }
                        }
                        return true;
                    })
                    .Where(ssr => ssr.ssrType == serviceCategory && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fees = (response as ReservationsPortServiceReference.saveCreateBookingResponse)?.CreateBookingRS
                            .FeeInformation?.Where(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        foreach (var fee in fees)
                        {
                            if (decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                            {
                                totalPrice += parsedAmount;
                            }
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.SubSsrCode ?? ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fees.FirstOrDefault()?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }
            
            if (response.GetType() == typeof(ReservationsPortServiceReference.saveModifyBookingResponse))
            {
                if ((response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS?.SSRDetails == null)
                {
                    return new List<BaseServiceDTO>();
                }

                var ssrDetails = (response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS?.SSRDetails;
                return ssrDetails
                    .Where(ssr =>
                    {
                        if (serviceCategory != NativeAPIAncillaryConstants.FLEX)
                        {
                            if (ssr.ssrType == serviceCategory && ssr.SubSsrCode == null ) 
                            {
                                return !ssrDetails.Any(parentSsr =>
                                    parentSsr.ChildSsrs != null &&
                                    parentSsr.ChildSsrs.Any(child => child.SSRId == ssr.SSRId));
                            }
                        }
                        return true;
                    })
                    .Where(ssr => ssr.ssrType == serviceCategory && ssr.SegmentId.Any(sId => sId == segmentId))
                    .Select(ssr =>
                    {
                        var fees = (response as ReservationsPortServiceReference.saveModifyBookingResponse)?.SaveModifyBookingRS
                            .FeeInformation?.Where(f =>
                                f.AncillaryDetailsType != null &&
                                f.AncillaryDetailsType.Any(y => y.AncillaryId == ssr.SSRId));

                        decimal totalPrice = 0;
                        foreach (var fee in fees)
                        {
                            if (decimal.TryParse(fee.FeeAmount.ToString(), out decimal parsedAmount))
                            {
                                totalPrice += parsedAmount;
                            }
                        }

                        return new BaseServiceDTO
                        {
                            Id = IbsUtility.GetFullSsrId(ssr.SSRId.ToString()),
                            Name = ssr.ssrName,
                            DescriptionText = ssr.ssrDescription,
                            Code = ssr.SubSsrCode ?? ssr.ssrCode,
                            PassengerIds = new[] { IbsUtility.GetFullPassengerID(ssr.GuestId.ToString()) },
                            TotalPrice = totalPrice,
                            Currency = fees.FirstOrDefault()?.FeeCurrency,
                            Count = ssr.NumberOfRequests,
                            FlightIds = IbsUtility.GetFlightTmobIdsBySegmentId(session, IbsUtility.GetFullSegmentId(ssr.SegmentId.FirstOrDefault()), stops),
                            Status = Enum.TryParse<SsrStatusEnum>(ssr.SSRStatus, out var status) ? status : SsrStatusEnum.NONE,
                            SegmentIds = ssr.SegmentId.Select(id => IbsUtility.GetFullSegmentId(id.ToString()))
                                .ToArray()
                        };
                    }).ToList();
            }

            return new List<BaseServiceDTO>();
        }

        public static ServiceDefinitionType GetIbsService(SessionCache session, string serviceId)
        {
            var ibsObject = session
                .CurrentFlow
                .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNotifable)
                .GetServices()
                .FirstOrDefault(f => f.ServiceDefinitionID == serviceId);

            if (ibsObject == null)
            {
                ibsObject = session
                    .CurrentFlow?
                    .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNonNotifable)?
                    .GetServices()?
                    .FirstOrDefault(f => f.ServiceDefinitionID == serviceId);
            }

            return ibsObject;
        }

        public static ServiceDefinitionType GetIbsServiceByEncodingCode(SessionCache session, string encodingCode)
        {
            var ibsObject = session
                .CurrentFlow
                .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNotifable)?
                .GetServices()?
                .FirstOrDefault(f => f.Encoding?.Code?.Value == encodingCode);

            if (ibsObject == null)
            {
                ibsObject = session
                    .CurrentFlow
                    .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNonNotifable)?
                    .GetServices()?
                    .FirstOrDefault(f => f.Encoding?.Code?.Value == encodingCode);
            }

            return ibsObject;
        }

        public static ALaCarteOfferItemType GetIbsServiceOfferItem(SessionCache session, string serviceId)
        {
            var notifableResponse = session
                .CurrentFlow
                .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNotifable);

            var nonNotifableResponse = session
                .CurrentFlow
                .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNonNotifable);


            return notifableResponse.GetOffers().FirstOrDefault(f => f.Service.ServiceDefinitionRef == serviceId) ??
                   nonNotifableResponse.GetOffers().FirstOrDefault(f => f.Service.ServiceDefinitionRef == serviceId);
        }

        public static List<string> FindPremiumBundleMeals(SessionCache session, string segmentKey)
        {
            List<string> codes = new List<string>();
            List<ServiceDefinitionType> services = new List<ServiceDefinitionType>();
            List<ALaCarteOfferItemType> offers = new List<ALaCarteOfferItemType>();
            ServiceDefinitionTypeBookingInstructions bookingInstructions = null;

            var serviceCategories = CachedData.ServiceCodeCategories;

            var bundleServices = session.CurrentFlow.Services.Services[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE]
                .Select(s => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(s))
                .Where(w => w.SegmentIds.Contains(segmentKey));
            foreach (var bundleService in bundleServices)
            {
                var nonNotifResponse =
                    session.CurrentFlow.GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNonNotifable);
                var notifResponse = session.CurrentFlow.GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNotifable);

                services = notifResponse.GetServices();
                offers = notifResponse.GetOffers();
                var bundleObject = services.FirstOrDefault(f => f.ServiceDefinitionID == bundleService.Id);

                bookingInstructions =
                    notifResponse.GetBookingMealInstructions(session, serviceCategories,
                        bundleObject?.ServiceDefinitionID);


                if (bundleObject == null)
                {
                    services = nonNotifResponse.GetServices();
                    offers = nonNotifResponse.GetOffers();
                    bundleObject = services.FirstOrDefault(f => f.ServiceDefinitionID == bundleService.Id);

                    bookingInstructions = nonNotifResponse.GetBookingMealInstructions(session, serviceCategories,
                        bundleObject?.ServiceDefinitionID);
                }
                else
                {
                    if (bookingInstructions == null)
                    {
                        var bundleEncodingCode = bundleObject.Encoding.Code;

                        var nonNotifBundles = nonNotifResponse.GetServices()
                            .Where(f => f.Encoding.Code.Value == bundleEncodingCode.Value);
                        var nonNotifBundle = nonNotifBundles
                            .FirstOrDefault(f =>
                                nonNotifResponse.GetOffers()
                                    .FirstOrDefault(ff => ff.Service?.ServiceDefinitionRef == f.ServiceDefinitionID)
                                    ?.Eligibility?.SegmentRefs.Value?.Contains(segmentKey) == true);

                        bookingInstructions = nonNotifResponse.GetBookingMealInstructions(session, serviceCategories,
                            nonNotifBundle?.ServiceDefinitionID);
                        services = nonNotifResponse.GetServices();
                        offers = nonNotifResponse.GetOffers();
                    }
                }

                if (bookingInstructions != null)
                {
                    var subMealCodes = bookingInstructions.SSRCode?.ToList() ?? new List<string>();

                    foreach (var subMealCode in subMealCodes)
                    {
                        var subMealService = services.FirstOrDefault(w => w.Encoding.Code.Value == subMealCode);

                        if (subMealService != null)
                        {
                            var mealSegment = offers
                                .Where(f => f.Service?.ServiceDefinitionRef == subMealService.ServiceDefinitionID)
                                ?.Select(s => s?.Eligibility?.SegmentRefs?.Value)?.ToList();

                            if (mealSegment?.Any(a => a.Contains(segmentKey)) == true)
                            {
                                codes.Add(subMealService.ServiceDefinitionID);
                            }
                            //codes.Add(subMealService.ServiceDefinitionID);
                        }
                    }
                }
            }

            return codes;
        }

        public static ServiceDefinitionType FindMealParentService(SessionCache session, string serviceId)
        {
            if (serviceId == null) return null;
            var mealService = GetIbsService(session, serviceId) ?? new ServiceDefinitionType();
            if (mealService == null) return null;

            if (mealService?.Encoding?.Code?.Value == null) return null;
            var encodingCode = mealService?.Encoding?.Code?.Value;
            var serviceCategories = CachedData.ServiceCodeCategories;


            var services = session
                .CurrentFlow
                .GetIbsData<ServiceListRS>(IbsDataTypeEnum.ServiceListNonNotifable)
                .GetServices();

            var meals = services
                .Where(f => IbsUtility.GetCategory(serviceCategories, f.Encoding?.Code.Value) ==
                            ServiceCategoryEnum.MEALS);

            var parentMeal = meals.FirstOrDefault(f => f.BookingInstructions?.SSRCode?.Contains(encodingCode) == true);

            if (parentMeal != null)
            {
                return parentMeal;
            }
            else
            {
                return null;
            }
        }

        public static bool LoadSelectedBundleMeals(SessionCache session, ref PassengerFlightMeal passMeal,
            ref ServiceDefinitionType parentMeal, List<string> bundlePassIds, List<string> segmentIds = null,
            List<string> segmentTmobIdValues = null)
        {
            List<string> segmentTmobIds = new List<string>();
            if (segmentTmobIdValues == null || segmentTmobIdValues.Count == 0)
            {
                segmentTmobIds = session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments)
                    .Where(w => segmentIds.Contains(w.Id)).Select(s => s.TmobId).ToList();
                if (segmentTmobIds.Count == 0)
                {
                    segmentTmobIds = session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments)
                        .Where(w => segmentIds.Contains(w.LocalId)).Select(s => s.TmobId).ToList();
                }
            }
            else
            {
                segmentTmobIds = segmentTmobIdValues;
            }

            passMeal = session.CurrentFlow.PNR.Meals.FirstOrDefault(f =>
                bundlePassIds.Contains(f.PassengerId) && segmentTmobIds.Contains(f.SegmentTmobId));
            parentMeal = GeneralMappers.FindMealParentService(session, passMeal?.ServiceId);

            if (parentMeal != null)
            {
                var mealObject = GeneralMappers.GetIbsService(session, passMeal.ServiceId);

                parentMeal.ServiceDefinitionID = passMeal.ServiceId;
                parentMeal.BookingInstructions = new ServiceDefinitionTypeBookingInstructions()
                {
                    SSRCode = new string[] { mealObject.Encoding.Code.Value }
                };
            }

            return passMeal != null && parentMeal != null;
        }

        public static PaymentCardType GetPaymentCard(SessionCache session, PaymentInformation payment, decimal amount,
            SecurePaymentInfomation securePaymentInfomation)
        {
            return new PaymentCardType()
            {
                refs = payment.Installment > 0
                       || (securePaymentInfomation != null && !string.IsNullOrEmpty(securePaymentInfomation.eci))
                    ? "V1_CRD.123456"
                    : null,
                CardCode = IbsUtility.GetCardType(payment.CardNumber),
                CardNumber = new PaymentCardTypeCardNumber()
                {
                    Value = Encryption.DoEncrypt(payment.CardNumber)
                },
                SeriesCode = new PaymentCardTypeSeriesCode()
                {
                    Value = Encryption.DoEncrypt(payment.SeriesCode)
                },
                CardHolderName = new PaymentCardTypeCardHolderName()
                {
                    Value = Encryption.DoEncrypt(payment.CardHolderName)
                },
                CardHolderBillingAddress = new PaymentCardTypeCardHolderBillingAddress()
                {
                    Street = new string[]
                    {
                        payment.Street,
                        payment.Street1,
                    },
                    PostalCode = payment.PostalCode,
                    CountryCode = new CountryCode()
                    {
                        Value = payment.CountryCode
                    },
                    County = payment.Country
                },
                Amount = new CurrencyAmountOptType()
                {
                    Value = amount,
                    Code = session.CurrentFlow.PNR.Currency
                },
                EffectiveExpireDate = new PaymentCardTypeEffectiveExpireDate()
                {
                    Expiration = Encryption.DoEncrypt(payment.Expiration)
                }
            };
        }
    }
}