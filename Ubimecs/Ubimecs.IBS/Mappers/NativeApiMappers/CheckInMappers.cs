using System.Net;
using CheckinPortServiceReference;
using CheckinUtilityPortServiceReference;
using Passbook.Generator;
using Passbook.Generator.Fields;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Configuration;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;
using AirlineCode = CheckinPortServiceReference.AirlineCode;
using ChannelKeyType = CheckinPortServiceReference.ChannelKeyType;

namespace Ubimecs.IBS.Mappers.NativeApiMappers
{
    public static class CheckInMappers
    {
        public static CHK_GetApisRQ MapToGetApisRequest(this CheckInGuestRequest checkInGuestRequest, FlightSegmentDTO iFlyResControlledFlightSegment, SessionCache session, ChannelConfiguration channelConfiguration)
        {
            return new CHK_GetApisRQ()
            {
                AirlineCode = new CheckinPortServiceReference.AirlineCode()
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType()
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },
                FlightIdentifier = new FlightIdentifierType()
                {
                    flightNumber = new FlightNumberType()
                    {
                        fltNumber = iFlyResControlledFlightSegment.FlightNumber,
                        fltSuffix = "*",
                        airlineCode = iFlyResControlledFlightSegment.AirlineId,
                        carrierCode = iFlyResControlledFlightSegment.AirlineId
                    },
                    flightDate = iFlyResControlledFlightSegment.DepartureDate,
                },
                PnrNumber = session.CurrentFlow.PNR.Number.ToString() ?? string.Empty,
                AirportCode = iFlyResControlledFlightSegment.DepartureCode
            };
        }

        public static CHK_RetrieveParametersRQ MapToRetrieveParametersRequest(ChannelConfiguration channelConfig)
        {
            return new CHK_RetrieveParametersRQ
            {
                AirlineCode = new CheckinUtilityPortServiceReference.AirlineCode
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinUtilityPortServiceReference.ChannelKeyType
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfig.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE
                }
            };
        }

        public static CheckInIFlyResControlResponse MapCheckInIFlyResControlResponse(this retrieveParametersResponse response)
        {
            var result = new CheckInIFlyResControlResponse();
            result.FlyResValues = response.GetFlyResParameters();
            result.TimeResValues_V2 = response.GetTimeResParameters_V2();
            result.RestrictedAirports = response.GetRestrictedAirports();
            result.RestrictedSsrValues = response.GetRestrictedSsrs();
            return result;
        }
        
        public static CHK_CheckinGuestRQ MapToCheckInGuestRequest(this CheckInGuestRequest request, SessionCache session, ChannelConfiguration channelConfiguration, string segmentTmobId = null)
        {
            List<string> CheckInIFlyResControlList = CachedData.CheckInIFlyResControl;
            bool IFlyResControlled = false;

            List<CheckinSegmentsType> CheckinSegmentsList = new List<CheckinSegmentsType>();

            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == request.TmobId && !f.State.Equals("Removed"));

            if (flight != null &&
                CheckInIFlyResControlList.Count > 0 &&
                CheckInIFlyResControlList?.Any(x => flight.Segments.Select(k => k.DepartureCode).ToList().Contains(x)) == true)
            {
                IFlyResControlled = true;
            }

            if (IFlyResControlled)
            {
                var item = flight.Segments?.FirstOrDefault(t => t.TmobId == segmentTmobId);
                if (item != null)
                {
                    CheckinSegmentsType checkinSegmentsType = new CheckinSegmentsType()
                    {
                        Segment = new SegmentInfo()
                        {
                            boardPoint = item.DepartureCode,
                            offPoint = item.ArrivalCode
                        },
                        FlightIdentifier = new FlightIdentifierType()
                        {
                            flightDate = item.DepartureDate,
                            flightNumber = new FlightNumberType()
                            {
                                airlineCode = item.AirlineId,
                                carrierCode = item.AirlineId,
                                fltNumber = item.FlightNumber,
                                fltSuffix = "*"
                            }
                        }
                    };
                    CheckinSegmentsList.Add(checkinSegmentsType);
                }

            }
            else
            {
                CheckinSegmentsType checkinSegmentsType = new CheckinSegmentsType()
                {
                    Segment = new SegmentInfo()
                    {
                        boardPoint = flight.DepartureCode,
                        offPoint = flight.ArrivalCode
                    },
                    FlightIdentifier = new FlightIdentifierType()
                    {
                        flightDate = flight.FlightDate ?? DateTime.MinValue,
                        flightNumber = new FlightNumberType()
                        {
                            airlineCode = flight.Segments.FirstOrDefault()?.AirlineId,
                            carrierCode = flight.Segments.FirstOrDefault()?.AirlineId,
                            fltNumber = flight.Segments.FirstOrDefault()?.FlightNumber,
                            fltSuffix = "*"
                        }
                    }
                };
                CheckinSegmentsList.Add(checkinSegmentsType);
            }

            List<CheckinGuestDetailsType> CheckinGuestDetailsType = new List<CheckinGuestDetailsType>();
            foreach (var item in request.PassengerIds)
            {
                var pass = session.CurrentFlow.PnrInfoResponse.PassengerList.FirstOrDefault(f => f.Id == item);
                if (pass.PassengerType != "INF")
                {
                    CheckinGuestDetailsType checkinSegmentsType = new CheckinGuestDetailsType()
                    {
                        NameAndPnrNumber = new NameType()
                        {
                            FirstName = pass.GivenName,
                            LastName = pass.Surname,
                            Gender = ((int)pass.Gender) == 0 ? GenderType.Female : GenderType.Male,
                            GenderSpecified = true,
                            PnrNumber = session.CurrentFlow.PNR.Number,
                            PaxKey = Convert.ToInt64(IbsUtility.ParseFullId(pass.Id)),
                            PaxKeySpecified = true,
                            NumberInParty = IFlyResControlled ? pass.NumberInParty : IbsUtility.ParseFullId(pass.Id) // Lekshminin ilettiği son flowda IFlyResControlled ise (Türkiye içi uçuş) NumberInParty kullanılacak değilse PaxKey gelicek!
                        },
                        CheckinSegments = CheckinSegmentsList != null && CheckinSegmentsList.Count > 0 ? CheckinSegmentsList.ToArray() : null
                    };
                    CheckinGuestDetailsType.Add(checkinSegmentsType);
                }

            }


            return new CHK_CheckinGuestRQ
            {
                AirlineCode = new CheckinPortServiceReference.AirlineCode()
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType()
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE
                },
                CheckinPnrNumber = new CheckinPnrNumberType()
                {
                    PnrNumber = session.CurrentFlow.PNR.Number,
                    CheckinSegments = CheckinSegmentsList != null && CheckinSegmentsList.Count > 0 ? CheckinSegmentsList.ToArray() : null
                },
                CheckinGuestDetails = CheckinGuestDetailsType.Count > 0 ? CheckinGuestDetailsType.ToArray() : null,
                ConnectionIndicator = flight.IsConnectedFlight,
                ConnectionIndicatorSpecified = true
            };
        }

        public static string MapCheckInGuestResponse(this checkinGuestResponse response)
        {
            CHK_CheckinGuestRS checkinGuestRS = response.CHK_CheckinGuestRS;
            try
            {
                if (checkinGuestRS != null && checkinGuestRS.BoardingPass != null)
                {
                    var boardingPasses = checkinGuestRS.BoardingPass;
                    var gateNumber = boardingPasses.FirstOrDefault(t => t.GateNumber != null)?.GateNumber;
                    if (gateNumber != null)
                    {
                        return gateNumber;
                    }
                    return string.Empty;

                }
                return string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
        public static CHK_PrintBoardingPassRQ Map(this PrintBoardingPassRequest request, SessionCache session, ChannelConfiguration channelConfiguration)
        {
            List<string> CheckInIFlyResControlList = CachedData.CheckInIFlyResControl;
            bool IFlyResControlled = false;
            var flightSegment = session.CurrentFlow?.PNR?.Flights?.SelectMany(s => s.Segments)?.FirstOrDefault(f => f.TmobId == request.FlightSegmentTmobId);
            if (flightSegment.DepartureCode != null)
            {
                if (CheckInIFlyResControlList?.Any(x => x.Equals(flightSegment.DepartureCode)) == true)
                {
                    IFlyResControlled = true;
                }
            }
            
            List<NameType> NameTypeList = new List<NameType>();
            if (String.IsNullOrEmpty(request.PassengerId))
            {
                foreach (var item in session.CurrentFlow.PnrInfoResponse.PassengerList)
                {
                    if (item.PassengerType != "INF")
                    {
                        NameType name = new NameType()
                        {
                            FirstName = item.GivenName,
                            LastName = item.Surname,
                            Title = Enum.TryParse(item.NameTitle, out TitleType title) ? title : TitleType.MR,
                            PnrNumber = session.CurrentFlow.PNR.Number,
                            NumberInParty = item.NumberInParty ?? IbsUtility.ParseFullId(item.Id),
                            PaxKey = item.Id == null ? 0 : Convert.ToInt64(IbsUtility.ParseFullId(item.Id)),
                            PaxKeySpecified = (IFlyResControlled || item.Id == null) ? false : true
                        };
                        NameTypeList.Add(name);
                    }
                }
            }
            else
            {
                var passengerPrintBoarding = new PassengerInformationDTO();
                if (request.PassengerId != null && request.PassengerId.Length > 0)
                {
                    passengerPrintBoarding = session.CurrentFlow.PnrInfoResponse.PassengerList.FirstOrDefault(t => t.Id == request.PassengerId);
                }
                if (passengerPrintBoarding.PassengerType != "INF")
                {
                    NameType name = new NameType()
                    {
                        FirstName = passengerPrintBoarding.GivenName,
                        LastName = passengerPrintBoarding.Surname,
                        Title = Enum.TryParse(passengerPrintBoarding.NameTitle, out TitleType title) ? title : TitleType.MR,
                        PnrNumber = session.CurrentFlow.PNR.Number,
                        NumberInParty = passengerPrintBoarding.NumberInParty ?? IbsUtility.ParseFullId(passengerPrintBoarding.Id),
                        PaxKey = passengerPrintBoarding.Id == null ? 0 : Convert.ToInt64(IbsUtility.ParseFullId(passengerPrintBoarding.Id)),
                        PaxKeySpecified = (IFlyResControlled || passengerPrintBoarding.Id == null) ? false : true
                    };
                    NameTypeList.Add(name);
                }
            }
            
            return new CHK_PrintBoardingPassRQ()
            {
                AirlineCode = new CheckinPortServiceReference.AirlineCode()
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType()
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },
                FlightIdentifier = new FlightIdentifierType()
                {
                    flightNumber = new FlightNumberType()
                    {
                        fltNumber = flightSegment.FlightNumber,
                        fltSuffix = "*",
                        airlineCode = GeneralConstants.AIRLINECODE,
                        carrierCode = GeneralConstants.AIRLINECODE
                    },
                    flightDate = flightSegment.DepartureDate,
                },
                Name =  NameTypeList != null && NameTypeList.Count > 0 ? NameTypeList.ToArray() : null,
                Segment = new SegmentInfo()
                {
                    boardPoint = flightSegment.DepartureCode,
                    offPoint = flightSegment.ArrivalCode
                }
            };
        }

        public static CHK_PrintBoardingPassRQ Map(this SmsPrintBoardingPassRequest request,ChannelConfiguration channelConfiguration)
        {
            return new CHK_PrintBoardingPassRQ()
            {
                AirlineCode = new CheckinPortServiceReference.AirlineCode
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = request.Locale ?? GeneralConstants.LOCALE
                },
                WebCheckinID =WebUtility.UrlDecode(request.PaxKey),
                isForSpecifiedFlight = false,
                isForSpecifiedFlightSpecified = true
            };
        }
        
        public static PrintBoardingPassResponse Map(this printBoardingPassResponse response, SessionCache session)
        {
            var result = new PrintBoardingPassResponse();

            if (response == null || response.CHK_PrintBoardingPassRS.BoardingPassInfo == null)
                return result;

            // Her bir BoardingPassInfo nesnesini DTO'ya dönüştür
            foreach (var bpInfo in response.CHK_PrintBoardingPassRS.BoardingPassInfo)
            {
                DateTime boardingTime, arrivalTime, departureTime;
                var boardingPassDTO = new PrintBoardingPassResponse.BoardingPassInfoDTO
                {
                    GuestName = bpInfo.GuestName,
                    FirstName = bpInfo.GuestName?.Split('/').LastOrDefault(),
                    LastName = bpInfo.GuestName?.Split('/').FirstOrDefault(),
                    PnrNumber = bpInfo.PnrNumber,
                    GateNumber = bpInfo.GateNumber,
                    Terminal = bpInfo.Terminal,
                    BoardingTime = DateTime.Parse(bpInfo.DateOfTravel.ToString()).Date,
                    ArrivalTime = bpInfo.ArrivalTime,
                    DepartureTime = bpInfo.DepartureTime,
                    BoardingHour = bpInfo.BoardingTime.ToString(),
                    ArrivalHour = bpInfo.ArrivalTime.ToString("HH:mm"),
                    DepartureHour = bpInfo.DepartureTime.ToString("HH:mm"),
                    Barcode = bpInfo.BarCode,
                    FlightInformation = new PrintBoardingPassResponse.BoardingPassFlightDTO
                    {
                        FlightNumber = bpInfo.FlightNumber?.fltNumber,
                        AirlineCode = bpInfo.FlightNumber?.airlineCode,
                        CarrierCode = bpInfo.FlightNumber?.carrierCode,
                        FlightDate = bpInfo.DateOfTravel,
                    },
                    SeatInformation = new PrintBoardingPassResponse.BoardingPassSeatDTO
                    {
                        OldSeatNumber = bpInfo.SeatNumber?.FirstOrDefault().OldSeatNumber, // TODO : Her seferinde ilk yolcunun bilgisini alıyor, bakılacak
                        NewSeatNumber = bpInfo.SeatNumber?.FirstOrDefault().NewSeatNumber,
                    },
                    BoardpointInformation = new PrintBoardingPassResponse.BoardingPassAirportDTO
                    {
                        AirportCode = bpInfo.BoardPoint?.AirportCode,
                        AirportName = bpInfo.BoardPoint?.AirportName,
                        CityName = bpInfo.BoardPoint?.AirportNameLocal,
                    },
                    OffpointInformation = new PrintBoardingPassResponse.BoardingPassAirportDTO
                    {
                        AirportCode = bpInfo.OffPoint?.AirportCode,
                        AirportName = bpInfo.OffPoint?.AirportName,
                        CityName = bpInfo.OffPoint?.CityNameLocal,
                    }
                };

                result.BoardingPasses.Add(boardingPassDTO);
            }

            return result;
        }

        public static bool RestrictedAirportsControl(SessionCache session, PrintBoardingPassRequest request)
        {
            var restrictedAirportList = CachedData.RestrictedAirportControl;
            var departureCode = request.FlightSegmentTmobId.Split('|').FirstOrDefault();
            if (restrictedAirportList != null)
            {
                return restrictedAirportList.Any(r => r == departureCode);
            }
            return false;
        }

        public static CHK_SendBoardingPassRQ Map(this SendBoardingPassRequest request, SessionCache session, ChannelConfiguration channelConfiguration)
        {
            var flight = session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(f => f.TmobId == request.TmobId);            
            var latestPnrInfoResponse = session?.CurrentFlow?.PnrInfoResponse;
            var passengers = latestPnrInfoResponse?.PassengerList;
            var contact = latestPnrInfoResponse?.ContactList?.FirstOrDefault(c => c.ContactType == "H");
            
            if (flight is null || request.PassengerIds is null || passengers is null)
                return new CHK_SendBoardingPassRQ();

            List<SendBoardingPass> sendBoardingPasses = new();

            if (request.PassengerIds is not null)
            {
                foreach (var passengerId in request.PassengerIds)
                {
                    var passenger = passengers?.FirstOrDefault(p => p.Id == passengerId);
                    List<SendMode> sendModes = new();

                    var sendBoardingPass = new SendBoardingPass
                    {
                        Name = new NameType
                        {
                            FirstName = passenger.GivenName,
                            LastName = passenger.Surname,
                            Title = (TitleType)IbsUtility.MapToNameTitle(passenger.NameTitle),
                            PaxKey = Convert.ToInt64(IbsUtility.ParseFullId(passenger.Id)),
                            PaxKeySpecified = true,
                            PnrNumber = latestPnrInfoResponse?.PNRNumber,
                            NumberInParty = IbsUtility.ParseFullId(passenger.Id)
                        }                        
                    };

                    if (contact != null)
                    {
                        if (request.SendViaSms)
                        {
                            string fullPhoneNumber = $"+{request.PhoneCode}{request.PhoneNumber}";  //$"+{contact.PhoneCode}{contact.PhoneNumber}";
                            sendModes.Add(new SendMode
                            {
                                Mode = "SMS",
                                DestinationAddress = fullPhoneNumber
                            });
                        }

                        if (request.SendViaEmail)
                        {
                            sendModes.Add(new SendMode
                            {
                                Mode = "EMAIL",
                                DestinationAddress = request.Email //contact.EmailAddress
                            });
                        }
                    }

                    if (sendModes.Any())
                    {
                        sendBoardingPass.SendMode = sendModes.ToArray();
                    }
                    sendBoardingPasses.Add(sendBoardingPass);
                }
            }


            if (flight is null) return new CHK_SendBoardingPassRQ();

            CHK_SendBoardingPassRQ sendBoardingPassRQ = new CHK_SendBoardingPassRQ
            {
                AirlineCode = new CheckinPortServiceReference.AirlineCode
                {
                    airlineCode = GeneralConstants.AIRLINECODE,
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType
                {
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },
                Segment = new SegmentInfo
                {
                    boardPoint = flight.DepartureCode,
                    offPoint = flight.ArrivalCode
                },
                SendBoardingPassRequest = sendBoardingPasses.ToArray(),
                FlightIdentifier = new FlightIdentifierType
                {
                    flightNumber = new FlightNumberType
                    {
                        airlineCode = GeneralConstants.AIRLINECODE,
                        carrierCode = GeneralConstants.CARRIERCODE,
                        fltSuffix = "*",
                        fltNumber = flight.Segments.FirstOrDefault().FlightNumber,
                    },
                    flightDate = (DateTime)flight.FlightDate,
                },
                SendAllInOneMail = false,
                SendAllInOneMailSpecified = true
            };


            return sendBoardingPassRQ;
        }
        public static SendBoardingPassResponse Map(this sendBoardingPassResponse response)
        {
            SendBoardingPassResponse sendBoardingPassResponse = new ();
            CHK_SendBoardingPassRS sendBoardingPassRS = response.CHK_SendBoardingPassRS;

            if (sendBoardingPassRS != null)
            {
                if (sendBoardingPassRS.SendBoardingPassResponse != null)
                {
                    foreach (var boardingPassResponseItem in sendBoardingPassRS.SendBoardingPassResponse)
                    {
                        sendBoardingPassResponse.PnrNumber = boardingPassResponseItem.Name.PnrNumber;

                        var passenger = new BoardingPassPassenger
                        {
                            Name = boardingPassResponseItem.Name.FirstName,
                            Surname = boardingPassResponseItem.Name.LastName
                        };

                        if (boardingPassResponseItem.SendStatus.Any())
                        {
                            foreach (var status in boardingPassResponseItem.SendStatus)
                            {
                                if (status.EmailSent != null)
                                {
                                    passenger.IsEmailSend = status.EmailSent == "SUCCESS" ? true : false;
                                }

                                if (status.SMSSent != null)
                                {
                                    passenger.IsSmsSend = status.SMSSent == "SUCCESS" ? true : false;
                                }
                            }
                        }

                        sendBoardingPassResponse.PassengerList.Add(passenger);
                    }
                }
            }

            return sendBoardingPassResponse;
        }
        
        public static List<string> GetFlyResParameters(this retrieveParametersResponse response)
        {
            return new List<string>(response.CHK_RetrieveParametersRS.BusinessParameters.FirstOrDefault(x => x.ParameterCode.Equals(NativeAPICheckinParametersConstants.FLYRES_CONTROLLED_AIRPORTS)).ParameterValue.Split(','));
        }
        
        public static Dictionary<string, string> GetTimeResParameters(this retrieveParametersResponse response)
        {
            var result = new Dictionary<string, string>();
            var paramters = response.CHK_RetrieveParametersRS.BusinessParameters.Where(row => row.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_END_TIME)).ToList();
            foreach (var item in paramters)
            {
                result.Add(item.ParameterCode, item.ParameterValue);
            }
            return result;
        }
        public static List<CheckInTimeResControlDTO> GetTimeResParameters_V2(this retrieveParametersResponse response)
        {
            var result = new List<CheckInTimeResControlDTO>();
            var paramters = response.CHK_RetrieveParametersRS.BusinessParameters.Where(row => row.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_END_TIME) || row.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_START_TIME)).ToList();
            foreach (var item in paramters)
            {
                result.Add(new CheckInTimeResControlDTO
                {
                    AirportCode = item.AirportCode,
                    ParameterCode = item.ParameterCode,
                    ParameterValue = item.ParameterValue
                });
            }
            return result;
        }
        
        public static List<string> GetRestrictedAirports(this retrieveParametersResponse response)
        {
            var paramaterValue = response.CHK_RetrieveParametersRS.BusinessParameters.FirstOrDefault(x =>
                    x.ParameterCode.Equals(NativeAPICheckinParametersConstants.WEB_CHECKIN_RESTRICTED_AIRPORTS))
                ?.ParameterValue;
            if (paramaterValue != null)
            {
                return new List<string>(paramaterValue.Split(','));
            }
            
            return new List<string>();
        }

        public static List<string> GetRestrictedSsrs(this retrieveParametersResponse response)
        {
            var result = new List<string>();
            return new List<string>(response.CHK_RetrieveParametersRS.BusinessParameters.FirstOrDefault(x => x.ParameterCode.Equals(NativeAPICheckinParametersConstants.WEB_CHECKIN_RESTRICTED_SSR_VALUES))?.ParameterValue?.Split(','));
        }

        public static CheckinPortServiceReference.CHK_SearchGuestRQ MapToSearchGuestRequest(SessionCache session, ChannelConfiguration channelConfiguration, SearchGuestRequest request)
        {
            CHK_SearchGuestRQ chkRequest = new CHK_SearchGuestRQ()
            {
                AirlineCode = new CheckinPortServiceReference.AirlineCode()
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType()
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE
                },
                Name = new GuestNameInfo()
                {
                    PnrNumber = session?.CurrentFlow?.PNR?.Number?.ToUpperInvariant() ?? request.PnrNumber?.ToUpperInvariant(),
                    LastName = string.IsNullOrEmpty(request.LastName) ? null : IBS.Utility.IbsUtility.ArrangeTurkishCharacter(request.LastName),
                    FirstName = string.IsNullOrEmpty(request.FirstName) ? null : IBS.Utility.IbsUtility.ArrangeTurkishCharacter(request.FirstName),
                    NumberInParty = string.IsNullOrEmpty(request.NumberInParty) ? null : request.NumberInParty
                },
                BoardPoint = string.IsNullOrEmpty(request.Departure) ? null : request.Departure,
                OffPoint = string.IsNullOrEmpty(request.Arrival) ? null : request.Arrival,
                TourOperatorNumber = string.IsNullOrEmpty(request.TourOperatorNumber) ? null : request.TourOperatorNumber,
                RecordLocator = string.IsNullOrEmpty(request.RecordLocator) ? null : request.RecordLocator,
                IsAllPaxInPNR = true,
                IsAllPaxInPNRSpecified = true
            };

            if (chkRequest.TourOperatorNumber != null)
            {
                chkRequest.Name.Title = (TitleType)((int)request.Title);
            }

            return chkRequest;
        }

        public static CheckinPortServiceReference.CHK_PrintItineraryRQ Map(this PrintItinaryRequest request, ChannelConfiguration channelConfiguration, SessionCache session)
        {
            return new CHK_PrintItineraryRQ()
            {
                
                 AirlineCode= new CheckinPortServiceReference.AirlineCode()
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                ChannelKey = new CheckinPortServiceReference.ChannelKeyType()
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfiguration.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },  
                PnrNumber = request.PnrNumber
            };
        }

        public static PrintItinaryResponse Map(this CheckinPortServiceReference.CHK_PrintItineraryRS response)
        {
            
            return new PrintItinaryResponse
            {
                FirstName = response.NameAndAddressType.Name.FirstName,
                LastName = response.NameAndAddressType.Name.LastName,
                PnrNumber = response.NameAndAddressType.Name.PnrNumber,
                ConfirmationNumber = response.PnrCommonType.ConfirmationNumber,
                OrginalCaller = response.PnrCommonType.OrginalCaller,
                BookingDate = response.PnrCommonType.BookingDate.Date.ToString(),
                
                PnrGuests = response.PNRGuestNameTypes.Select(g => new PnrGuest
                {
                    FirstName= g.GuestName.FirstName,
                    LastName= g.GuestName.LastName,
                    PnrNumber=g.GuestName.LastName
                }).ToList(),
                PnrSegmentDetails = response.PNRSegmentDetailsType.Select(s => new PnrSegmentDetail
                {
                    ArrivalCode = s.ArrivalAirportCode,
                    DepartureCode = s.DepartureAirportCode,
                    FlightIdentifierType = s.FlightIdentifierType,
                    JourneyTime = s.JourneyTime,
                    Stops = s.Stops
                }).ToList(),
                PnrFareDetails = response.PNRFareDetailsType.Select(pf => new PnrFareDetail
                {
                    FareCode = pf.BaseFareCodes.FareCode,
                    Total = pf.Total
                }).ToList()
            };
        }
        
        public static CHK_UncheckGuestRQ Map(this CancelCheckInRequest request, SessionCache session, ChannelConfiguration channelConfig)
        {
            if (request == null || session?.CurrentFlow?.PNR == null)
                return new CHK_UncheckGuestRQ();

            var flight = session.CurrentFlow.PNR.Flights
                .FirstOrDefault(x => x.TmobId == request.TmobId);
            
            if (flight == null)
                return new CHK_UncheckGuestRQ();

            var firstSegment = flight.Segments.FirstOrDefault();
            if (firstSegment == null)
                return new CHK_UncheckGuestRQ();

            var passengerIds = request.PassengerIds.ToList();

            var passengers = session.CurrentFlow.PNR.Passengers
                .Where(x => passengerIds.Contains(x.Id))
                .ToList();

            var pnrNumber = session.CurrentFlow.PNR.Number;

            return new CHK_UncheckGuestRQ
            {
                ChannelKey = new ChannelKeyType
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    ChannelCode = channelConfig.ChannelCode,
                },
                AirlineCode = new AirlineCode
                {
                    airlineCode = GeneralConstants.AIRLINECODE
                },
                Segment = new SegmentInfo
                {
                    boardPoint = flight.DepartureCode,
                    offPoint = flight.ArrivalCode
                },
                FlightIdentifier = new FlightIdentifierType
                {
                    flightNumber = new FlightNumberType
                    {
                        fltNumber = firstSegment.FlightNumber,
                        fltSuffix = "*",
                        airlineCode = firstSegment.AirlineId,
                        carrierCode = firstSegment.AirlineId
                    },
                    flightDate = flight.FlightDate ?? DateTime.Now
                },
                NameAndPnrNumber = passengers.Select(passenger => new NameType
                {
                    FirstName = passenger.Name,
                    LastName = passenger.Surname,
                    PnrNumber = pnrNumber,
                    PaxKey = Convert.ToInt64(IbsUtility.ParseFullId(passenger.Id)),
                    PaxKeySpecified = true,
                    NumberInParty = "1"
                }).ToArray()
            };
        }  
        
         public static PassGeneratorRequest MapAddToWalletRequest(AddToWalletRequest request, SessionCache session)
        {
             PassGenerator generator = new PassGenerator();
             PassGeneratorRequest passGeneratorRequest = new PassGeneratorRequest();
             var boardingPasses = session.CurrentFlow.BoardingPasses.Where(f => f.SegmentTmobId == request.FlightSegmentTmobId).ToList();
             PrintBoardingPassResponse.BoardingPassInfoDTO boardingPassInfo = new PrintBoardingPassResponse.BoardingPassInfoDTO();
             foreach (var pass in boardingPasses)
             {
                 var boardingPass = pass.BoardingPasses.FirstOrDefault(p => p.PaxKey == request.PassengerId);
                 if (boardingPasses.Count > 0)
                     boardingPassInfo = boardingPass;
             }

             var segmentInfo = session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == request.FlightSegmentTmobId);
             var ifeAccessCode = session.CurrentFlow?.PNR?.Passengers?.FirstOrDefault(t => t.Id == request.PassengerId)?.IFEAccessCode;


             passGeneratorRequest.PassTypeIdentifier = GeneralConstants.PASS_TYPE_IDENTIFIER; //"pass.com.freebirdtesttmob";
             passGeneratorRequest.TeamIdentifier = GeneralConstants.TEAM_IDENTIFIER; //"YE3WJUT986";
             passGeneratorRequest.SerialNumber = Guid.NewGuid().ToString();
             passGeneratorRequest.Description = "Freebird";
             passGeneratorRequest.OrganizationName = "Freebird Airlines";

             passGeneratorRequest.BackgroundColor = "rgb(255,255,255)";
             passGeneratorRequest.LabelColor = "rgb(15, 68, 130)";
             passGeneratorRequest.ForegroundColor = "rgb(0,0,0)";


             string appleCertPath = GeneralConstants.ADD_TO_WALLET_FILE_PATH + GeneralConstants.ADD_TO_WALLET_FILE_NAME; //"/Users/<USER>/flightcertificates/AddToWallet/PassTypeCertificatesBoardingPass.p12";
             string passbookRootPath = GeneralConstants.ADD_TO_WALLET_FILE_PATH; //"/Users/<USER>/flightcertificates/AddToWallet/";
                 
             passGeneratorRequest.AppleWWDRCACertificate = new System.Security.Cryptography.X509Certificates.X509Certificate2(File.ReadAllBytes(passbookRootPath + 
                 GeneralConstants.ADD_TO_WALLET_APPLE_CERT_NAME));
             passGeneratorRequest.PassbookCertificate = new System.Security.Cryptography.X509Certificates.X509Certificate2(File.ReadAllBytes(appleCertPath), GeneralConstants.ADD_TO_WALLET_P12_PASSWORD);
             passGeneratorRequest.Images.Add(PassbookImage.Icon, System.IO.File.ReadAllBytes(passbookRootPath + "icon.png"));
             passGeneratorRequest.Images.Add(PassbookImage.Icon2X, System.IO.File.ReadAllBytes(passbookRootPath + "<EMAIL>"));

             passGeneratorRequest.Images.Add(PassbookImage.Logo, System.IO.File.ReadAllBytes(passbookRootPath + "freebirdLogo.png"));
             passGeneratorRequest.Images.Add(PassbookImage.Logo2X, System.IO.File.ReadAllBytes(passbookRootPath + "<EMAIL>"));

             var passengerIFEAccessCode = string.Empty;


             if (request.PassengerId != null && request.PassengerId.Length > 0)
             {
                 passengerIFEAccessCode = ifeAccessCode;
             }

             passGeneratorRequest.Style = PassStyle.BoardingPass;

             passGeneratorRequest.AddHeaderField(new StandardField("gate", "GATE", boardingPassInfo.GateNumber ?? ""));
             /*if (segmentInfo != null)
             {
                 passGeneratorRequest.AddHeaderField(new StandardField("date", "", segmentInfo.DepartureDate.ToString("dd.MM.yyyy")));

             }
             else
             {
                 passGeneratorRequest.AddHeaderField(new StandardField("date", "", ""));
             }   */         
             passGeneratorRequest.AddPrimaryField(new StandardField("origin", boardingPassInfo.BoardpointInformation?.AirportName ?? "", boardingPassInfo.BoardpointInformation?.AirportCode ?? ""));
             passGeneratorRequest.AddPrimaryField(new StandardField("destination", boardingPassInfo.OffpointInformation?.AirportName ?? "", boardingPassInfo.OffpointInformation?.AirportCode ?? ""));

             if (boardingPassInfo.FlightInformation != null)
             {
                 passGeneratorRequest.AddAuxiliaryField(new StandardField("flight", "FLIGHT", boardingPassInfo.FlightInformation?.AirlineCode + " " + boardingPassInfo.FlightInformation?.FlightNumber));
             }

             if (segmentInfo != null)
             {
                 passGeneratorRequest.AddAuxiliaryField(new StandardField("date", "DATE", segmentInfo.DepartureDate.ToString("dd MMM").ToUpper()));

             }
             else
             {
                 passGeneratorRequest.AddAuxiliaryField(new StandardField("date", "", "-"));

             }

             passGeneratorRequest.AddAuxiliaryField(new StandardField("boarding", "BOARDING", boardingPassInfo.BoardingTime.ToString("HH:mm")));           
             
             passGeneratorRequest.AddAuxiliaryField(new StandardField("departure_arrival", "DEPART/ARRIVE", boardingPassInfo.DepartureTime.ToString("HH:mm")+"/"+boardingPassInfo.ArrivalTime.ToString("HH:mm")));
             
             passGeneratorRequest.AddSecondaryField(new StandardField("passenger-name", "PASSENGER", boardingPassInfo.GuestName ?? ""));

             if (!string.IsNullOrEmpty(passengerIFEAccessCode))
             {
                 passGeneratorRequest.AddAuxiliaryField(new StandardField("seat", "SEAT", boardingPassInfo.SeatInformation.NewSeatNumber ?? ""));
             }

             //passGeneratorRequest.AddAuxiliaryField(new StandardField("zone", "GROUP", boardingPassInfo.Zone ?? ""));
             //passGeneratorRequest.AddSecondaryField(new StandardField("passenger-name", "PASSENGER", boardingPassInfo.GuestName ?? ""));


             //passGeneratorRequest.AddSecondaryField(new StandardField("terminal", "TERMINAL", boardingPassInfo.Terminal ?? ""));
             //passGeneratorRequest.AddSecondaryField(new StandardField("seq", "SEQ", boardingPassInfo.SequenceNumber ?? ""));
             if (string.IsNullOrEmpty(passengerIFEAccessCode))
             {
                 passGeneratorRequest.AddSecondaryField(new StandardField("seat", "SEAT", boardingPassInfo.SeatInformation.NewSeatNumber ?? ""));
             }          
             if (!string.IsNullOrEmpty(passengerIFEAccessCode))
             {
                 passGeneratorRequest.AddSecondaryField(new StandardField("ife-access-code", "INFLIGHT ENTERTAINMENT", passengerIFEAccessCode ?? ""));
             }

             DateTimeOffset relevantDate = segmentInfo.DepartureDate.Date.Add(DateTime.Parse(segmentInfo.DepartureTime).TimeOfDay);
             var country = CachedData.GetCountryCodeByAirportCode(boardingPassInfo.BoardpointInformation?.AirportCode);
             var offsetValue = Ubimecs.Infrastructure.Utilities.Utility.GetOffsetTimeForUtc(country);
             DateTimeOffset relevantDateChanged = relevantDate.ToOffset(new TimeSpan(offsetValue, 0, 0));
             DateTimeOffset relevateDateDateChanged = relevantDateChanged.AddHours(3 - offsetValue);

             //passGeneratorRequest.RelevantDate = segmentInfo.DepartureDate.Date.Add(DateTime.Parse(segmentInfo.DepartureTime).TimeOfDay);
             passGeneratorRequest.RelevantDate = relevateDateDateChanged;

             passGeneratorRequest.TransitType = TransitType.PKTransitTypeAir;

             passGeneratorRequest.AddBarcode(BarcodeType.PKBarcodeFormatAztec, boardingPassInfo.Barcode, "ISO-8859-1");        

             return passGeneratorRequest;
 }

    }
}