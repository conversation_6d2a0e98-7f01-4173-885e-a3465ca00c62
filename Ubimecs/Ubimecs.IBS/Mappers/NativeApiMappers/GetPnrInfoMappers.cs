using System.Text;
using PricePortServiceReference;
using ReservationsPortServiceReference;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.Mappers.RequestMappers;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.DTO.Payment;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.Other;
using Ubimecs.Infrastructure.Models.PNR;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;
using Ubimecs.Infrastructure.Models.Response.Agency;
using BookerDetailsType = ReservationsPortServiceReference.BookerDetailsType;
using BookingChannelType = ReservationsPortServiceReference.BookingChannelType;
using CardDetails_Type = ReservationsPortServiceReference.CardDetails_Type;
using CommentCode_Type = ReservationsPortServiceReference.CommentCode_Type;
using CommentDetailsType = ReservationsPortServiceReference.CommentDetailsType;
using ContactDetails_Type = ReservationsPortServiceReference.ContactDetails_Type;
using FlightSegmentDetailsType = ReservationsPortServiceReference.FlightSegmentDetailsType;
using GenderDetails_Type = ReservationsPortServiceReference.GenderDetails_Type;
using GroupPaxDetailsType = ReservationsPortServiceReference.GroupPaxDetailsType;
using GuestPaymentInfoType = ReservationsPortServiceReference.GuestPaymentInfoType;
using GuestReponseDetailsType = ReservationsPortServiceReference.GuestReponseDetailsType;
using PnrContactType = ReservationsPortServiceReference.PnrContactType;
using ReservationStatusDetails_Type = ReservationsPortServiceReference.ReservationStatusDetails_Type;
using TravelDocumentsType = ReservationsPortServiceReference.TravelDocumentsType;

namespace Ubimecs.IBS.Mappers.NativeApiMappers
{
    public static class GetPnrInfoMappers
    {
        //TODO: Default Check-In saati configden gelmeli
        private static int SeventyTwoHoursInMinutes = 72 * 60;
        private static int OneHourInMinutes = 1 * 60;
        public static RetrieveBookingRQ MapPnrInfoRequest(this GetPnrInfoRequest request, ChannelConfiguration channelConfig, SessionCache session)
        {
            RetrieveBookingRQ retrieveBookingRq = new RetrieveBookingRQ();
            retrieveBookingRq.AirlineCode = GeneralConstants.AIRLINECODE;
            retrieveBookingRq.BookingChannel = new BookingChannelType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfig.ChannelCode, //GeneralConstants.BOOKING_CHANNEL_CODE,
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            };
            
            if (!string.IsNullOrEmpty(request.TicketNumber))
            {
                retrieveBookingRq.TicketNumber = request.TicketNumber;
                //retrieveBookingRq.GivenName = request.GivenName;
                //retrieveBookingRq.SurName = request.Surname;
            }
            else
            {
                retrieveBookingRq.PnrNumber = request.OrderId;
                retrieveBookingRq.SurName = request.Surname;
            }
            return retrieveBookingRq;
        }
        public static GetPnrInfoResponse MapPnrInfoResponse(object response, SessionCache session)
        {
            GetPnrInfoResponse result = new GetPnrInfoResponse();
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                session.CurrentFlow.SaveIbsData((response as retrieveBookingResponse).RetrieveBookingRS,
                    IbsDataTypeEnum.OrderRetrieve);
            }
            
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                session.CurrentFlow.SaveIbsData((response as saveCreateBookingResponse).CreateBookingRS, IbsDataTypeEnum.OrderRetrieve);
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                session.CurrentFlow.SaveIbsData((response as saveModifyBookingResponse).SaveModifyBookingRS, IbsDataTypeEnum.OrderRetrieve);
            }

            
            var contactList = GetContactList(response);
            var travelDocuments = GetTravelDocuments(response);
            var passengerList = GetPassengerList(response, travelDocuments);
            var pnrContact = contactList.FirstOrDefault(x => x.ContactType != "E");
            var bookerDetails = GetBookerDetails(response);
            var reservationInfo = GetReservationInfo(response);
            var paymentDetails = GetPaymentDetails(response, session);
            var priceInfo = PriceMappers.MapToPriceInfo(response, session);
            var invoiceDetails = GetInvoiceDetails(response);

            var itinary = (IEnumerable<dynamic>)(GetItinary(response));
            var allServices = GeneralMappers.GetAllServicesNative(response, session, itinary);

            result.ContactId = null;
            result.ContactType = pnrContact.ContactType;
            result.PhoneNumber = pnrContact.PhoneNumber;
            result.PhoneCountryCode = pnrContact.PhoneCode;
            result.EmailAddress = pnrContact.EmailAddress;
            result.Title = passengerList.FirstOrDefault()?.NameTitle ?? string.Empty;
            result.Name = passengerList.FirstOrDefault()?.GivenName ?? string.Empty;
            result.Surname = passengerList.FirstOrDefault()?.Surname ?? string.Empty;
            result.PNRNumber = reservationInfo.PnrNumber;
            result.PnrType = IbsUtility.DeterminePnrType(response);
            result.StatusCode = reservationInfo.PnrStatus;
            result.ContactList = contactList;
            result.PassengerList = passengerList;
            result.Services = allServices;
            result.TravelDocuments = travelDocuments;
            result.TotalAmount = reservationInfo.TotalAmountPaid.ToString();
            result.Currency = reservationInfo.Currency;           
            result.PaymentDetails = paymentDetails;
            result.PriceInfo = priceInfo;
            result.InvoiceDetails = invoiceDetails ?? new InvoiceInformation();
            var flightDestinationList = GetFlightDestinationList(response, result, session);
            result.FlightDestionationList = flightDestinationList;

            var farePaidStatus = GetFarePaidStatusFromResponse(response, session);
            if (farePaidStatus.HasValue)
            {
                result.FarePaidStatus = ParseFarePaidStatus(farePaidStatus.Value);
            }

            session.CurrentFlow.PNR.Number = result.PNRNumber;
            session.CurrentFlow.PNR.Currency = result.Currency;
            
            MapSegmentPricesToFlights(response, itinary, flightDestinationList, session);
            result.PriceInfo.TaxesAndSurcharges = PriceMappers.MapToTaxesAndSurcharges(flightDestinationList);
            
            var isFirstSegmentFlown = IsFirstSegmentFlown(flightDestinationList);
            result.IsNameChangeButtonDisabled = isFirstSegmentFlown || result.PnrType != PnrTypeEnum.Direct;
            
            if (result.StatusCode == "ACTIVE" && session.CurrentFlow.Type == Ubimecs.Infrastructure.Models.Flow.FlowType.Booking)
            {
                session.CurrentFlow.FlowFinished = true;
            }

            string pnrType = GetPnrType(response);
            if (session.CurrentFlow.BookingType == BookingTypeEnum.GroupBooking || pnrType == "GROUP")
            {
                var pnrComment = GetPnrComment(response);
                var groupDetails = GetGroupDetails(response);
                result.IsGroupPnr = true;
                result.GroupPnrStatus = IbsUtility.MapToGroupBookingStatus(itinary, reservationInfo.PnrStatus);
                result.GroupInformation = groupDetails;
                result.GroupPnrComment = pnrComment;
                result.GroupRequestDate = GetGroupRequestDate(response);
                result.GroupPnrActionButtons = GetGroupPnrActionButtons(response);
                result.GroupPnrFopType = GetFopType(response);
                result.TimeLimitDetails = GetGroupPnrTimeLimitDetails(response);
            }
            return result;
        }

        public static void MapSegmentPricesToFlights(
            object response,
            IEnumerable<dynamic> itinerary,
            List<OrderRetrieveFlightDestination> flights,
            SessionCache session)
        {
            if (flights == null || itinerary == null) return;

            string currencyCode = session.CurrentFlow.PNR.Currency;

            foreach (var segment in itinerary)
            {
                string segmentId = TryGetSegmentId(segment);
                if (string.IsNullOrEmpty(segmentId)) continue;

                var matchingFlight = FindFlightBySegmentId(flights, segmentId);
                if (matchingFlight == null) continue;

                matchingFlight.Prices = PriceMappers.GetPassengerAndFeePrices(response, segment, currencyCode, session);
            }
        }
        
        private static string TryGetSegmentId(dynamic segment)
        {
            try
            {
                return segment?.SegmentId?.ToString();
            }
            catch
            {
                return null;
            }
        }
        private static OrderRetrieveFlightDestination FindFlightBySegmentId(List<OrderRetrieveFlightDestination> flights, string segmentId)
        {
            return flights.FirstOrDefault(f =>
                f?.Segments != null && f.Segments.Any(s => s.Id == IbsUtility.MapToSegmentId(segmentId)));
        }
        public static OfferRebookingStateEnum MapState(string segmentStatus)
        {
            return segmentStatus switch
            {
                "CANCELLED" => OfferRebookingStateEnum.Removed,
                "CONFIRMED" => OfferRebookingStateEnum.Added,
                _ => OfferRebookingStateEnum.NotChanged
            };
        }
        public static RetrieveBookingRQ MapTourOperatorPnrRequest(this GetTourOperatorPnrInfoRequest request, ChannelConfiguration channelConfig, SessionCache session)
        {
            var firstFlight = request.Flights?.FirstOrDefault();
            var departureDate = firstFlight?.DepartureDate;
            
            RetrieveBookingRQ retrieveBookingRq = new RetrieveBookingRQ
            {
                AirlineCode = GeneralConstants.AIRLINECODE,
                BookingChannel = new BookingChannelType
                {
                    ChannelType = GeneralConstants.CHECKIN_CHANNEL_TYPE,
                    Channel = channelConfig.ChannelCode, //GeneralConstants.CHECKIN_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },
                GivenName = request.Name,
                SurName = request.Surname,
                FlightDate = departureDate ?? DateTime.MinValue,
                FlightDateSpecified = departureDate.HasValue,
                BoardPoint = firstFlight?.DepartureAirport ?? request.DepartureCode,
                OffPoint = firstFlight?.ArrivalAirport ?? request.ArrivalCode,
                TourOperatorBookingNo = request.TourOpearatorNumber
            };

            return retrieveBookingRq;
        }
        public static List<ContantInformation> GetContactList(object response)
        {
            var responseContactInfo = new List<PnrContactType>();
            var contactList = new List<Ubimecs.Infrastructure.Models.DTO.Passenger.ContantInformation>();
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                responseContactInfo = ((retrieveBookingResponse)response).RetrieveBookingRS.PnrContact.ToList();
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                responseContactInfo = ((saveCreateBookingResponse)response).CreateBookingRS.PnrContact.ToList();
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                responseContactInfo = ((modifyBookingResponse)response).ModifyBookingRS.PnrContact.ToList();
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                responseContactInfo = ((saveModifyBookingResponse)response).SaveModifyBookingRS.PnrContact.ToList();
            }

            if (responseContactInfo.Any())
            {
                var pnrType = IbsUtility.DeterminePnrType(response);
                var filteredContactInfo = responseContactInfo;

                // Sadece ServiceBooking için ContactType = PAX_HOME_CONTACT filtrele
                if (pnrType == PnrTypeEnum.ServiceBooking)
                {
                    filteredContactInfo = responseContactInfo
                        .Where(c => c.ContactType == ContactDetails_Type.H)
                        .ToList();
                }
                
                contactList = filteredContactInfo.Select(c =>
                {
                    var (givenName, surname) = IbsUtility.SplitName(c?.SurName ?? string.Empty, c?.SurName ?? string.Empty);

                    return new ContantInformation
                    {
                        ContactType = c?.ContactType.ToString() ?? string.Empty,
                        NameTitle = c?.NamePrefix.ToString() ?? string.Empty,
                        Surname = surname,
                        GivenName = givenName,
                        PhoneNumber = c?.Address?.PhoneNumber,
                        PhoneCode = c?.Address?.PhoneNumberCountryCode,
                        EmailAddress = c?.Address?.EmailAddress,
                        IsSmsNotificationEnabled = c?.Address?.SendItineraryToSMS ?? false,
                        PostalAddress = new PostalAddress
                        {
                            CityName = c?.Address?.City,
                            CountryCode = c?.Address?.CountryName?.Length < 3 ? c?.Address?.CountryName :
                                    !string.IsNullOrEmpty(c?.Address?.CountryName) ? 
                                    IbsUtility.GetCountryCodeByName(Infrastructure.Utilities.Utility.ConvertCapitalLetter(c.Address.CountryName)) : null,
                            PostalCode = c?.Address?.ZipCode,
                            Street = new string[]
                                {
                                    c?.Address?.Address1,
                                    c?.Address?.Address2,
                                    c?.Address?.Address3
                                }
                                .Where(street => !string.IsNullOrEmpty(street))
                                .ToArray(),
                            Label = c?.Address?.AddressType1 ?? string.Empty
                        }
                    };
                }).ToList();
            }
            
            return contactList;
        }
        public static List<PassengerInformationDTO> GetPassengerList(object response, List<TravelDocumentInformationDTO>? travelDocuments)
        {
            var responsePassengerInfo = new List<GuestReponseDetailsType>();
            var passengerList = new List<PassengerInformationDTO>();
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                responsePassengerInfo = ((retrieveBookingResponse)response).RetrieveBookingRS.GuestDetails.ToList();
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                responsePassengerInfo = ((saveCreateBookingResponse)response).CreateBookingRS.GuestDetails.ToList();
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                responsePassengerInfo = ((modifyBookingResponse)response).ModifyBookingRS.GuestDetails.ToList();
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                responsePassengerInfo = ((saveModifyBookingResponse)response).SaveModifyBookingRS.GuestDetails.ToList();
            }

            if (responsePassengerInfo.Any())
            {
                passengerList = responsePassengerInfo.Select(g =>
                {
                    var paxTicketDeils = g.PaxTicketDetails?.FirstOrDefault();
                    var travelDocument = travelDocuments?.FirstOrDefault(x => x.PassengerID == IbsUtility.GetFullPassengerID(g.GuestId));

                    return new PassengerInformationDTO
                    {
                        Id = IbsUtility.GetFullPassengerID(g.GuestId),
                        PassengerType = IbsUtility.MapPaxTypeToEnum(g.GuestType.ToString()).ToString(),
                        Birthdate = g.DateOfBirth,
                        Birthplace = travelDocument?.BirthPlace ?? string.Empty,
                        Gender = g.Gender == GenderDetails_Type.M ? GenderTypeEnum.Male : GenderTypeEnum.Female,
                        NameTitle = g.NamePrefix.ToString(),
                        GivenName = g.GivenName,
                        Surname = g.SurName,
                        FamilyId = g.FamilyId,
                        InfantId = null,
                        ParentGuestID = g.ParentGuestID != null ? IbsUtility.GetFullPassengerID(g.ParentGuestID) : null,
                        PaybackAccountNumber = null, //TODO: burası konusulacak
                        PaybackCouponNumber = null,
                        CountryCode = null,
                        IdentityNumber = (travelDocument?.DocumentType == "IDENTITY CARD" ? travelDocument?.DocumentNumber : null) ?? null,
                        MemberShipNumber = null,
                        NumberInParty = null,
                        ETicketNumber = paxTicketDeils?.TicketNumber ?? null,
                        HesCode = null,
                        DocumentNumber = travelDocument?.DocumentNumber ?? string.Empty,
                        DocumentType = travelDocument?.DocumentType ?? string.Empty,
                        DateOfExpiry = travelDocument?.DateOfExpiry ?? DateTime.MinValue,
                        IssuingCountry = travelDocument?.IssuingCountry ?? string.Empty,
                        CitizenshipCountryCode =
                                        string.IsNullOrEmpty(travelDocument?.CitizenshipCountryCode)
                                        ? (travelDocument?.ResidenceCountryCode ?? string.Empty)
                                        : (travelDocument.CitizenshipCountryCode.Length == 2
                                            ? travelDocument.CitizenshipCountryCode
                                            : IbsUtility.GetCountryTwoLetterCodeByThreeLetterCode(travelDocument.CitizenshipCountryCode)),
                        ResidenceCountryCode = travelDocument?.ResidenceCountryCode ?? string.Empty,
                        VisaNo = travelDocument?.VisaNo ?? string.Empty,
                        VisaIssuedBy = travelDocument?.VisaIssuedBy ?? string.Empty,
                        IFEAccessCode = null,
                    };

                }).ToList();
            }

            return passengerList;
        }
        public static List<TravelDocumentInformationDTO> GetTravelDocuments(object response)
        {
            var travelDocuments = new List<TravelDocumentsType>();
            var mappedTravelDocuments = new List<TravelDocumentInformationDTO>();
            
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                if (((retrieveBookingResponse)response).RetrieveBookingRS.TravelDocuments != null)
                {
                    travelDocuments = ((retrieveBookingResponse)response).RetrieveBookingRS.TravelDocuments.ToList(); 
                }
                
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                if (((saveCreateBookingResponse)response).CreateBookingRS.TravelDocuments != null)
                {
                    travelDocuments = ((saveCreateBookingResponse)response).CreateBookingRS.TravelDocuments.ToList();
                }
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                if (((modifyBookingResponse)response).ModifyBookingRS.TravelDocuments != null)
                {
                    travelDocuments = ((modifyBookingResponse)response).ModifyBookingRS.TravelDocuments.ToList();
                }
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                if (((saveModifyBookingResponse)response).SaveModifyBookingRS.TravelDocuments != null)
                {
                    travelDocuments =
                        ((saveModifyBookingResponse)response).SaveModifyBookingRS.TravelDocuments.ToList();
                }
            }
            if (travelDocuments.Any())
            {
                mappedTravelDocuments = travelDocuments.Select(td => new TravelDocumentInformationDTO
                {
                    PassengerID = IbsUtility.GetFullPassengerID(td.GuestId.ToString()),
                    Gender = Enum.TryParse<GenderTypeEnum>(td.Gender, out var genderResult) ? genderResult : null,
                    GivenName = td.FirstName,
                    Surname = td.LastName,
                    DocumentNumber = td.TravelDocumentNumber,
                    DocumentType = IbsUtility.DocumentTypeByValue(td.TravelDocumentType),
                    DateOfExpiry = td.TravelDocumentexpirydate,
                    BirthDate = td.DateOfBirth,
                    BirthPlace = td.PlaceOfBirth,
                    IssuingCountry = CachedData.GetCountrCode(td.TravelDocumentCountryOfIssue),
                    IBSIssuingCountry = CachedData.GetCountrCode(td.TravelDocumentCountryOfIssue),
                    CitizenshipCountryCode = CachedData.GetCountrCode(td.Nationality),
                    IBSCitizenshipCountryCode = CachedData.GetCountrCode(td.Nationality),
                    IBSResidenceCountryCode = CachedData.GetCountrCode(td.Residence?.Country) ?? string.Empty,
                    ResidenceCountryCode = CachedData.GetCountrCode(td.Residence?.Country) ?? string.Empty,
                    VisaNo = "",
                    MemberShipNumber = "", // TODO: burası konusulacak
                    VisaIssuedBy = td.PlaceForVisa,
                    HesCode = null,
                    TravelDocumentId = td.TravelDocumentId.ToString(),
                }).ToList();
            }
            
            return mappedTravelDocuments;
        }
        public static List<GuestPaymentInfoDTO> GetPaymentDetails(object response, SessionCache session)
        { 
            var guestPaymentInfos = new List<GuestPaymentInfoType>();
            var guestPaymentInfoDtos = new List<GuestPaymentInfoDTO>();
            double paymentAmount = 0;
            var paymentCurrency = string.Empty;
            
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                if (((retrieveBookingResponse)response).RetrieveBookingRS.GuestPaymentInfo != null)
                {
                    guestPaymentInfos = ((retrieveBookingResponse)response).RetrieveBookingRS.GuestPaymentInfo.ToList();
                    paymentAmount = ((retrieveBookingResponse)response).RetrieveBookingRS.TotalAmountPaid.amount;
                    paymentCurrency = ((retrieveBookingResponse)response).RetrieveBookingRS.TotalAmountPaid.currency;
                }
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                if (((saveCreateBookingResponse)response).CreateBookingRS.GuestPaymentInfo != null)
                {
                    guestPaymentInfos = ((saveCreateBookingResponse)response).CreateBookingRS.GuestPaymentInfo.ToList();
                    paymentAmount = ((saveCreateBookingResponse)response).CreateBookingRS.TotalAmountPaid.amount;
                    paymentCurrency = ((saveCreateBookingResponse)response).CreateBookingRS.TotalAmountPaid.currency;
                }
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                if (((modifyBookingResponse)response).ModifyBookingRS.GuestPaymentInfo != null)
                {
                    guestPaymentInfos = ((modifyBookingResponse)response).ModifyBookingRS.GuestPaymentInfo.ToList();
                    paymentAmount = ((modifyBookingResponse)response).ModifyBookingRS.TotalAmountPaid.amount;
                    paymentCurrency = ((modifyBookingResponse)response).ModifyBookingRS.TotalAmountPaid.currency;
                }
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                if (((saveModifyBookingResponse)response).SaveModifyBookingRS.GuestPaymentInfo != null)
                {
                    guestPaymentInfos = ((saveModifyBookingResponse)response).SaveModifyBookingRS.GuestPaymentInfo.ToList();
                    paymentAmount = ((saveModifyBookingResponse)response).SaveModifyBookingRS.TotalAmountPaid.amount;
                    paymentCurrency = ((saveModifyBookingResponse)response).SaveModifyBookingRS.TotalAmountPaid.currency;
                }
            }
            if (response.GetType() == typeof(cancelBookingResponse))
            {
                var latestPnrInfoResponse = session.CurrentFlow.GetIbsData<RetrieveBookingRS>(IbsDataTypeEnum.OrderRetrieve.ToString());
                if (latestPnrInfoResponse.GuestPaymentInfo != null)
                {
                    guestPaymentInfos = latestPnrInfoResponse.GuestPaymentInfo.ToList();
                    paymentAmount = latestPnrInfoResponse.TotalAmountPaid.amount;
                    paymentCurrency = latestPnrInfoResponse.TotalAmountPaid.currency;
                }
            }

            foreach (var guestPaymentInfo in guestPaymentInfos)
            {
                guestPaymentInfoDtos.Add(new GuestPaymentInfoDTO
                {
                    PaymentDate = DateTime.Parse(guestPaymentInfo?.TransactionTime.Date.ToString()).ToString("dd.MM.yyyy"),
                    PaymentAmount = guestPaymentInfo.PaymentAmount, //paymentAmount,
                    PaymentCurrency = paymentCurrency,
                    PaymentStatus = guestPaymentInfo.PaymentStatus ?? string.Empty,
                    PaymentMethod = guestPaymentInfo?.FormOfPaymentCode != null
                        ? IbsUtility.MapToPaymentTypeString(guestPaymentInfo.FormOfPaymentCode)
                        : string.Empty,
                    CardInfo = new CardInfo
                    {
                        CardHolderName = guestPaymentInfo?.CardHolderName ?? string.Empty,
                        CardMaskedNumber = guestPaymentInfo?.MaskedCreditcardNumber ?? string.Empty,
                        TransactionDate = DateTime.Parse(guestPaymentInfo?.TransactionTime.Date.ToString()).ToString("dd.MM.yyyy"),
                        CardType = (guestPaymentInfo?.CardType ?? CardDetails_Type.VISA).ToString()
                    }
                });
            }

            return guestPaymentInfoDtos;
        }
        public static List<OrderRetrieveFlightDestination> GetFlightDestinationList(object response, GetPnrInfoResponse result, SessionCache session)
        {
            var flightDestinationList = new List<OrderRetrieveFlightDestination>();
            var flightSegmentDetails = new List<FlightSegmentDetailsType>();
            int languageId = (int)session.Language;
            var plcCountries = CachedData.PlcCountries;
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                flightSegmentDetails = (response as retrieveBookingResponse)?.RetrieveBookingRS.Itinerary.ToList();
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                flightSegmentDetails = (response as saveCreateBookingResponse)?.CreateBookingRS.Itinerary.ToList();
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                flightSegmentDetails = (response as modifyBookingResponse)?.ModifyBookingRS.Itinerary.ToList();
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                flightSegmentDetails = ((saveModifyBookingResponse)response)?.SaveModifyBookingRS.Itinerary.ToList();
            }

                if (flightSegmentDetails != null)
                {
                    foreach (var flight in flightSegmentDetails)
                    {
                        decimal basePrice = 0;
                        var basePriceDescription = string.Empty;
                        var totalPriceDescription = string.Empty;
                        if (response.GetType() == typeof(saveCreateBookingResponse))
                        {
                            //INFO: Buradaki amaç createOrder bittikten sonra Segment ler yeniden oluştuğu için SegmentId leri session da güncellendi.
                            session.CurrentFlow.PNR.Flights.ForEach((sessionFlight) =>
                            {
                                sessionFlight.Segments.ForEach((segment) =>
                                {
                                    if(segment.FlightNumber == flight.fltNumber && segment.ArrivalCode == flight.offPoint && segment.DepartureCode == flight.boardPoint)
                                    {
                                        segment.LocalId = IbsUtility.GetFullSegmentId(flight.SegmentId);
                                        segment.Id = IbsUtility.GetFullSegmentId(flight.SegmentId);
                                        basePrice = sessionFlight.BasePrice;
                                        basePriceDescription = sessionFlight.BasePriceDescription;
                                        totalPriceDescription = sessionFlight.TotalPriceDescription;
                                    }
                                });
                            }); 
                        }
                        
                        var ArrivalCode = flight.offPoint;
                        var DepartureCode = flight.boardPoint;
                        var departureCountryCode = CachedData.GetCountryCodeByAirportCode(DepartureCode);
                        var arrivalCountryCode = CachedData.GetCountryCodeByAirportCode(ArrivalCode);
                        var departureTime = GetCheckInLeftTime(flightSegmentDetails);
                        bool flightIsDomestic = GetIsFlightDomestic(departureCountryCode, arrivalCountryCode);
                        var checkInStartTime = GetCheckInStartTime(flightIsDomestic, DepartureCode);
                        var minDepartureTime = flight.scheduledDepartureTimeLTC;
                        
                        var newDestination = new OrderRetrieveFlightDestination
                        {
                            Id = IbsUtility.GetFullFlightID(flight.SegmentId),
                            JourneyTime = IbsUtility.GetTimeSpanNative(flight.journeyTime),
                            //IsCheckInOpen = GetIsCheckInOpen(DepartureCode, departureTime, checkInStartTime),
                            SelectedBundleCode = GetFlightSelectedBundle(response, flight.SegmentId),
                            IsPlcRoute = GetIsPlcRoute(plcCountries, departureCountryCode, arrivalCountryCode),
                            IsFlown = minDepartureTime <= Ubimecs.Infrastructure.Utilities.Utility.GetLocalTimeByAirportCode(flight.boardPoint),
                            CabinClass = flight.CabinClass,
                            FlightPrice = Convert.ToDecimal(PriceMappers.CalculateFlightPrice(response, Convert.ToInt64(flight.SegmentId))),
                            Segments = new List<FlightSegmentDTO>()
                            {
                                 new FlightSegmentDTO
                                {
                                    AirlineId = flight.carrierCode,
                                    ArrivalCode = flight.offPoint,
                                    ArrivalDate = flight.scheduledArrivalTimeLTC.Date,
                                    ArrivalTime = flight.scheduledArrivalTimeLTC.TimeOfDay.ToString(),
                                    DepartureCode = flight.boardPoint,
                                    DepartureDate = flight.scheduledDepartureTimeLTC.Date,
                                    DepartureTime = flight.scheduledDepartureTimeLTC.TimeOfDay.ToString(),
                                    FlightNumber = flight.fltNumber,
                                    SegmenReferenceId = flight.SegmenReferenceId,
                                    FlightSegmentGroupID = flight.FlightSegmentGroupID,
                                    NotConfirmed = flight.segmentStatus == ReservationStatusDetails_Type.CONFIRMED ? false : true,
                                    Id = IbsUtility.GetFullSegmentId(flight.SegmentId),
                                    JourneyTime = TimeSpan.Parse(flight.journeyTime),
                                    FormattedDepartureDate = IbsUtility.FormatDate((LanguageEnum)languageId, flight.scheduledDepartureDateTime),
                                    DepartureCity = CachedData.AirPortList.Airports.FirstOrDefault(airport => airport.AirportCode == flight.boardPoint)?.CityNames[languageId] ?? string.Empty,
                                    ArrivalCity = CachedData.AirPortList.Airports.FirstOrDefault(airport => airport.AirportCode == flight.offPoint)?.CityNames[languageId] ?? string.Empty,
                                    IsInternational = IbsUtility.GetIsInternationalFlight(flight.boardPoint, flight.offPoint),
                                    CarrierName = flight.deiCarrierName ?? GeneralConstants.CARRIER_NAME,
                                    PriceClassName = flight.FareClass,
                                    AircraftInfo = new AircraftInfo
                                    {
                                        Type = flight.AircraftType,
                                        Version = flight.AircraftVersion,
                                    },
                                    CabinClass = flight.CabinClass,
                                    Stops = flight.stops,
                                    DayChange = flight.arrivalDayChange,
                                }
                            },
                            BasePrice = basePrice,
                            BasePriceDescription = basePriceDescription,
                            TotalPriceDescription = totalPriceDescription,
                            SegmentStatus = flight.segmentStatus.ToString()
                        };

                        flightDestinationList.Add(newDestination);
                }
            }
            if (flightDestinationList.Count > 0)
            {
                #region Order Channels
                var orderChannels = CachedData.OrderChannel;
                #endregion
                #region PNR Restrictions Parameters
                var nameChangeRestrictedPNRs = CachedData.GetCMS(session.Language, "name_change_booking_restricted_pnrs").Split(';').ToList();
                var cancelFlightRestrictedPNRs = CachedData.GetCMS(session.Language, "cancel_flight_booking_restricted_pnrs").Split(';').ToList();
                var addExtrasRetrictedPNRs = CachedData.GetCMS(session.Language, "adding_extras_restricted_pnrs").Split(';').ToList();
                var addFlightRetrictedPNRs = CachedData.GetCMS(session.Language, "adding_flight_restricted_pnrs").Split(';').ToList();
                var changeFlightRestrictedPNRs = CachedData.GetCMS(session.Language, "rebook_flight_restricted_pnrs").Split(';').ToList();
                var upgradeFlightRestrictedPNRs = CachedData.GetCMS(session.Language, "upgrade_flight_restricted_pnrs").Split(';').ToList();
                #endregion
                bool isTourOperator = result.PnrType == PnrTypeEnum.TourOperator;

                double checkinLastHours = Convert.ToDouble(GeneralConstants.CHECK_IN_LAST_HOURS);
                double checkinLastMinutes = checkinLastHours * 60;

                // TourOperator için özel Check-In başlangıç süresi (48 saat)
                int tourOperatorCheckInStartHours = 48;
                int tourOperatorCheckInStartMinutes = tourOperatorCheckInStartHours * 60;

                List<string> infantIdList = new List<string>();
                foreach (var flight in flightDestinationList)
                {
                    if (flightDestinationList.Count == 1)
                    {
                        flight.Title = CachedData.GetCMS(languageId, "departure_flight");
                    }
                    else if (flightDestinationList.Count == 2 && flightDestinationList[0].DepartureCode == flightDestinationList[1].ArrivalCode)
                    {
                        if (flightDestinationList[0].DepartureCode == flight.DepartureCode)
                        {
                            flight.Title = CachedData.GetCMS(languageId, "departure_flight");

                        }
                        else
                        {
                            flight.Title = CachedData.GetCMS(languageId, "return_flight");

                        }
                    }


                    var departureCountryCode = CachedData.GetCountryCodeByAirportCode(flight.Segments.First().DepartureCode);
                    var arrivalCountryCode = CachedData.GetCountryCodeByAirportCode(flight.Segments.First().ArrivalCode);
                    bool domesticFlight = GetIsFlightDomestic(departureCountryCode, arrivalCountryCode);

                    int checkInStartTime = isTourOperator ? tourOperatorCheckInStartMinutes : SeventyTwoHoursInMinutes;
                    if (!isTourOperator)
                    {
                        if (GetChangedAirportCheckInEndTime(flight.Segments.First().DepartureCode)
                                is AirportCheckIn airportCheckIn &&
                            !string.IsNullOrEmpty(airportCheckIn.check_in_start_time) &&
                            !string.IsNullOrEmpty(airportCheckIn.check_in_end_time))
                        {
                            checkInStartTime = Convert.ToInt32(airportCheckIn.check_in_start_time);
                        }
                    }
                    if (checkInStartTime != checkinLastMinutes)
                    {
                        checkinLastMinutes = checkInStartTime;
                    }
                    List<string> checkedInPassengerIds = new List<string>();
                    foreach (var segment in flight.Segments)
                    {
                        var checkedInPassengerId = GetCheckInPassengers(response, IbsUtility.ParseFullId(segment.Id));
                        checkedInPassengerIds.AddRange(checkedInPassengerId);
                    }
                    flight.CheckedInPassengerIds = checkedInPassengerIds.ToList();
                    bool getCheckInClosed = GetCheckInClosed(flight.Segments.First().CompleteDepartureTime, domesticFlight, flight.Segments.First().DepartureCode, departureCountryCode);
                    var remainingDepartureTime = Convert.ToInt32(GetRemainingDepartureTime(flight.Segments.First().CompleteDepartureTime, departureCountryCode).TotalMinutes);
                    remainingDepartureTime = remainingDepartureTime < 0 ? remainingDepartureTime * (-1) : remainingDepartureTime;

                    var departureTime = GetCheckInLeftTime(flight.Segments);
                    flight.CheckInClosedTime = GetCheckInClosedTime(domesticFlight, departureCountryCode);
                    flight.CheckInRemainingHour = Convert.ToInt32((remainingDepartureTime - checkInStartTime) / 60);
                    flight.CheckInRemainingTime = GetCheckInRemainingTime(departureTime, (int)session.Language, departureCountryCode, domesticFlight, isTourOperator);
                    //item.CheckInRemainingHour = item.CheckInRemainingHour < 0 ? (-1) * item.CheckInRemainingHour : item.CheckInRemainingHour;
                    if (isTourOperator)
                    {
                        // TourOperator için 48 saat kuralı
                        flight.IsCheckInOpen = remainingDepartureTime <= tourOperatorCheckInStartMinutes;
                        flight.IsCheckInButtonDisabled = flight.IsDisabled
                                                         || result.StatusCode == "NOK"
                                                         || getCheckInClosed
                                                         || remainingDepartureTime > tourOperatorCheckInStartMinutes;
                    }
                    else
                    {
                        // Diğer PNR tipleri için normal kurallar
                        flight.IsCheckInOpen = flight.CheckInRemainingHour < 0;
                        flight.IsCheckInButtonDisabled = flight.IsDisabled
                                                         || result.StatusCode == "NOK"
                                                         || getCheckInClosed
                                                         || remainingDepartureTime > checkinLastMinutes;
                    }


                    flight.RemainingDepartureTime = !flight.IsFlown ? FormatTimeFromMinutes(remainingDepartureTime) : string.Empty;
                    flight.RemainingDepartureHour = GetRemainingDepartureHour(remainingDepartureTime);
                    flight.IsAddFlightButtonDisabled = CheckRestriction(orderChannels, addFlightRetrictedPNRs, result.OrderCreatedChannel);

                    flight.IsAddExtrasButtonDisabled = CheckRestriction(orderChannels, addExtrasRetrictedPNRs, result.OrderCreatedChannel);

                    flight.IsChangeFlightButtonDisabled = flight.IsDisabled
                                                     || result.StatusCode == "NOK"
                                                     //|| GetRemainingDepartureTime(item.Segments.First().CompleteDepartureTime, departureCountryCode).TotalHours <= checkinLastHours
                                                     || CheckRestriction(orderChannels, changeFlightRestrictedPNRs, result.OrderCreatedChannel);

                    flight.IsCancelFlightButtonDisabled = flight.IsDisabled
                                                     || result.StatusCode == "NOK"
                                                     //|| GetRemainingDepartureTime(item.Segments.First().CompleteDepartureTime, departureCountryCode).TotalHours <= checkinLastHours
                                                     || CheckRestriction(orderChannels, cancelFlightRestrictedPNRs, result.OrderCreatedChannel);
                    flight.IsUpgradeFlightButtonDisabled = flight.IsDisabled
                                                      || result.StatusCode == "NOK"
                                                      || GetRemainingDepartureTime(flight.Segments.First().CompleteDepartureTime, departureCountryCode).TotalMinutes <= checkinLastMinutes
                                                      || result.Services.Bundles.Any(a => a.SegmentIds.Contains(flight.Segments.First().Id) && a.CategoryId == (int)ServiceCategoryEnum.SUN_PREMIUM_BUNDLE)
                                                      || result.Services.GolfBundles.Any(a => a.SegmentIds.Intersect(flight.Segments.Select(t => t.Id).ToList()).Any())
                                                      || CheckRestriction(orderChannels, upgradeFlightRestrictedPNRs, result.OrderCreatedChannel);

                    //|| result.Services.GolfBundles.Any(a => a.SegmentIds.Contains(item.Segments.First().Id));
                    //flight.IsCheckInOpen = !getCheckInClosed && flight.IsCheckInOpen;

                    flight.CheckInTimeOut = flight.IsDisabled
                                       || result.StatusCode == "NOK"
                                       || getCheckInClosed;

                    flight.IsCheckinIncludeRestirctedSsr = SsrCheck(result, session);

                    foreach (var check in flight.CheckedInPassengerIds)
                    {
                        var infId = result.PassengerList.FirstOrDefault(p => p.ParentGuestID == check)?.Id;
                        if (!string.IsNullOrEmpty(infId))
                            infantIdList.Add(infId);

                    }
                }

                var flightsByConfirmed = flightDestinationList.Where(x => x.SegmentStatus != ReservationStatusDetails_Type.CANCELLED.ToString()).ToList();

                SetTitleOfFlights(flightsByConfirmed, languageId);

                flightDestinationList.ForEach(p => p.CheckedInPassengerIds.AddRange(infantIdList));
                flightDestinationList.ForEach(f => f.IsCheckInCompleted = f.CheckedInPassengerIds.Count == result.PassengerList.Count);

                SetAlternativeFlightOfferedFlags(flightDestinationList);
            }

            return flightDestinationList;
        }
        private static void SetTitleOfFlights(List<OrderRetrieveFlightDestination> flightDestinationList,int languageId)
        {
            foreach (var flight in flightDestinationList)
            {
                if (flightDestinationList.Count == 1)
                {
                    flight.Title = CachedData.GetCMS(languageId, "departure_flight");
                }
                else if (flightDestinationList.Count == 2 && flightDestinationList[0].DepartureCode == flightDestinationList[1].ArrivalCode)
                {
                    if (flightDestinationList[0].DepartureCode == flight.DepartureCode)
                    {
                        flight.Title = CachedData.GetCMS(languageId, "departure_flight");

                    }
                    else
                    {
                        flight.Title = CachedData.GetCMS(languageId, "return_flight");

                    }
                }
            }
        }
        public static BookerDetailsDTO GetBookerDetails(object response)
        {
            var bookerDetailsResponse = new BookerDetailsType();
            var bookerDetail = new BookerDetailsDTO();
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                bookerDetailsResponse = ((retrieveBookingResponse)response).RetrieveBookingRS.BookerDetails;
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                bookerDetailsResponse = ((saveCreateBookingResponse)response).CreateBookingRS.BookerDetails;
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                bookerDetailsResponse = ((modifyBookingResponse)response).ModifyBookingRS.BookerDetails;
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                bookerDetailsResponse = ((saveModifyBookingResponse)response).SaveModifyBookingRS.BookerDetails;
            }

            if (bookerDetailsResponse != null)
            {
                bookerDetail.GivenName = bookerDetailsResponse.GivenName;
                bookerDetail.SurName = bookerDetailsResponse.SurName;
                bookerDetail.NamePrefix = bookerDetailsResponse.NamePrefix.ToString();
            }
            
            return bookerDetail;
        }
        public static ReservationInfoNativeDTO GetReservationInfo(object response)
        {
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                var reservationInfo = new ReservationInfoNativeDTO();
                reservationInfo.PnrNumber = (response as retrieveBookingResponse).RetrieveBookingRS.PNRNumber;
                reservationInfo.PnrStatus = (response as retrieveBookingResponse).RetrieveBookingRS.PnrStatus;
                reservationInfo.PnrType = (response as retrieveBookingResponse).RetrieveBookingRS.PnrType;
                reservationInfo.TotalAmountPaid = (response as retrieveBookingResponse).RetrieveBookingRS.TotalAmountPaid.amount;
                reservationInfo.Currency = (response as retrieveBookingResponse).RetrieveBookingRS.TotalAmountPaid.currency;
                reservationInfo.TotalAmountToBePaid = (response as retrieveBookingResponse).RetrieveBookingRS.TotalAmountToBePaid.amount;
                
                return reservationInfo;
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                var reservationInfo = new ReservationInfoNativeDTO();
                reservationInfo.PnrNumber = (response as saveCreateBookingResponse).CreateBookingRS.PnrNumber;
                reservationInfo.PnrStatus = (response as saveCreateBookingResponse).CreateBookingRS.PnrStatus;
                reservationInfo.PnrType = (response as saveCreateBookingResponse).CreateBookingRS.PnrType;
                reservationInfo.TotalAmountPaid = (response as saveCreateBookingResponse).CreateBookingRS.TotalAmountPaid.amount;
                reservationInfo.Currency = (response as saveCreateBookingResponse).CreateBookingRS.TotalAmountPaid.currency;
                reservationInfo.TotalAmountToBePaid = (response as saveCreateBookingResponse).CreateBookingRS.TotalAmountToBePaid.amount;
                
                return reservationInfo;
            }
            if (response.GetType() == typeof(modifyBookingResponse))
            {
                var reservationInfo = new ReservationInfoNativeDTO();
                reservationInfo.PnrNumber = (response as modifyBookingResponse).ModifyBookingRS.PnrNumber;
                reservationInfo.PnrStatus = (response as modifyBookingResponse).ModifyBookingRS.PnrStatus;
                reservationInfo.PnrType = (response as modifyBookingResponse).ModifyBookingRS.PnrType;
                reservationInfo.TotalAmountPaid = (response as modifyBookingResponse).ModifyBookingRS.TotalAmountPaid.amount;
                reservationInfo.Currency = (response as modifyBookingResponse).ModifyBookingRS.TotalAmountPaid.currency;
                reservationInfo.TotalAmountToBePaid = (response as modifyBookingResponse).ModifyBookingRS.TotalAmountToBePaid.amount;
                
                return reservationInfo;
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                var reservationInfo = new ReservationInfoNativeDTO();
                reservationInfo.PnrNumber = (response as saveModifyBookingResponse).SaveModifyBookingRS.PnrNumber;
                reservationInfo.PnrStatus = (response as saveModifyBookingResponse).SaveModifyBookingRS.PnrStatus;
                reservationInfo.PnrType = (response as saveModifyBookingResponse).SaveModifyBookingRS.PnrType;
                reservationInfo.TotalAmountPaid = (response as saveModifyBookingResponse).SaveModifyBookingRS.TotalAmountPaid.amount;
                reservationInfo.Currency = (response as saveModifyBookingResponse).SaveModifyBookingRS.TotalAmountPaid.currency;
                reservationInfo.TotalAmountToBePaid = (response as saveModifyBookingResponse).SaveModifyBookingRS.TotalAmountToBePaid.amount;
                
                return reservationInfo;
            }
            
            return new ReservationInfoNativeDTO();
        }
        public static string GetFlightSelectedBundle(object response, string segmentId)
        {
            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                return ((retrieveBookingResponse)response).RetrieveBookingRS?.SSRDetails?.FirstOrDefault(x=> x?.ssrType == NativeAPIAncillaryConstants.BUNDLE && x.SegmentId.Any(sId => sId == segmentId))?.ssrCode ?? string.Empty;
            }
            
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                return ((saveCreateBookingResponse)response).CreateBookingRS?.SSRDetails?.FirstOrDefault(x=> x?.ssrType == NativeAPIAncillaryConstants.BUNDLE && x.SegmentId.Any(sId => sId == segmentId))?.ssrCode ?? string.Empty;
            }
            
            return string.Empty;
        }
        private static bool GetIsPlcRoute(List<PlcCountry> plcCountries, string departureCountryCode, string arrivalCountryCode)
        {
            bool result = false;
            result = !(departureCountryCode.Equals("TR") && arrivalCountryCode.Equals("TR")) &&
                     plcCountries.Where(w => w.IsArrival).Any(t => t.CountryCode.Equals(arrivalCountryCode));
            return result;
        }
        private static int GetCheckInStartTime(bool isFlightDomestic, string departureCode, bool isTourOperator = false)
        {
            if (isTourOperator)
            {
                return 48 * 60; // 48 saat = 2880 dakika
            }
            
            var checkInTimeResControl = CachedData.CheckInTimeResControl_V2;
            string checkInClosedTime = string.Empty;
            if (GetChangedAirportCheckInEndTime(departureCode) is AirportCheckIn airportInfo && !string.IsNullOrEmpty(airportInfo.check_in_end_time) && !string.IsNullOrEmpty(airportInfo.check_in_start_time))
            {
                return Convert.ToInt32(airportInfo.check_in_start_time);
            }

            if (isFlightDomestic)
            {
                checkInClosedTime = checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains("DOM"))?.ParameterValue;
            }
            else
            {
                checkInClosedTime = checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains(departureCode))?.ParameterValue;
            }

            if (string.IsNullOrEmpty(checkInClosedTime))
            {
                checkInClosedTime = checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains("INTL-ALL")).ParameterValue;
            }
            return Convert.ToInt32(checkInClosedTime);
        }
        private static AirportCheckIn GetChangedAirportCheckInEndTime(string departureCode)
        {
            if (string.IsNullOrEmpty(departureCode))
            {
                return default;
            }

            //CheckInEndTimeModel checkInEndTimes = JsonConvert.DeserializeObject<CheckInEndTimeModel>(CachedData.GetCMS(LanguageEnum.Turkish, "check_in_end_times"));
            var checkInTimeResControl = CachedData.CheckInTimeResControl_V2;
            if (checkInTimeResControl == null)
            {
                return default;
            }
            //if (!checkInTimeResControl.Any(t => t.AirportCode.ToUpper().Trim() == departureCode.ToUpper().Trim()))
            //{
            //    return default;
            //}
            //var checkInStartTimeForTheAirport = checkInTimeResControl?.FirstOrDefault(t => t.AirportCode.ToUpper().Trim() == departureCode && t.ParameterCode.Contains(_timeResStartTimeParameterCode));
            //if (checkInStartTimeForTheAirport == null)
            //{
            //    checkInStartTimeForTheAirport = checkInTimeResControl?.FirstOrDefault(t => t.AirportCode.ToUpper() == "ALL" && t.ParameterCode.Contains(_timeResStartTimeParameterCode));
            //    if(checkInStartTimeForTheAirport == null)
            //    {
            //        return default;
            //    }
            //}

            var checkInEndTime   = GetCheckInEndTimeOrAll(checkInTimeResControl, departureCode).ParameterValue ?? OneHourInMinutes.ToString();
            var checkInStartTime = GetCheckInStartTimeOrAll(checkInTimeResControl, departureCode).ParameterValue ?? SeventyTwoHoursInMinutes.ToString();

            var airportCheckIn = new AirportCheckIn
            {
                code = GetCheckInStartTimeOrAll(checkInTimeResControl, departureCode).AirportCode,
                check_in_end_time = checkInEndTime,
                check_in_start_time = checkInStartTime
            };
            return airportCheckIn;
        }
        private static CheckInTimeResControlDTO GetCheckInEndTimeOrAll(List<CheckInTimeResControlDTO> checkInTimeResControl, string departureCode)
        {
            var checkInEndTimeForTheAirport = checkInTimeResControl?.FirstOrDefault(t => t.AirportCode.ToUpper().Trim() == departureCode && t.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_END_TIME));
            if (checkInEndTimeForTheAirport == null)
            {
                checkInEndTimeForTheAirport = checkInTimeResControl?.FirstOrDefault(t => t.AirportCode.ToUpper() == "ALL" && t.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_END_TIME));
                if (checkInEndTimeForTheAirport == null) { return default; }
            }
            return checkInEndTimeForTheAirport;
        }
        private static CheckInTimeResControlDTO GetCheckInStartTimeOrAll(List<CheckInTimeResControlDTO> checkInTimeResControl,string departureCode)
        {
            var checkInStartTimeForTheAirport = checkInTimeResControl?.FirstOrDefault(t => t.AirportCode.ToUpper().Trim() == departureCode && t.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_START_TIME));
            if(checkInStartTimeForTheAirport == null)
            {
                checkInStartTimeForTheAirport = checkInTimeResControl?.FirstOrDefault(t => t.AirportCode.ToUpper() == "ALL" && t.ParameterCode.Contains(NativeAPICheckinParametersConstants.WEB_CHECKIN_START_TIME));
                if( checkInStartTimeForTheAirport == null) { return default; }
            }
            return checkInStartTimeForTheAirport;
        }
        private static bool GetIsFlightDomestic(string departureCountrCode, string arrivalCountryCode)
        {
            if (departureCountrCode.Equals(GeneralConstants.DOMESTIC_COUNTRY_CODE) && departureCountrCode == arrivalCountryCode)
            {
                return true;
            }
            return false;
        }
        private static DateTime GetCheckInLeftTime(List<FlightSegmentDetailsType> segmentList)
        {
            // return segmentList.LastOrDefault().CompleteDepartureTime;
            DateTime result = new DateTime();
            StringBuilder connectionFlightKey = new StringBuilder("", 500);
            string checkInLeftTime = "";
            foreach (var segment in segmentList)
            {
                connectionFlightKey.Append(segment.scheduledDepartureDateTime.ToString("yyyy-MM-dd HH:mm") + "|");
            }
            if (segmentList.Count > 1)
            {
                checkInLeftTime = connectionFlightKey.ToString().Split('|')[1].Substring(0, 16);
            }
            else
            {
                checkInLeftTime = connectionFlightKey.ToString().Split('|')[0].Substring(0, 16);
            }

            return DateTime.Parse(checkInLeftTime);
        }
        private static dynamic GetItinary(object response)
        {
            return response switch
            {
                confirmPriceResponse confirmResponse => confirmResponse.ConfirmPriceRS.Itinerary,
                modifyBookingResponse modifyResponse => modifyResponse.ModifyBookingRS.Itinerary,
                saveModifyBookingResponse saveModifyResponse => saveModifyResponse.SaveModifyBookingRS.Itinerary,
                saveCreateBookingResponse saveCreateResponse => saveCreateResponse.CreateBookingRS.Itinerary,
                retrieveBookingResponse retrieveBookingResponse => retrieveBookingResponse.RetrieveBookingRS.Itinerary,
                _ => null
            };
        }
        private static List<string> GetCheckInPassengers(object response, string segmentId)
        {
            List<string> passengerIds = new();
            if (response.GetType() == typeof(retrieveBookingResponse))
            {

                var ssrDetails = ((retrieveBookingResponse)response).RetrieveBookingRS?.SSRDetails?.Where(x => x?.ssrCode == "CHKD" && x.SegmentId.Contains(segmentId))?.ToList();

                if (ssrDetails != null && ssrDetails.Any())
                {
                    foreach (var ssrDetail in ssrDetails)
                    {
                        passengerIds.Add(IbsUtility.GetFullPassengerID(ssrDetail.GuestId.ToString()));
                    }
                }
            }

            return passengerIds;
        }
        public static TimeSpan GetDiffBetweenDepartureTimeAndLocalDateTime(DateTime departureTime, string countryCode)
        {
            var timeOffset = Ubimecs.Infrastructure.Utilities.Utility.GetOffsetTimeForUtc(countryCode);
            var localDateTime = Convert.ToDateTime((DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm"))).AddHours(timeOffset);
            return (Convert.ToDateTime(departureTime.ToString("yyyy-MM-dd HH:mm"))) - (localDateTime);
        }
        public static TimeSpan GetCheckInRemainingTime(DateTime departureTime, string countryCode, bool isFlightDomestic, bool isTourOperator = false)
        {
            var offsetTime = Ubimecs.Infrastructure.Utilities.Utility.GetOffsetTimeForUtc(countryCode);
            var currentDateTime = DateTime.UtcNow.AddHours(offsetTime);
            int closedTime = GetCheckInStartTime(isFlightDomestic, countryCode, isTourOperator);
            var value = (DateTime.Parse(departureTime.ToString()).AddMinutes(-1 * Convert.ToInt32(closedTime))) - (currentDateTime);
            return value;
        }
        public static TimeSpan GetRemainingDepartureTime(DateTime departureTime, string countryCode)
        {
            var offsetTime = Ubimecs.Infrastructure.Utilities.Utility.GetOffsetTimeForUtc(countryCode);
            var currentDateTime = DateTime.UtcNow.AddHours(offsetTime);
            currentDateTime = Convert.ToDateTime(currentDateTime.ToString("yyyy-MM-dd HH:mm"));
            return (Convert.ToDateTime(departureTime.ToString("yyyy-MM-dd HH:mm"))) - (currentDateTime);
        }
        private static bool GetCheckInClosed(DateTime departureTime, bool isFlightDomestic, string departureAirportCode, string departureCountryCode)
        {
            if (string.IsNullOrEmpty(departureAirportCode)) return true;
            var checkinForbiddenMinutesBeforeDeparture = GetCheckInClosedTime(isFlightDomestic, departureAirportCode);
            var remainingTimeForDeparture = GetDiffBetweenDepartureTimeAndLocalDateTime(departureTime, departureCountryCode).TotalMinutes;
            if (checkinForbiddenMinutesBeforeDeparture != 0)
            {
                if (checkinForbiddenMinutesBeforeDeparture > remainingTimeForDeparture)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return true;
            }

        }
        private static int GetCheckInClosedTime(bool isFlightDomestic, string departureCode)
        {
            var checkInTimeResControl = CachedData.CheckInTimeResControl_V2;
            string checkInClosedTime = string.Empty;

            if (GetChangedAirportCheckInEndTime(departureCode) is AirportCheckIn airportInfo && !string.IsNullOrEmpty(airportInfo.check_in_end_time) && !string.IsNullOrEmpty(airportInfo.check_in_start_time))
            {
                return Convert.ToInt32(airportInfo.check_in_end_time);
            }

            if (isFlightDomestic)
            {
                checkInClosedTime = GetDomesticEndTimeOrAll(checkInTimeResControl);
                //checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains("DOM"))?.ParameterValue; // && ALL && endtime olan dom
            }
            else
            {
                checkInClosedTime = checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains(departureCode))?.ParameterValue; // && ALL && endtime olan dom
            }

            if (string.IsNullOrEmpty(checkInClosedTime))
            {
                checkInClosedTime = checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains("INTL-ALL")).ParameterValue;
            }
            return Convert.ToInt32(checkInClosedTime);
        }
        private static string GetDomesticEndTimeOrAll(List<CheckInTimeResControlDTO> checkInTimeResControl)
        {
            return checkInTimeResControl.FirstOrDefault(row => row.ParameterCode.Contains("WEBCHECKIN_END_TIME_DOM") && row.AirportCode == "ALL")?.ParameterValue;
        }
        private static bool CheckRestriction(List<OrderChannel> orderChannels, List<string> checkList, string pnrOrderChannel)
        {
            if (pnrOrderChannel == null) return true;

            if (checkList.Count == 0) return false;

            var orderSource = orderChannels.FirstOrDefault(t => t.ChannelCode == pnrOrderChannel)?.OrderSource;

            if (orderSource == null) return false;

            return checkList.Contains(orderSource);
        }
        private static bool SsrCheck(GetPnrInfoResponse pnrInfoRs, SessionCache session)
        {
            var errorMsg = CachedData.GetCMS(session.Language, "restricted_ssrs_check_in_pop_up_text");
            var restrictedSsrListStr = CachedData.GetCMS(session.Language, "restricted_ssrs_check_in");
            if (restrictedSsrListStr != null)
            {
                var restrictedSsrList = restrictedSsrListStr.Split(',');
                var includeSsr = pnrInfoRs.Services.Others.Where(p => restrictedSsrList.Contains(p.Code));
                if (includeSsr != null)
                    if (includeSsr.Count() > 0)
                        return true;

            }
            return false;
        }
        private static DateTime GetCheckInLeftTime(List<FlightSegmentDTO> segmentList)
        {
            // return segmentList.LastOrDefault().CompleteDepartureTime;
            DateTime result = new DateTime();
            StringBuilder connectionFlightKey = new StringBuilder("", 500);
            string checkInLeftTime = "";
            foreach (var segment in segmentList)
            {
                connectionFlightKey.Append(segment.DepartureDate.ToString("yyyy-MM-dd") + "T" + segment.DepartureTime + " ");
            }
            if (segmentList.Count > 1)
            {
                checkInLeftTime = connectionFlightKey.ToString().Split(' ')[1].Substring(0, 16);
            }
            else
            {
                checkInLeftTime = connectionFlightKey.ToString().Split(' ')[0].Substring(0, 16);
            }

            return DateTime.Parse(checkInLeftTime);
        }
        private static string GetCheckInRemainingTime(DateTime departureTime, int languageId, string countryCode, bool isFlightDomestic, bool isTourOperator = false)
        {
            var remainingTimeOfCheckIn = GetCheckInRemainingTime(departureTime, countryCode, isFlightDomestic, isTourOperator);
            var d = remainingTimeOfCheckIn.Days > 0 ? $"{remainingTimeOfCheckIn.Days}{CachedData.GetCMS(languageId, "countdown_check_in_day")} " : "";
            var h = remainingTimeOfCheckIn.Hours > 0 ? $"{remainingTimeOfCheckIn.Hours}{CachedData.GetCMS(languageId, "countdown_check_in_hour")} " : "";
            var m = remainingTimeOfCheckIn.Minutes > 0 ? $"{remainingTimeOfCheckIn.Minutes}{CachedData.GetCMS(languageId, "countdown_check_in_min")}" : "";
            var checkInString = "{d}{h}{m}"; //CachedData.GetCMS(languageId, "countdown_check_in");
            checkInString = checkInString.Replace("{d}", "{0}").Replace("{h}", "{1}").Replace("{m}", "{2}");
            return String.Format(checkInString, d, h, m);
        }
        private static string FormatTimeFromMinutes(int totalMinutes)
        {
            int days = totalMinutes / (24 * 60);
            int hours = (totalMinutes % (24 * 60)) / 60;
            int minutes = totalMinutes % 60;

            return $"{days}d {hours}h {minutes}min";
        }
        private static InvoiceInformation? GetInvoiceDetails(object response)
        {
            var ssrDetails = response switch
            {
                retrieveBookingResponse r => r.RetrieveBookingRS.SSRDetails,
                saveCreateBookingResponse s => s.CreateBookingRS.SSRDetails,
                saveModifyBookingResponse s => s.SaveModifyBookingRS.SSRDetails,
                _ => null
            };

            if (ssrDetails == null) return null;

            var invoiceSsr = ssrDetails.FirstOrDefault(ssr =>
                ssr.ssrCode == NativeAPIInvoiceTypeConstants.Company || 
                ssr.ssrCode == NativeAPIInvoiceTypeConstants.Private);
                             
            if (invoiceSsr == null) return null;
           
           var metaData = invoiceSsr.SsrFieldMetaData;

            return new InvoiceInformation
            {
                InvoiceType = invoiceSsr.ssrCode == NativeAPIInvoiceTypeConstants.Company ? 
                            InvoiceTypeEnum.Company : InvoiceTypeEnum.Private,
                CountryOfResidence = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.COUNTRY_OF_RESIDENCE),
                CompanyName = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.COMPANY_NAME),
                TaxOffice = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.TAX_OFFICE),
                TaxNumber = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.TAX_NO),
                InvoiceAddress = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.INVOICE_ADDRESS),
                TurkishNationalityId = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.TURKISH_NATIONALITY_ID),
                Surname = metaData.GetValueOrNull(NativeAPIInvoiceFieldsContants.SURNAME),
            };
        }

        public static string? GetValueOrNull(this IEnumerable<ReservationsPortServiceReference.SsrFieldMetaDataType> metaData, string fieldName) =>
            string.IsNullOrWhiteSpace(metaData?.FirstOrDefault(x => x.Name == fieldName)?.Value) 
            ? null 
            : metaData.FirstOrDefault(x => x.Name == fieldName)?.Value;
        
        public static int GetRemainingDepartureHour(int totalMinutes) { return totalMinutes / 60; }
        
        public static GroupInformation GetGroupDetails(object response)
        {
            var groupPaxDetails = new GroupPaxDetailsType();
            var groupInformation = new GroupInformation();
            var pnrContact = new PnrContactType();
            string agencyCode = string.Empty;

            if (response.GetType() == typeof(retrieveBookingResponse))
            {
                groupPaxDetails = ((retrieveBookingResponse)response).RetrieveBookingRS.GroupPaxDetails;
                pnrContact = ((retrieveBookingResponse)response).RetrieveBookingRS.PnrContact.FirstOrDefault(x=> x.ContactType == ContactDetails_Type.H);
                agencyCode = ((retrieveBookingResponse)response).RetrieveBookingRS.AgencyCode;
            }
            if (response.GetType() == typeof(saveCreateBookingResponse))
            {
                groupPaxDetails = ((saveCreateBookingResponse)response).CreateBookingRS.GroupPaxDetails;
                pnrContact = ((saveCreateBookingResponse)response).CreateBookingRS.PnrContact.FirstOrDefault(x=> x.ContactType == ContactDetails_Type.H);
                agencyCode = ((saveCreateBookingResponse)response).CreateBookingRS.AgencyCode;
            }
            if (response.GetType() == typeof(saveModifyBookingResponse))
            {
                groupPaxDetails = ((saveModifyBookingResponse)response).SaveModifyBookingRS.GroupPaxDetails;
                pnrContact = ((saveModifyBookingResponse)response).SaveModifyBookingRS.PnrContact.FirstOrDefault(x=> x.ContactType == ContactDetails_Type.H);
                agencyCode = ((saveModifyBookingResponse)response).SaveModifyBookingRS.AgencyCode;
            }

            if (groupPaxDetails != null)
            {
                groupInformation.AgencyCode = agencyCode;
                groupInformation.GroupName = groupPaxDetails.GroupName;
                groupInformation.GroupType = IbsUtility.MapToGroupTypeEnum(groupPaxDetails.GroupType);
                groupInformation.NameTitle = IbsUtility.MapToNameTitle(groupPaxDetails.LeaderTitle);
                groupInformation.FirstName = groupPaxDetails.LeaderFirstName;
                groupInformation.LastName = groupPaxDetails.LeaderLastName;
            }

            if (pnrContact != null)
            {
                groupInformation.PhoneCode = pnrContact.Address.PhoneNumberCountryCode;
                groupInformation.PhoneNumber = pnrContact.Address.PhoneNumber;
                groupInformation.Email = pnrContact.Address.EmailAddress;
            }
            
            return groupInformation;
        }
        
        public static string GetPnrComment(object response)
        {
            string pnrComment = response switch
            {
                retrieveBookingResponse rbr => rbr.RetrieveBookingRS?.PnrComments?.FirstOrDefault(x=>x.CommentTypeCode == CommentCode_Type.CONFIDENTIAL_COMMENT)?.CommentValue,
                saveCreateBookingResponse scbr => scbr.CreateBookingRS?.PnrComments?.FirstOrDefault(x=>x.CommentTypeCode == CommentCode_Type.CONFIDENTIAL_COMMENT)?.CommentValue,
                saveModifyBookingResponse smbr => smbr.SaveModifyBookingRS?.PnrComments?.FirstOrDefault(x=>x.CommentTypeCode == CommentCode_Type.CONFIDENTIAL_COMMENT)?.CommentValue,
                _ => string.Empty
            };

            return pnrComment ?? string.Empty;
        }
        
        public static string GetGroupRequestDate(object response)
        {
            string requestDate = response switch
            {
                retrieveBookingResponse rbr => rbr.RetrieveBookingRS.CreationDateAndTime.Date.ToString("dd.MM.yyyy"),
                saveCreateBookingResponse scbr => scbr.CreateBookingRS.CreationDateAndTime.Date.ToString("dd.MM.yyyy"),
                saveModifyBookingResponse smbr => smbr.SaveModifyBookingRS.CreationDateAndTime.Date.ToString("dd.MM.yyyy"),
                _ => string.Empty
            };

            return requestDate;
        }
        
        public static GroupPnrActionButtons GetGroupPnrActionButtons(object response)
        {
            if (response == null) return new GroupPnrActionButtons();

            var actionButtons = new GroupPnrActionButtons
            {
                IsEditButtonEnabled = true,
                IsCancelRequestButtonEnabled = true
            };

            var reservationInfo = GetReservationInfo(response);
            if (reservationInfo == null) return actionButtons;

            var guestDetails = GetGuestDetails(response);
            var itinary = GetItinary(response) as IEnumerable<dynamic>;
            var groupBookingStatus = IbsUtility.MapToGroupBookingStatus(itinary, reservationInfo.PnrStatus);

            if (groupBookingStatus == GroupBookingStatusEnum.Approved)
            {
                actionButtons.IsMakePaymentButtonEnabled = reservationInfo.TotalAmountToBePaid > 0;
                actionButtons.IsInvoiceAccessButtonEnabled = !actionButtons.IsMakePaymentButtonEnabled;
                actionButtons.IsUploadPassengerListButtonEnabled = guestDetails?.Any(g => g.GivenName == "NTBA") ?? false;
            }

            return actionButtons;
        }

        private static IEnumerable<ReservationsPortServiceReference.GuestReponseDetailsType> GetGuestDetails(object response)
        {
            return response switch
            {
                retrieveBookingResponse r => r.RetrieveBookingRS.GuestDetails,
                saveCreateBookingResponse s => s.CreateBookingRS.GuestDetails,
                saveModifyBookingResponse sm => sm.SaveModifyBookingRS.GuestDetails,
                _ => null
            };
        }
        public static string GetPnrType(object response)
        {
            return response switch
            {
                retrieveBookingResponse rbr => rbr.RetrieveBookingRS.PnrType,
                saveCreateBookingResponse scbr => scbr.CreateBookingRS.PnrType,
                saveModifyBookingResponse smbr => smbr.SaveModifyBookingRS.PnrType,
                _ => string.Empty
            };
        }
        
        public static string GetFopType(object response)
        {
            return response switch
            {
                retrieveBookingResponse rbr => rbr.RetrieveBookingRS.GuestPaymentInfo?.FirstOrDefault()?.FormOfPaymentCode.ToString(),
                saveCreateBookingResponse scbr => scbr.CreateBookingRS.GuestPaymentInfo?.FirstOrDefault()?.FormOfPaymentCode.ToString(),
                saveModifyBookingResponse smbr => smbr.SaveModifyBookingRS.GuestPaymentInfo?.FirstOrDefault()?.FormOfPaymentCode.ToString(),
                _ => string.Empty
            };
        }

        private static PaymentStatus ParseFarePaidStatus(ReservationsPortServiceReference.FarePaidStatusDetails_Type farePaidStatus)
        {
            return farePaidStatus switch
            {
                ReservationsPortServiceReference.FarePaidStatusDetails_Type.FULL => PaymentStatus.PAID,
                ReservationsPortServiceReference.FarePaidStatusDetails_Type.PARTIAL => PaymentStatus.PARTIAL_PAID,
                ReservationsPortServiceReference.FarePaidStatusDetails_Type.ZERO => PaymentStatus.UNPAID,
                _ => throw new ArgumentOutOfRangeException(nameof(farePaidStatus), $"Unknown FarePaidStatus: {farePaidStatus}")
            };
        }

        private static ReservationsPortServiceReference.FarePaidStatusDetails_Type? GetFarePaidStatusFromResponse(object response, SessionCache session)
        {
            if (response is retrieveBookingResponse rbr)
                return rbr.RetrieveBookingRS?.FarePaidStatusSpecified == true ? rbr.RetrieveBookingRS.FarePaidStatus : (ReservationsPortServiceReference.FarePaidStatusDetails_Type?)null;

            if (response is saveCreateBookingResponse scbr)
                return scbr.CreateBookingRS?.FarePaidStatusSpecified == true ? scbr.CreateBookingRS.FarePaidStatus : (ReservationsPortServiceReference.FarePaidStatusDetails_Type?)null;

            if (response is saveModifyBookingResponse smbr)
                return smbr.SaveModifyBookingRS?.FarePaidStatusSpecified == true ? smbr.SaveModifyBookingRS.FarePaidStatus : (ReservationsPortServiceReference.FarePaidStatusDetails_Type?)null;

            if (response is modifyBookingResponse mbr)
                return mbr.ModifyBookingRS?.FarePaidStatusSpecified == true ? mbr.ModifyBookingRS.FarePaidStatus : (ReservationsPortServiceReference.FarePaidStatusDetails_Type?)null;

            if (response is cancelBookingResponse)
            {
                var retrieveBooking = session.CurrentFlow.GetIbsData<RetrieveBookingRS>(IbsDataTypeEnum.OrderRetrieve.ToString());
                return retrieveBooking?.FarePaidStatusSpecified == true ? retrieveBooking.FarePaidStatus : (ReservationsPortServiceReference.FarePaidStatusDetails_Type?)null;
            }

            return null;
        }


        public static List<GroupBookingTimeLimitDetail> GetGroupPnrTimeLimitDetails(object response)
        {
            var timeLimitDetails = response switch
            {
                retrieveBookingResponse r => r.RetrieveBookingRS?.TimeLimitDetails,
                saveCreateBookingResponse s => s.CreateBookingRS?.TimeLimitDetails,
                saveModifyBookingResponse s => s.SaveModifyBookingRS?.TimeLimitDetails,
                _ => null
            };

            if (timeLimitDetails == null || !timeLimitDetails.Any())
                return new List<GroupBookingTimeLimitDetail>();

            return timeLimitDetails
                .Select(t => new GroupBookingTimeLimitDetail
                {
                    TimeLimitAction = t.TimeLimitAction,
                    TimeLimitType = IbsUtility.ParseTimeLimitType(t.TimeLimitType),
                    TimeLimitLTC = t.TimeLimitLTC,
                    TimeLimitUTC = t.TimeLimitUTC
                })
                .Where(t => t.TimeLimitType == TimeLimitTypeEnum.NTBA 
                            || t.TimeLimitType == TimeLimitTypeEnum.ZERO_PAYMENT_GROUP
                            || t.TimeLimitType == TimeLimitTypeEnum.PARTIAL_PAYMENT_GROUP
                            || t.TimeLimitType == TimeLimitTypeEnum.GROUPFORM
                            || t.TimeLimitType == TimeLimitTypeEnum.GQ_OFFER_TIMELIMIT)
                .ToList();
        }
        
        private static bool IsFirstSegmentFlown(List<OrderRetrieveFlightDestination> flightDestinationList)
        {
            if (flightDestinationList == null || !flightDestinationList.Any())
            {
                return false;
            }

            var orderedSegments = flightDestinationList.OrderBy(f => f.Segments.Min(y=>y.CompleteDepartureTime)).ToList();
            var firstSegment = orderedSegments.FirstOrDefault();
    
            if (firstSegment == null)
            {
                return false;
            }
            
            return firstSegment.IsFlown;
        }

        public static List<FlightRequestDto> Map(this GetPnrInfoResponse response,SessionCache session)
        {
             
            return response.FlightDestionationList.Select(fd => new FlightRequestDto
            {
                PersonId = session.IdentityInfo?.CrmPersonId,
                PnrNumber = response.PNRNumber,
                TourOperatorPnrNumber = response.TourOperatorNumber,
                ArrivalCode = fd.ArrivalCode,
                DepartureCode = fd.DepartureCode,
                ArrivalDate = ((DateTimeOffset)fd.Segments.LastOrDefault()?.ArrivalDate).ToUnixTimeMilliseconds().ToString(),
                DepartureDate = ((DateTimeOffset)fd.Segments.FirstOrDefault()?.DepartureDate).ToUnixTimeMilliseconds().ToString(),
                JourneyTime = fd.JourneyTime.ToString(),
                AirlineId = fd.Segments.FirstOrDefault()?.AirlineId,
                FlightNumber = Convert.ToInt32(fd.Segments.FirstOrDefault()?.FlightNumber),
                FareClass = fd.Segments.FirstOrDefault()?.FareBasisCode,
                PnrStatus = response.StatusCode,
                PnrType = response.PnrType.ToString(),
                Channel = response.OrderCreatedChannel,
                TicketSalesChannel = "İnternet",
                ReservationChannelType = "API",
                TotalAmount = Convert.ToDecimal(response.TotalAmount),
                Currency = response.Currency,
                PaxCount = response.PassengerList.Count,
                OriginalCaller = response.Name+" "+response.Surname, 
                Bundle = fd.SelectedBundleCode,
                SegmentStatus = fd.SegmentStatus,
                FlightSalesDate = ((DateTimeOffset)fd.Segments.LastOrDefault()?.ArrivalDate).ToUnixTimeMilliseconds().ToString(),
                FlightSalesDayDiff = 1,
                PaymentOption = response.PaymentDetails.FirstOrDefault()?.PaymentMethod,
                PromotionCode = "PromoCode",
                Passengers = response.PassengerList.Select(x=>new FlightPassenger
                {
                    FirstName = x.GivenName,
                    LastName = x.Surname,
                    Gender = x.Gender.ToString(),
                    BirthDate =((DateTimeOffset)x.Birthdate).ToUnixTimeMilliseconds().ToString(),
                    PaxId =  Convert.ToInt32(IbsUtility.ParseFullId(x.Id)), //????
                    Phone = response.PhoneNumber,
                    ContactOption = "Evet", //????
                    Email = response.EmailAddress,
                    Nationality = x.CountryCode,
                    PaxType = x.PassengerType
                }).ToList()
            }).ToList();
           
        }

        public static PNRExtractRQ MapToPnrExtractRequest(this GetPnrInfoRequest request, ChannelConfiguration channelConfig, SessionCache session)
        {
            PNRExtractRQ pnrExtractRq = new PNRExtractRQ();
            pnrExtractRq.AirlineCode = GeneralConstants.AIRLINECODE;
            pnrExtractRq.BookingChannel = new ReservationsPortServiceReference.BookingChannelKeyType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfig.ChannelCode, //GeneralConstants.BOOKING_CHANNEL_CODE,
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            };

            if (!String.IsNullOrEmpty(request.OrderId))
            {
                pnrExtractRq.PNRNumber = request.OrderId;
            }

            else
            {
                return null;
            }

            return pnrExtractRq;
        }

        public static List<GuestPaymentInfoDTO> GetPaymentDetailsFromExtract(PNRExtractRS response)
        {
            var paymentInfoList = new List<GuestPaymentInfoDTO>();

            if (response?.TotalPaymentDetails?.PaymentDetails == null)
                return paymentInfoList;

            foreach (var payment in response.TotalPaymentDetails.PaymentDetails)
            {
                paymentInfoList.Add(new GuestPaymentInfoDTO
                {
                    PaymentDate = DateTime.Parse(payment.PaymentDate).ToString("dd.MM.yyyy"),
                    PaymentAmount = payment.PaymentAmount,
                    PaymentCurrency = payment.PaymentCurrency,
                    PaymentStatus = payment.PaymentStatus,
                    PaymentMethod = IbsUtility.MapToPaymentTypeString(payment.FormOfPaymentCode),
                    CardInfo = new CardInfo
                    {
                        CardHolderName = payment.CardHolderName,
                        CardMaskedNumber = payment.CreditCardNumber,
                        TransactionDate = DateTime.Parse(payment.PaymentDate).ToString("dd.MM.yyyy"),
                        CardType = payment.CardType
                    }
                });
            }

            return paymentInfoList;
        }

        private static void SetAlternativeFlightOfferedFlags(List<OrderRetrieveFlightDestination> flights)
        {
            if (flights == null || flights.Count < 2)
                return;

            var validCombinations = new[]
            {
                new[] { "WAS_CONFIRMED", "SCHEDULE_CHANGE" },
                new[] { "TIME_CHANGE_FROM_CONFIRMED", "TIME_CHANGE" }
            };

            foreach (var combination in validCombinations)
            {
                var statusSet = flights.Select(f => f.SegmentStatus).Distinct().ToList();

                if (statusSet.Contains(combination[0]) && statusSet.Contains(combination[1]))
                {
                    foreach (var flight in flights)
                    {
                        if (combination.Contains(flight.SegmentStatus) &&
                            flight.FlightDate.HasValue &&
                            flight.FlightDate.Value.Date >= DateTime.Today)
                        {
                            flight.IsAlternativeFlightOffered = true;
                        }
                        else
                        {
                            flight.IsAlternativeFlightOffered = false;
                        }
                    }

                    break;
                }
            }
        }
    }
}
