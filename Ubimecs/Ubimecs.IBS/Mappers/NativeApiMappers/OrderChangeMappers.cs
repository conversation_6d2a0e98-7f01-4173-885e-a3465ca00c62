using ReservationsPortServiceReference;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Payment;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.IBS.Mappers.NativeApiMappers;

public static partial class ReservationMappers
{
    public static SaveModifyBookingRQ MapToSaveModifyBooking(SessionCache session, 
        ChannelConfiguration channelConfiguration,
        string tmobId = null, 
        ChangeOrderRequest request = null,
        bool isbundleTypeChanging = false,
        RebookingContactInfoChangeRequest? contactInfoChangeRequest = null,
        ChangeRebookingTravelDocumentRequest travelDocumentChangeRequest = null)
    {
        var flight = tmobId != null 
            ? session.CurrentFlow.PNR.Flights.FirstOrDefault(x => x.TmobId == tmobId)
            : session.CurrentFlow.RebookingType == RebookingTypeEnum.CancelFlight
                ? session.CurrentFlow.PNR.Flights.FirstOrDefault(x => x.State == OfferRebookingStateEnum.Removed)
                : GetCurrentFlight(session);

        if (isbundleTypeChanging && session.CurrentFlow.RebookingType == RebookingTypeEnum.ChangeFlight)
        {
           flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(x => x.State == OfferRebookingStateEnum.Upgraded);
        }
        
        var passengers = session.CurrentFlow.PNR.Passengers.Where(x => x.PassengerType != PassengerTypeEnum.INF).ToList() ?? new();
        var seats = session.CurrentFlow.PNR.Seats ?? new();
        var numberOfSeats = passengers.Count;
        var latestPnrInfo = session.CurrentFlow.PnrInfoResponse;
        var latestPnrInfoResponse = session.CurrentFlow.GetIbsData<RetrieveBookingRS>(IbsDataTypeEnum.OrderRetrieve);
        var nameChangedPassengerList = session.CurrentFlow.PNR.Passengers.Where(x => x.IsNameChanged);
        #region SSRModifyDetails
        var ssrModifyList = new List<SsrModifyType>();
        
        var saveModifyBookingRQ = new SaveModifyBookingRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            PointOfSale = IbsUtility.PointOfSaleType(flight?.DepartureCode, session.CurrentFlow.PNR.Currency)
                .Location.CountryCode.Value,
            BookingChannel = new BookingChannelType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode, //GeneralConstants.BOOKING_CHANNEL_CODE,
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            },
            BookerDetails = new BookerDetailsType
            {
                SurName = latestPnrInfoResponse.BookerDetails.SurName
            },
            PnrNumber = session.CurrentFlow.PNR.Number,
            NumberOfSeats = numberOfSeats,
            isConfirmPriceRequired = false,
            isCancelPnr = false
        };
        if (flight != null)
        {
            string[] segmentIds = flight.SegmentRefs.Split(' ').Select(s => IbsUtility.ParseFullId(s)).ToArray();
            if (flight.IsConnectedFlight)
            {
                segmentIds = new[] {IbsUtility.ParseFullId(flight.Id)};
            }
            
            var bundleSsrModifyMap = passengers.MapToBundleSsrModification(segmentIds, flight.SelectedBundleCode, session, channelConfiguration, isbundleTypeChanging);
            ssrModifyList.AddRange(bundleSsrModifyMap);
        
            var sportsEquipments = session.CurrentFlow.PNR.SportEquipments.Where(x => x.Count > 0 && x.FlightTmobId == flight.TmobId).ToList();
            var sportEquipmentsToSsrMap = sportsEquipments.MapToSportEquipmentSsrModification(segmentIds, session);
            ssrModifyList.AddRange(sportEquipmentsToSsrMap);
        
            var meals = session.CurrentFlow.PNR.Meals;
            var mealsToSsrMap = meals.MapToMealSsrModification(segmentIds, session, ssrModifyList);
            ssrModifyList.AddRange(mealsToSsrMap);
        
            var extraBaggages = session.CurrentFlow.PNR.ExtraBaggages;
            var extraBaggagesToSsrMap = extraBaggages.MapToExtraBaggageSsrModification(segmentIds, session);
            ssrModifyList.AddRange(extraBaggagesToSsrMap);
        
            var extraServices = session.CurrentFlow.PNR.ExtraServices;
            var extraServicesToSsrMap = extraServices.MapToExtraServiceSsrModification(segmentIds, session);
            ssrModifyList.AddRange(extraServicesToSsrMap);
        
            var flexes = session.CurrentFlow.PNR.FlexServices.Where(x => !x.IsBundleFlex).ToList() ?? new();
            var flexToSsrMap = flexes.MapToFlexSsrModification(segmentIds, session);
            ssrModifyList.AddRange(flexToSsrMap);
            
            if (session.CurrentFlow.RebookingType == RebookingTypeEnum.ChangeFlightNative || session.CurrentFlow.RebookingType == RebookingTypeEnum.AddFlight)
            {
                var removedFlight = session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Removed);
                var oldSegmentIds = removedFlight?.SegmentRefs.Split(' ').Select(s => IbsUtility.ParseFullId(s)).ToArray();
            
                #region PaxCountDetailMap 
                var paxCountDetailsMap = passengers.Modify_MapToPaxCountDetailTypes();
                #endregion
        
                List<ReservationsPortServiceReference.FareInfoType> fareInfos = new List<ReservationsPortServiceReference.FareInfoType>();
                var fareInfoType = Modify_MapFlightToFareInfoType(session, flight.TmobId, paxCountDetailsMap, segmentIds);
                fareInfos.Add(fareInfoType);
            
                List<FlightSegmentDetailsType> segmentInfos = new List<FlightSegmentDetailsType>();
                var flightSegmentDetailsMap = Modify_TmobIdToSegmentInfo(session, flight.TmobId);
                segmentInfos.Add(flightSegmentDetailsMap);
            
                if (flight != null)
                {
                    saveModifyBookingRQ.ItineraryChangeType = new SegmentChangeType[]
                    {
                        new SegmentChangeType
                        {
                            PnrActionType = session.CurrentFlow.RebookingType == RebookingTypeEnum.AddFlight ? PnrActionType.ADD : PnrActionType.MODIFY,
                            OldSegmentId = oldSegmentIds?.Select(x => Convert.ToInt64(x)).ToArray(),
                            FlightSegmentDetails = segmentInfos.ToArray()
                        }
                    };
                    saveModifyBookingRQ.FareInfo = fareInfos.ToArray();
                }
            }

            if (session.CurrentFlow.RebookingType == RebookingTypeEnum.CancelFlight)
            {
                saveModifyBookingRQ.ItineraryChangeType = new[]
                {
                    new SegmentChangeType()
                    {
                        PnrActionType = PnrActionType.DELETE,
                        OldSegmentId = segmentIds.Select(x => Convert.ToInt64(x)).ToArray(),
                        FlightSegmentDetails = flight.Segments.Select(segment => new FlightSegmentDetailsType
                        {
                            FlightSegmentGroupID = IbsUtility.ParseFullId(segment.Id),
                            SegmentId = IbsUtility.ParseFullId(segment.Id),
                            carrierCode = segment.AirlineId,
                            fltNumber = segment.FlightNumber,
                            fltSuffix = "*",
                            flightDate = new DateOnlyDetailsType
                            {
                                Date = flight.FlightDate.Value
                            },
                            boardPoint = segment.DepartureCode,
                            offPoint = segment.ArrivalCode,
                            journeyTime = string.Format("{0}:{1}", segment.JourneyTime.Hours, segment.JourneyTime.Minutes),
                            stops = segment.Stops,
                            arrivalDayChange = segment.DayChange,
                            CabinClass = segment.CabinClass,
                            FareClass = segment.PriceClassName,

                            OldSegmentId = new[] { segment.Id },
                        }).ToArray()
                    }
                };
            }      
        }
        #endregion
        
        var passengerSeats = HandleSeatChanges(
            seats,
            latestPnrInfo.Services.Seats,
            flight);

        #region InvoiceDetails
        if (session.CurrentFlow.RebookingType != RebookingTypeEnum.CancelFlight 
            && session.CurrentFlow.RebookingType != RebookingTypeEnum.CancelPNR 
            && session.CurrentFlow.RebookingType != RebookingTypeEnum.ContantInfoChange
            && session.CurrentFlow.RebookingType != RebookingTypeEnum.TravelDocumentChange
            && session.CurrentFlow.RebookingType != RebookingTypeEnum.NameChange)
        {
            if (request?.InvoiceInformation != null)
            {
                var invoiceInformation = request.InvoiceInformation;
                var invoiceToSsrMap = MapRebookingInvoiceSSR(latestPnrInfoResponse, invoiceInformation, session);
                ssrModifyList.AddRange(invoiceToSsrMap);
            }
        }
        #endregion
        
        bool isPartialPaid = true;

        if (ssrModifyList.Count > 0)
        {
            saveModifyBookingRQ.SsrModifyType = ssrModifyList.ToArray();
            isPartialPaid = false;
        }
        if (passengerSeats.Count > 0)
        {
            saveModifyBookingRQ.SeatAssignmentChangeType = passengerSeats?.ToArray();
            isPartialPaid = false;
        }

        if (request != null)
        {
            var paymentInfoDetails = ReservationMappers_MapToPaymentInfoDetails(request, session, session.CurrentFlow.PNR.Passengers, isPartialPaid);
            if (paymentInfoDetails.Any())
            {
                saveModifyBookingRQ.GuestPaymentInfo = paymentInfoDetails.ToArray();
            }
        }
        
        if (!string.IsNullOrEmpty(session.CurrentFlow.PNR.IbsSessionId))
        {
            saveModifyBookingRQ.PnrSessionId = session.CurrentFlow.PNR.IbsSessionId;
        }
        
        if (session.CurrentFlow.RebookingType == RebookingTypeEnum.ContantInfoChange && contactInfoChangeRequest != null)
        {
            var pnrContact = contactInfoChangeRequest.MapToContactInfo();

            BookingContactChangeType bookingContactChangeType = new BookingContactChangeType
            {
                PnrActionType = PnrActionType.MODIFY,
                PnrContactType = [pnrContact]
            };

            saveModifyBookingRQ.BookingContactChangeType = [bookingContactChangeType];
        }
        
        if (nameChangedPassengerList.Any())
        {
            var mapToNameChangeGuestDetailsType = MapToNameChangePaxChangeType_OrderChange(nameChangedPassengerList.ToList());
            saveModifyBookingRQ.PaxChangeType = mapToNameChangeGuestDetailsType.ToArray();
            
            var travelDocumentTypes = MapToTravelDocumentsWithNameChange(nameChangedPassengerList.ToList(), session);
            saveModifyBookingRQ.TravelDocumentChangeType = travelDocumentTypes?.ToArray();
        }
        
        if (session.CurrentFlow.RebookingType == RebookingTypeEnum.TravelDocumentChange && travelDocumentChangeRequest != null)
        {
            var travelDocumentTypes = travelDocumentChangeRequest?.MapToTravelDocuments(session);
            saveModifyBookingRQ.TravelDocumentChangeType = travelDocumentTypes?.ToArray();
        }

        

        return saveModifyBookingRQ;
    }
    
    public static List<SsrModifyType> MapRebookingInvoiceSSR(RetrieveBookingRS latestPnrInfoResponse, InvoiceInformation invoiceInformation, SessionCache session)
    {
        var ssrModifyList = new List<SsrModifyType>();
        var existingInvoice = latestPnrInfoResponse.SSRDetails?
            .FirstOrDefault(x => x.ssrCode == NativeAPIInvoiceTypeConstants.Company || x.ssrCode == NativeAPIInvoiceTypeConstants.Private);

        if (invoiceInformation == null)
        {
            if (existingInvoice != null)
            {
                ssrModifyList.Add(new SsrModifyType
                {
                    PnrActionType = PnrActionType.DELETE,
                    SSRInformationType = new SSRInformationType[]
                    {
                        new SSRInformationType
                        {
                            SSRId = existingInvoice.SSRId,
                            SSRIdSpecified = true
                        }
                    }
                });
            }
        }
        
        if (invoiceInformation != null)
        {
            if (existingInvoice != null)
            {
                ssrModifyList.Add(new SsrModifyType
                {
                    PnrActionType = PnrActionType.DELETE,
                    SSRInformationType = new SSRInformationType[]
                    {
                        new SSRInformationType
                        {
                            SSRId = existingInvoice.SSRId,
                            SSRIdSpecified = true
                        }
                    }
                });
            }

            var invoiceSsrMap = invoiceInformation.MapToInvoiceSSR(session);
            ssrModifyList.Add(new SsrModifyType
            {
                PnrActionType = PnrActionType.ADD,
                SSRInformationType = new SSRInformationType[] {invoiceSsrMap}
            });
        }

        return ssrModifyList;
    }
    
    public static List<PaxChangeType> MapToNameChangePaxChangeType(List<Infrastructure.Models.PNR.Passenger> passengers, RebookingNameChangeRequest request)
    {
        var paxChangeTypes = new List<PaxChangeType>();
        int familyIdCounter = 500;
        
        foreach (var requestPassenger in request.Passengers)
        {
            var sessionGuestDetails = passengers.FirstOrDefault(p => p.Id == requestPassenger.PassengerID);
            
            string familyId = !string.IsNullOrEmpty(sessionGuestDetails?.FamilyId)
                ? sessionGuestDetails.FamilyId
                : familyIdCounter.ToString();

            var paxChangeType = CreatePaxChangeType(requestPassenger, sessionGuestDetails, familyId);
            paxChangeTypes.Add(paxChangeType);
            familyIdCounter++;
        }

        return paxChangeTypes;
    }
    
    public static List<PaxChangeType> MapToNameChangePaxChangeType_OrderChange(List<Infrastructure.Models.PNR.Passenger> passengers)
    {
        var paxChangeTypes = new List<PaxChangeType>();
        int familyIdCounter = 500;
        
        foreach (var requestPassenger in passengers)
        {
            string familyId = !string.IsNullOrEmpty(requestPassenger?.FamilyId)
                ? requestPassenger.FamilyId
                : familyIdCounter.ToString();

            var paxChangeType = CreatePaxChangeType_OrderChange(requestPassenger, familyId);
            paxChangeTypes.Add(paxChangeType);
            familyIdCounter++;
        }

        return paxChangeTypes;
    }
    
    private static PaxChangeType CreatePaxChangeType(RebookingNameChangeRequest.PassengerInfo requestPassenger, 
        Infrastructure.Models.PNR.Passenger sessionGuestDetails,
        string familyId
        )
    {
        return new PaxChangeType
        {
            SSRDelIndicator = false,
            travelDocDelIndicator = false,
            nextOfKinDelIndicator = false,
            InfantDelIndicator = false,
            PnrActionType = PnrActionType.MODIFY,
            GuestRequestDetailsType = CreateGuestRequestDetails(requestPassenger, sessionGuestDetails, familyId)
        };
    }

    private static PaxChangeType CreatePaxChangeType_OrderChange(
        Infrastructure.Models.PNR.Passenger sessionGuestDetails,
        string familyId
    )
    {
        return new PaxChangeType
        {
            SSRDelIndicator = false,
            travelDocDelIndicator = false,
            nextOfKinDelIndicator = false,
            InfantDelIndicator = false,
            PnrActionType = PnrActionType.MODIFY,
            GuestRequestDetailsType = CreateGuestRequestDetails_OrderChange(sessionGuestDetails, familyId)
        };
    }
    
    private static GuestRequestDetailsType CreateGuestRequestDetails(
        RebookingNameChangeRequest.PassengerInfo requestPassenger, 
        Infrastructure.Models.PNR.Passenger sessionGuestDetails,
        string familyId)
    {
        var guestRequestDetailsType = new GuestRequestDetailsType();
        guestRequestDetailsType.GivenName = requestPassenger.GivenName;
        guestRequestDetailsType.SurName = requestPassenger.Surname;
        guestRequestDetailsType.NamePrefix = IbsUtility.MapToNameTitle(requestPassenger.NameTitle);
        guestRequestDetailsType.NamePrefixSpecified = true;
        guestRequestDetailsType.GuestType = GetGuestType(sessionGuestDetails.PassengerType.ToString());
        guestRequestDetailsType.DateOfBirth = requestPassenger.Birthdate.HasValue
            ? DateTime.SpecifyKind(requestPassenger.Birthdate.Value, DateTimeKind.Utc)
            : DateTime.MinValue;
        guestRequestDetailsType.DateOfBirthSpecified = requestPassenger.Birthdate.HasValue;
        guestRequestDetailsType.GuestId = IbsUtility.ParseFullId(requestPassenger.PassengerID);
        guestRequestDetailsType.Gender = GetGenderFromNameTitle(requestPassenger.NameTitle);
        guestRequestDetailsType.GenderSpecified = true;
        guestRequestDetailsType.FamilyId = familyId;
        if (sessionGuestDetails.PassengerType == PassengerTypeEnum.INF)
        {
            guestRequestDetailsType.ParentGuestID = IbsUtility.ParseFullId(sessionGuestDetails.ParentGuestID);
        }
        return guestRequestDetailsType;
    }
    
    private static GuestRequestDetailsType CreateGuestRequestDetails_OrderChange(
        Infrastructure.Models.PNR.Passenger sessionGuestDetails,
        string familyId)
    {

        var guestDetails = new GuestRequestDetailsType
        {
            GivenName = sessionGuestDetails.Name,
            SurName = sessionGuestDetails.Surname,
            NamePrefix = IbsUtility.MapToNameTitle(sessionGuestDetails.NameTitle),
            NamePrefixSpecified = true,
            GuestType = GetGuestType(sessionGuestDetails.PassengerType.ToString()),
            DateOfBirth = sessionGuestDetails.BirthDate.HasValue 
                ? DateTime.SpecifyKind(sessionGuestDetails.BirthDate.Value, DateTimeKind.Utc)
                : DateTime.MinValue,
            DateOfBirthSpecified = sessionGuestDetails.BirthDate.HasValue,
            GuestId = IbsUtility.ParseFullId(sessionGuestDetails.Id),
            Gender = GetGenderFromNameTitle(sessionGuestDetails.NameTitle),
            GenderSpecified = true,
            FamilyId = familyId
        };
        
        if (!string.IsNullOrEmpty(sessionGuestDetails.ParentGuestID))
        {
            guestDetails.ParentGuestID = IbsUtility.ParseFullId(sessionGuestDetails.ParentGuestID);
        }

        return guestDetails;
    }
    public static GenderDetails_Type GetGenderFromNameTitle(string nameTitle)
    {
        if (string.IsNullOrEmpty(nameTitle))
            return GenderDetails_Type.M;
        
        return nameTitle.ToUpperInvariant() switch
        {
            "MR" => GenderDetails_Type.M,
            "MRS" => GenderDetails_Type.F,
            "MS" => GenderDetails_Type.F,
            "MSTR" => GenderDetails_Type.M,
            "MISS" => GenderDetails_Type.F,
            _ =>  GenderDetails_Type.M
        };
    }
    
    public static SessionPnrFlightDTO GetCurrentFlight(SessionCache session)
    {
        var pnr = session.CurrentFlow.PNR;
        var flights = pnr.Flights;

        if (pnr.SportEquipments.Any())
        {
            var first = pnr.SportEquipments.First();
            return flights.FirstOrDefault(x => x.TmobId == first.FlightTmobId);
        }

        if (pnr.Seats.Any())
        {
            var first = pnr.Seats.First();
            return flights.FirstOrDefault(x => x.Segments.Any(s => s.TmobId == first.SegmentTmobId));
        }

        if (pnr.Meals.Any())
        {
            var first = pnr.Meals.First();
            return flights.FirstOrDefault(x => x.Segments.Any(s => s.TmobId == first.SegmentTmobId));
        }

        if (pnr.ExtraBaggages.Any())
        {
            var first = pnr.ExtraBaggages.First();
            return flights.FirstOrDefault(x => x.TmobId == first.TmobId);
        }

        if (pnr.Bundles.Any())
        {
            var first = pnr.Bundles.First();
            return flights.FirstOrDefault(x => x.TmobId == first.TmobId);
        }

        if (pnr.ExtraServices.Any())
        {
            var first = pnr.ExtraServices.First();
            return flights.FirstOrDefault(x => x.TmobId == first.TmobId);
        }

        if (pnr.FlexServices.Any())
        {
            var first = pnr.FlexServices.First();
            return flights.FirstOrDefault(x => x.Segments.Any(s => s.TmobId == first.SegmentTmobId));
        }

        if (pnr.CarParkings.Any())
        {
            var first = pnr.CarParkings.First();
            return flights.FirstOrDefault(x => x.Segments.Any(s => s.TmobId == first.SegmentTmobId));
        }

        if (pnr.GroundTransfers.Any())
        {
            var first = pnr.GroundTransfers.First();
            return flights.FirstOrDefault(x => x.Segments.Any(s => s.TmobId == first.SegmentTmobId));
        }

        return null;
    }
    
    public static SaveModifyBookingRQ Map(this RebookingMakePaymentRequest request, SessionCache session, ChannelConfiguration channelConfiguration)
    {
        var latestPnrResponse = session.CurrentFlow.PnrInfoResponse;
        var passengers = session.CurrentFlow.PNR.Passengers.Where(x => x.PassengerType != PassengerTypeEnum.INF).ToList() ?? new();
        SaveModifyBookingRQ saveModifyBookingRQ = new SaveModifyBookingRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            AgencyCode = session.AgencyInfo.AgencyCode,
            OriginalAgentID = session.AgencyInfo.AgencyCode,
            CurrentAgentID = session.AgencyInfo.AgencyCode,
            BookingChannel = new BookingChannelType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode,
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            },
            BookerDetails = new BookerDetailsType
            {
                SurName = latestPnrResponse.Surname,
            },
            PnrNumber = session.CurrentFlow.PNR.Number,
            NumberOfSeats = passengers.Count,
            isConfirmPriceRequired = false,
            isCancelPnr = false
        };
        
        if (request != null)
        {
            var paymentInfoDetails = ReservationMappers.ReservationMappers_MapToPaymentInfoDetails(request, session, session.CurrentFlow.PNR.Passengers);
            if (paymentInfoDetails.Any())
            {
                saveModifyBookingRQ.GuestPaymentInfo = paymentInfoDetails.ToArray();
            }
        }
        
        return saveModifyBookingRQ;
    }
    
    public static SaveModifyBookingRQ Map(this AddBookingToProfileRequest request, SessionCache session, ChannelConfiguration channelConfiguration)
    {
        var latestPnrResponse = session.CurrentFlow.PnrInfoResponse;
        var passengers = session.CurrentFlow.PNR.Passengers.Where(x => x.PassengerType != PassengerTypeEnum.INF).ToList() ?? new();
        SaveModifyBookingRQ saveModifyBookingRQ = new SaveModifyBookingRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            AgencyCode = session.AgencyInfo.AgencyCode,
            OriginalAgentID = session.AgencyInfo.AgencyCode,
            CurrentAgentID = session.AgencyInfo.AgencyCode,
            BookingChannel = new BookingChannelType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode,
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            },
            BookerDetails = new BookerDetailsType
            {
                SurName = latestPnrResponse.Surname,
                ProfileId = session.IdentityInfo?.IbsPersonId != null ? session.IdentityInfo?.IbsPersonId : null,
            },
            PnrNumber = session.CurrentFlow.PNR.Number,
            NumberOfSeats = passengers.Count,
            isConfirmPriceRequired = false,
            isCancelPnr = false
        };
        return saveModifyBookingRQ;
    }

}