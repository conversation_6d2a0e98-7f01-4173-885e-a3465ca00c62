using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.customerProfile;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;
using GuestDetails = Ubimecs.IBS.customerProfile.GuestDetails;

namespace Ubimecs.IBS.Mappers.NativeApiMappers;

public static class CustomerMappers
{
    public static Ubimecs.IBS.customerProfile.ListCustomerProfileRQ Map(string email,ChannelConfiguration channelConfiguration)
    {
        return new Ubimecs.IBS.customerProfile.ListCustomerProfileRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            BookingChannel = new BookingChannelKeyType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode,
                Locale = GeneralConstants.LOCALE
            },
            ProfileType = "CUSTOMER",
            EmailID = email
        };
    }
    
    public static CustomerBookedListRQ Map(this GetProfileUpcomingFlightRequest request,ChannelConfiguration channelConfiguration,SessionCache session)
    {
        return new CustomerBookedListRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            BookingChannel = new BookingChannelKeyType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode,
                Locale = GeneralConstants.LOCALE
            },
            BookerProfileId = request.BookerProfileId
        };
    }
    public static List<PNRList> ToPagedList(this List<Ubimecs.IBS.customerProfile.PNRList> response,int page)
    {
        int pageSize = 3;
        int skip = (page - 1) * pageSize;
        int take = pageSize;
        return response.Skip(skip).Take(take).ToList();
    }
    public static CreateCustomerProfileRQ CreateCustomerProfileMap(Ubimecs.Infrastructure.Models.DTO.Gda.UserInfo userInfo, ChannelConfiguration channelConfiguration)
    {
        string phoneCountryCode = userInfo.Phone.Substring(0, 3);
        string phoneNumber = userInfo.Phone.Substring(3);
        return new CreateCustomerProfileRQ()
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            BookingChannel = new BookingChannelKeyType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode,
                Locale = GeneralConstants.LOCALE
            },
            ProfileType = "CUSTOMER",
            GuestDetails = new List<GuestDetails>
            {
                new GuestDetails
                {
                    Title = GetTitle(userInfo.Title),
                    TitleSpecified = true,
                    FirstName = userInfo.Name,
                    LastName = userInfo.Surname,
                    GuestType = GuestType.ADT,
                    Gender = GetGender(userInfo.Title),
                    GenderSpecified = true,
                }
            }.ToArray(),
            HomeContact = new CustomerProfileContactDetails
            {
                AddressType = "HOME",
                PrefferedIndicator = true,
                CellphoneNumber = phoneNumber,
                EmailId = userInfo.Email,
                SendItineraryToEmailId = true,
                CellNumberCountryCode = phoneCountryCode,
                SendItineraryToSMS = true,
                SendItineraryToSMSSpecified = true,
                SendItineraryToAltEmailIdSpecified = true
            }
        };
        
        /*<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sim="http://www.ibsplc.com/iRes/simpleTypes/">
            <soapenv:Header>
            <api_access_key>FH-GUEST EN-iflyfhibe-freebird123</api_access_key>
            </soapenv:Header>
            <soapenv:Body>
            <sim:CreateCustomerProfileRQ>
            <AirlineCode>FH</AirlineCode>
            <BookingChannel>
            <ChannelType>API</ChannelType>
            <Channel>GUEST EN</Channel>
            <Locale>en_US</Locale>
            </BookingChannel>
            <ProfileType>CUSTOMER</ProfileType>
            <GuestDetails>
            <LastName>YILMAZ</LastName>
            <FirstName>SALİH CAN</FirstName>
            <Title>MR</Title>
            <GuestType>ADULT</GuestType>
            <Gender>M</Gender>
            </GuestDetails>
            <HomeContact>
            <AddressType>HOME</AddressType>
            <PrefferedIndicator>true</PrefferedIndicator>
            <CellphoneNumber>5386679352</CellphoneNumber>
            <Country>TR</Country>
            <EmailId><EMAIL></EmailId>
            <SendItineraryToEmailId>true</SendItineraryToEmailId>
            <CellNumberCountryCode>+90</CellNumberCountryCode>
            <SendItineraryToSMS>true</SendItineraryToSMS>
            </HomeContact>
            </sim:CreateCustomerProfileRQ>
            </soapenv:Body>
            </soapenv:Envelope>*/
    }

    private static NamePrefixType GetTitle(string namePrefixType)
    {
        namePrefixType = namePrefixType.ToUpper();
        return namePrefixType switch
        {
            "MR" =>  Ubimecs.IBS.customerProfile.NamePrefixType.MR,
            "MRS" => Ubimecs.IBS.customerProfile.NamePrefixType.MRS,
            "M" => Ubimecs.IBS.customerProfile.NamePrefixType.M,
            _ => throw new ArgumentOutOfRangeException(nameof(namePrefixType), namePrefixType, null)
           
        };
    }
    
    private static Ubimecs.IBS.customerProfile.GenderType GetGender(string title)
    {
        title = title.ToUpper();
        return title switch
        {
            "MR" => Ubimecs.IBS.customerProfile.GenderType.M,
            "MRS" => Ubimecs.IBS.customerProfile.GenderType.F,
            _ => throw new ArgumentOutOfRangeException(nameof(title), title, null)
        };
    }
}