using AvailabilityPortServiceReference;
using ReservationsPortServiceReference;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.PNR;
using DateOnlyDetailsType = ReservationsPortServiceReference.DateOnlyDetailsType;
using FlightSegmentDetailsType = ReservationsPortServiceReference.FlightSegmentDetailsType;

namespace Ubimecs.IBS.Mappers.NativeApiMappers;

public static class GuaranteeAncillaryMappers
{
    public static GuaranteeAncillaryRQ Map(SessionCache session, ChannelConfiguration channelConfiguration)
    {
        var passengers = session.CurrentFlow.PNR.Passengers;
        var paxCountDetailsMap = passengers.MapToPaxCountDetailsGuarantee();
        var flights = session.CurrentFlow.PNR.Flights.ToList();
        List<FlightSegmentDetailsType> flightSegmentDetailsTypes = new List<FlightSegmentDetailsType>();
        var seatAssignmentDetails = new List<ReservationsPortServiceReference.SeatAssignmentDetailsType>();
        foreach (var flight in flights)
        {
            var flightSegmentDetailsMap = TmobIdToSegmentInfo(session, flight.TmobId);
            flightSegmentDetailsTypes.AddRange(flightSegmentDetailsMap);
            var seats = session.CurrentFlow.PNR.Seats.Where(x=>flight.Segments.Any(y=>y.TmobId == x.SegmentTmobId)).ToList();
            if (seats.Any())
            {
                var seatAssigmentDetailsMap = flight.Segments.GuaranteeMapToSeatAssignmentDetailsType(seats, session);
                seatAssignmentDetails.AddRange(seatAssigmentDetailsMap);
            }
        }
        var guaranteeAncillaryRq = new GuaranteeAncillaryRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            BookingChannel = new BookingChannelType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode, //IbsUtility.GetChannel(session),
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            },
            PnrType = "NORMAL",
            PaxCountDetails = paxCountDetailsMap.ToArray(),
            ItineraryDetails = flightSegmentDetailsTypes.ToArray(),
            GuestDetails = MapToGuestRequestDetailsTypes(passengers).ToArray(),
            SeatAssignmentDetails = seatAssignmentDetails.ToArray()
        };
        return guaranteeAncillaryRq;
    }
    public static List<PaxCountDetailsType> MapToPaxCountDetailsGuarantee(this List<Passenger> passengers)
    {
        var groupedPassengers = passengers.GroupBy(x => x.PassengerType);
        var paxCountDetailsMap = new List<PaxCountDetailsType>();
        foreach (var gp in groupedPassengers)
        {
            var paxType = IbsUtility.MapPaxType(gp.Key);
            paxCountDetailsMap.Add(new PaxCountDetailsType { PaxType = paxType, PaxCount = gp.Count() });
        }
        return paxCountDetailsMap;
    }
    public static List<FlightSegmentDetailsType> TmobIdToSegmentInfo(SessionCache session, string tmobId)
    {
        var flightSegmentDetailsMap = new List<FlightSegmentDetailsType>();
        if (session.CurrentFlow.Type == FlowType.Booking)
        {
            AirAvailabilityRS airAvailabilityRS =
                session.CurrentFlow.GetIbsData<AirAvailabilityRS>(IbsDataTypeEnum.ShopAirResponse.ToString());
            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == tmobId);
            var segmentAvailability =
                airAvailabilityRS.GetSegmentAvailability(IbsUtility.ParseFullId(IbsUtility.ParseFullId(flight.Id)));
            var pricingInfo = airAvailabilityRS.GetPricingInfo(IbsUtility.ParseFullId(flight.Id));
            var segmentInfo = airAvailabilityRS.GetSegmentInfoByFlightId(IbsUtility.ParseFullId(flight.Id));
            var originDestinationInfo = airAvailabilityRS.OriginDestinationInfo.FirstOrDefault(od =>
                od.TripInfo.Any(ti => ti.TripIndex == Convert.ToInt64(IbsUtility.ParseFullId(flight.Id))));
            flightSegmentDetailsMap = segmentInfo.MapToFlightSegment(segmentAvailability,false);
        }
        else if (session.CurrentFlow.Type == FlowType.Rebooking)
        {
            var latestPnrInfoResponse = session.CurrentFlow.GetIbsData<RetrieveBookingRS>(IbsDataTypeEnum.OrderRetrieve.ToString());
            var itinary = latestPnrInfoResponse.Itinerary;
            
            flightSegmentDetailsMap = itinary.Select(itin =>
            {
                var details = new FlightSegmentDetailsType
                {
                    FlightSegmentGroupID = itin.SegmentId,
                    SegmentId = itin.SegmentId,
                    carrierCode = GeneralConstants.CARRIERCODE,
                    fltNumber = itin.fltNumber,
                    fltSuffix = "*",
                    flightDate = new DateOnlyDetailsType
                    {
                        Date = itin.flightDate.Date,
                    },
                    boardPoint = itin.boardPoint,
                    offPoint = itin.offPoint,
                    scheduledDepartureDateTime = itin.scheduledDepartureDateTime,
                    DepartureTimeZone = itin.DepartureTimeZone,
                    scheduledArrivalTime = itin.scheduledArrivalTime,
                    ArrivalTimeZone = itin.ArrivalTimeZone,
                    journeyTime = itin.journeyTime,
                    stops = itin.stops,
                    arrivalDayChange = itin.arrivalDayChange,
                    CabinClass = itin.CabinClass,
                    FareClass = itin.FareClass
                };
                return details;
            }).ToList();
        }

        return flightSegmentDetailsMap;
    }
    public static List<ReservationsPortServiceReference.SeatAssignmentDetailsType> GuaranteeMapToSeatAssignmentDetailsType(this List<FlightSegmentDTO> segments, List<Infrastructure.Models.DTO.Seat.SegmentPassengerSeat> seats, SessionCache session)
    {
        List<ReservationsPortServiceReference.SeatAssignmentDetailsType> mapToSeatAssignmentDetailsType = new List<ReservationsPortServiceReference.SeatAssignmentDetailsType>();

        var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(x=> x.Segments.Any(y=> y.TmobId == segments.FirstOrDefault()?.TmobId));
        var segmentId = string.Empty;
           
        if (flight.IsConnectedFlight)
        {
            segmentId = IbsUtility.ParseFullId(flight.Id);
        }
        else
        {
            segmentId = IbsUtility.ParseFullId(flight.Segments.FirstOrDefault().Id);
        }
            
        var seatAssignmentDetails = seats
            .Where(seat => seat.SeatMarkingId == null)
            .Select(seat => new ReservationsPortServiceReference.SeatAssignmentDetailsType
        {
            Origin = flight.DepartureCode,
            Destination = flight.ArrivalCode,
            ChildBoardPoint = flight.DepartureCode,
            ChildOffPoint = flight.ArrivalCode,
            GuestSeatDetails = new ReservationsPortServiceReference.GuestSeatDetailsType[]
            {
                new ReservationsPortServiceReference.GuestSeatDetailsType{
                    GuestId = IbsUtility.ParseFullId(seat.PassengerId),
                    SeatNumbers = string.Format("{0}{1}", seat.Number, seat.Column)
                }
            },
            SegmentId = segmentId
        }).ToList();
                
        mapToSeatAssignmentDetailsType.AddRange(seatAssignmentDetails);
        return mapToSeatAssignmentDetailsType;
    }
    public static List<GuestRequestDetailsType> MapToGuestRequestDetailsTypes(this List<Infrastructure.Models.PNR.Passenger> passengers)
    {
        var guestDetailsList = passengers.Select(requestPassenger =>
        {
            var guestDetails = new GuestRequestDetailsType
            {
                GivenName = requestPassenger.Name,
                SurName = requestPassenger.Surname,
                NamePrefix = IbsUtility.MapToNameTitle(requestPassenger.NameTitle),
                NamePrefixSpecified = true,
                Gender = IbsUtility.ConvertToGenderDetailsType(requestPassenger.Gender),
                GenderSpecified = true,
                GuestType = ReservationMappers.GetGuestType(requestPassenger.PassengerType.ToString()),
                GuestId = IbsUtility.ParseFullId(requestPassenger.Id),
                DateOfBirth = requestPassenger.BirthDate ?? new DateTime(0001, 01, 01),
                DateOfBirthSpecified = requestPassenger.BirthDate.HasValue
            };

            if (!string.IsNullOrEmpty(requestPassenger.ParentGuestID))
            {
                guestDetails.ParentGuestID = IbsUtility.ParseFullId(requestPassenger.ParentGuestID);
            }

            return guestDetails;
        }).ToList();

        return guestDetailsList;
    }
}