using System.Collections.Specialized;
using System.Xml.Linq;
using Ubimecs.IBS.Models;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.IBS.Utility;

namespace Ubimecs.IBS.Requesters
{
    public class IbsRequester<TRequest, TResponse>
    {
        internal NameValueCollection headers = new NameValueCollection();

        public IbsRequester(SessionCache session, string serviceName, string url, string username = null, string password = null, int timeout = 0)
        {
            Session = session;
            ServiceName = serviceName;
            Url = url;
            Timeout = timeout;

            headers.Add("CLIENT_SESSION_ID", session.SessionId);
            headers.Add("Content-Encoding", "gzip");
            headers.Add("Accept-Encoding", "gzip");
            if (!string.IsNullOrWhiteSpace(username))
            {
                headers.Add("userName", username);
            }

            if (!string.IsNullOrWhiteSpace(password))
            {
                headers.Add("password", password);
            }
        }

        public SessionCache Session { get; }
        public string ServiceName { get; }
        public string Url { get; }
        public int Timeout { get; }

        public TResponse Execute(TRequest request)
        {
            var response = InternalExecute(request);

            return response;
        }

        internal virtual TResponse InternalExecute(TRequest request)
        {
            //try
            //{
            Envelope envelope = new Envelope()
            {
                Body = new EnvelopeBody()
                {
                    Item = request
                }
            };

            Logger.Instance.XmlLog(Session.SessionId, ServiceName, envelope, typeof(TRequest).Name);

            var response = Ubimecs.Infrastructure.Utilities.Utility.XmlRequest(Url, Ubimecs.Infrastructure.Utilities.Utility.SerilizeToXML(envelope).ToCharArray(), headers, timeout: Timeout);
            var envelopeResponse = Ubimecs.Infrastructure.Utilities.Utility.DerializeFromXML<Envelope>(response);

            Logger.Instance.XmlLog(Session.SessionId, ServiceName, envelopeResponse, typeof(TResponse).Name);

            CheckForGeneralError(envelopeResponse);

            return (TResponse)envelopeResponse.Body.Item;
            //}
            //catch (Exception ex)
            //{
            //    Logger.Instance.ErrorLog(Session.SessionId, ServiceName, ex);
            //    return default;
            //}            
        }

        internal virtual void CheckForGeneralError(Envelope envelope)
        {
            if (envelope.Body.Fault != null)
            {
                string faultcode;
                string faultstring;

                //TODO:Kontrol et yeni değişiklik yapıldı
                var xml = XElement.Parse(Ubimecs.Infrastructure.Utilities.Utility.RemoveInvalidXmlChars(envelope.Body.Fault.OuterXml));

                faultcode = xml.Elements().Where(x => x.Name == "faultcode").FirstOrDefault().Value;
                faultstring = xml.Elements().Where(x => x.Name == "faultstring").FirstOrDefault().Value;

                throw new IbsException(faultcode, faultstring, Session.Language);
            }

            object errorObject = null;

            if (envelope.Body.Item is OrderReshopRS orderReshopRS)
            {
                errorObject = orderReshopRS.Items?.FirstOrDefault();
            }

            if (envelope.Body.Item is OrderViewRS orderRetrieve)
            {
                errorObject = orderRetrieve.Items?.FirstOrDefault();
                var resetOBject = false;
                if (errorObject is ErrorsType errorObj)
                {
                    if (Ubimecs.Infrastructure.Utilities.Statics.SecurePaymentErrors.Any(t => t == errorObj.Error.FirstOrDefault().ShortText.Split('#')[0]))
                    {
                        resetOBject = true;
                    }
                }
                if (resetOBject)
                {
                    errorObject = null;
                }
            }

            if (envelope.Body.Item is OfferPriceRS offerPrice)
            {
                errorObject = offerPrice.Items?.FirstOrDefault();

                if (errorObject is ErrorsType errors)
                {
                    if (errors.Error.FirstOrDefault().ShortText?.Contains("BKG_BOE_MISCON") == true)
                    {
                        throw new TmobException(CachedData.GetCMS(Session.Language, "booking_too_close_flight_pop_up"));
                    }
                }
            }

            if (envelope.Body.Item is SeatAvailabilityRS seatAvailability)
            {
                errorObject = seatAvailability.Items?.FirstOrDefault();
            }

            if (envelope.Body.Item is BaggageAllowanceRS baggageAllowance)
            {
                errorObject = baggageAllowance.Items?.FirstOrDefault();
            }

            if (envelope.Body.Item is BaggageChargesRS baggageCharges)
            {
                errorObject = baggageCharges.Items?.FirstOrDefault();
            }

            if (envelope.Body.Item is AirShoppingRS airShopping)
            {
                errorObject = airShopping.Items?.FirstOrDefault();
            }

            if (errorObject is ErrorsType error)
            {
                throw new IbsException(error.Error.FirstOrDefault(), Session.Language);

            }
        }
    }
}
