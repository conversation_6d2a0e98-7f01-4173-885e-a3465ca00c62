//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Ubimecs.IBS.Services.ClaimFlightService {
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/common/type/")]
    public partial class WebServiceException : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string faultcodeField;
        
        private string faultstringField;
        
        private string[] faultdataField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string faultcode {
            get {
                return this.faultcodeField;
            }
            set {
                this.faultcodeField = value;
                this.RaisePropertyChanged("faultcode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string faultstring {
            get {
                return this.faultstringField;
            }
            set {
                this.faultstringField = value;
                this.RaisePropertyChanged("faultstring");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("faultdata", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string[] faultdata {
            get {
                return this.faultdataField;
            }
            set {
                this.faultdataField = value;
                this.RaisePropertyChanged("faultdata");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/common/type/")]
    public partial class WebServiceTransactionHeader : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string transactionIDField;
        
        private string userNameField;
        
        private string channelUserCodeField;
        
        private string transactionTokenField;
        
        private System.DateTime timeStampField;
        
        private string deviceIdField;
        
        private string deviceIPField;
        
        private string deviceOperatingSystemField;
        
        private string deviceLocationLatitudeField;
        
        private string deviceLocationLongitudeField;
        
        private string deviceCountryCodeField;
        
        private string additionalInfoField;
        
        private string remarksField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string transactionID {
            get {
                return this.transactionIDField;
            }
            set {
                this.transactionIDField = value;
                this.RaisePropertyChanged("transactionID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string userName {
            get {
                return this.userNameField;
            }
            set {
                this.userNameField = value;
                this.RaisePropertyChanged("userName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string channelUserCode {
            get {
                return this.channelUserCodeField;
            }
            set {
                this.channelUserCodeField = value;
                this.RaisePropertyChanged("channelUserCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public string transactionToken {
            get {
                return this.transactionTokenField;
            }
            set {
                this.transactionTokenField = value;
                this.RaisePropertyChanged("transactionToken");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime timeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
                this.RaisePropertyChanged("timeStamp");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string deviceId {
            get {
                return this.deviceIdField;
            }
            set {
                this.deviceIdField = value;
                this.RaisePropertyChanged("deviceId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string deviceIP {
            get {
                return this.deviceIPField;
            }
            set {
                this.deviceIPField = value;
                this.RaisePropertyChanged("deviceIP");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string deviceOperatingSystem {
            get {
                return this.deviceOperatingSystemField;
            }
            set {
                this.deviceOperatingSystemField = value;
                this.RaisePropertyChanged("deviceOperatingSystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string deviceLocationLatitude {
            get {
                return this.deviceLocationLatitudeField;
            }
            set {
                this.deviceLocationLatitudeField = value;
                this.RaisePropertyChanged("deviceLocationLatitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string deviceLocationLongitude {
            get {
                return this.deviceLocationLongitudeField;
            }
            set {
                this.deviceLocationLongitudeField = value;
                this.RaisePropertyChanged("deviceLocationLongitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string deviceCountryCode {
            get {
                return this.deviceCountryCodeField;
            }
            set {
                this.deviceCountryCodeField = value;
                this.RaisePropertyChanged("deviceCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string additionalInfo {
            get {
                return this.additionalInfoField;
            }
            set {
                this.additionalInfoField = value;
                this.RaisePropertyChanged("additionalInfo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public string remarks {
            get {
                return this.remarksField;
            }
            set {
                this.remarksField = value;
                this.RaisePropertyChanged("remarks");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class AccruedPointDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string pointTypeField;
        
        private double pointsField;
        
        private string bonusNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string pointType {
            get {
                return this.pointTypeField;
            }
            set {
                this.pointTypeField = value;
                this.RaisePropertyChanged("pointType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double points {
            get {
                return this.pointsField;
            }
            set {
                this.pointsField = value;
                this.RaisePropertyChanged("points");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string bonusName {
            get {
                return this.bonusNameField;
            }
            set {
                this.bonusNameField = value;
                this.RaisePropertyChanged("bonusName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class RetroClaimPartnerAttribute : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attributeKeyField;
        
        private string attributeNameField;
        
        private string attributeValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string attributeKey {
            get {
                return this.attributeKeyField;
            }
            set {
                this.attributeKeyField = value;
                this.RaisePropertyChanged("attributeKey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string attributeName {
            get {
                return this.attributeNameField;
            }
            set {
                this.attributeNameField = value;
                this.RaisePropertyChanged("attributeName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string attributeValue {
            get {
                return this.attributeValueField;
            }
            set {
                this.attributeValueField = value;
                this.RaisePropertyChanged("attributeValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class NonAirRetroclaimRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string billNumberField;
        
        private string amountSpentField;
        
        private string siteidField;
        
        private string partnerCategoryField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string billNumber {
            get {
                return this.billNumberField;
            }
            set {
                this.billNumberField = value;
                this.RaisePropertyChanged("billNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string amountSpent {
            get {
                return this.amountSpentField;
            }
            set {
                this.amountSpentField = value;
                this.RaisePropertyChanged("amountSpent");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string siteid {
            get {
                return this.siteidField;
            }
            set {
                this.siteidField = value;
                this.RaisePropertyChanged("siteid");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public string partnerCategory {
            get {
                return this.partnerCategoryField;
            }
            set {
                this.partnerCategoryField = value;
                this.RaisePropertyChanged("partnerCategory");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class AirRetroclaimRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string carrierCodeField;
        
        private string flightNumberField;
        
        private string originAirportField;
        
        private string destinationAirportField;
        
        private string cabinClassField;
        
        private string bookingClassField;
        
        private string originalBookingClassField;
        
        private string ticketNumberField;
        
        private string seatNumberField;
        
        private string ticketedFirstNameField;
        
        private string ticketedLastNameField;
        
        private string ticketTitleField;
        
        private string amountSpentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string carrierCode {
            get {
                return this.carrierCodeField;
            }
            set {
                this.carrierCodeField = value;
                this.RaisePropertyChanged("carrierCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string flightNumber {
            get {
                return this.flightNumberField;
            }
            set {
                this.flightNumberField = value;
                this.RaisePropertyChanged("flightNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string originAirport {
            get {
                return this.originAirportField;
            }
            set {
                this.originAirportField = value;
                this.RaisePropertyChanged("originAirport");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string destinationAirport {
            get {
                return this.destinationAirportField;
            }
            set {
                this.destinationAirportField = value;
                this.RaisePropertyChanged("destinationAirport");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=4)]
        public string cabinClass {
            get {
                return this.cabinClassField;
            }
            set {
                this.cabinClassField = value;
                this.RaisePropertyChanged("cabinClass");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string bookingClass {
            get {
                return this.bookingClassField;
            }
            set {
                this.bookingClassField = value;
                this.RaisePropertyChanged("bookingClass");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string originalBookingClass {
            get {
                return this.originalBookingClassField;
            }
            set {
                this.originalBookingClassField = value;
                this.RaisePropertyChanged("originalBookingClass");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string ticketNumber {
            get {
                return this.ticketNumberField;
            }
            set {
                this.ticketNumberField = value;
                this.RaisePropertyChanged("ticketNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string seatNumber {
            get {
                return this.seatNumberField;
            }
            set {
                this.seatNumberField = value;
                this.RaisePropertyChanged("seatNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string ticketedFirstName {
            get {
                return this.ticketedFirstNameField;
            }
            set {
                this.ticketedFirstNameField = value;
                this.RaisePropertyChanged("ticketedFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string ticketedLastName {
            get {
                return this.ticketedLastNameField;
            }
            set {
                this.ticketedLastNameField = value;
                this.RaisePropertyChanged("ticketedLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string ticketTitle {
            get {
                return this.ticketTitleField;
            }
            set {
                this.ticketTitleField = value;
                this.RaisePropertyChanged("ticketTitle");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public string amountSpent {
            get {
                return this.amountSpentField;
            }
            set {
                this.amountSpentField = value;
                this.RaisePropertyChanged("amountSpent");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class AccrualResponseDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string responseCodeField;
        
        private string responseDescriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string responseCode {
            get {
                return this.responseCodeField;
            }
            set {
                this.responseCodeField = value;
                this.RaisePropertyChanged("responseCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string responseDescription {
            get {
                return this.responseDescriptionField;
            }
            set {
                this.responseDescriptionField = value;
                this.RaisePropertyChanged("responseDescription");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class AccrualInternalIdentifier : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string ianField;
        
        private string oprahField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string ian {
            get {
                return this.ianField;
            }
            set {
                this.ianField = value;
                this.RaisePropertyChanged("ian");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string oprah {
            get {
                return this.oprahField;
            }
            set {
                this.oprahField = value;
                this.RaisePropertyChanged("oprah");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/wsdl", ConfigurationName="ClaimFlightService.RaiseRetroClaimRequest")]
    public interface RaiseRetroClaimRequest {
        
        // CODEGEN: Generating message contract since the operation raiseRetroClaimRequest is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Ubimecs.IBS.Services.ClaimFlightService.WebServiceException), Action="", Name="RetroClaimWebServiceException", Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData1 raiseRetroClaimRequest(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData1> raiseRetroClaimRequestAsync(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1 request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class RetroClaimRequestData : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string companyCodeField;
        
        private string retroclaimReferenceNumberField;
        
        private string membershipNumberField;
        
        private string businessTypeField;
        
        private string activityDateField;
        
        private string givenNameField;
        
        private string familyNameField;
        
        private string retroClaimStatusField;
        
        private string claimDateField;
        
        private string partnerCodeField;
        
        private string confirmationSentDateField;
        
        private string confirmedDateField;
        
        private string processingDateField;
        
        private string reasonCodeField;
        
        private string remarksField;
        
        private string requiredActionField;
        
        private string voucherIdentifierField;
        
        private AccrualInternalIdentifier[] accrualInternalIdentifierField;
        
        private AccrualResponseDetail[] accrualResponseDetailField;
        
        private AirRetroclaimRequest airRetroclaimRequestField;
        
        private NonAirRetroclaimRequest nonAirRetroclaimRequestField;
        
        private RetroClaimPartnerAttribute[] retroClaimPartnerAttributesField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string companyCode {
            get {
                return this.companyCodeField;
            }
            set {
                this.companyCodeField = value;
                this.RaisePropertyChanged("companyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string retroclaimReferenceNumber {
            get {
                return this.retroclaimReferenceNumberField;
            }
            set {
                this.retroclaimReferenceNumberField = value;
                this.RaisePropertyChanged("retroclaimReferenceNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string businessType {
            get {
                return this.businessTypeField;
            }
            set {
                this.businessTypeField = value;
                this.RaisePropertyChanged("businessType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string activityDate {
            get {
                return this.activityDateField;
            }
            set {
                this.activityDateField = value;
                this.RaisePropertyChanged("activityDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string givenName {
            get {
                return this.givenNameField;
            }
            set {
                this.givenNameField = value;
                this.RaisePropertyChanged("givenName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string familyName {
            get {
                return this.familyNameField;
            }
            set {
                this.familyNameField = value;
                this.RaisePropertyChanged("familyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string retroClaimStatus {
            get {
                return this.retroClaimStatusField;
            }
            set {
                this.retroClaimStatusField = value;
                this.RaisePropertyChanged("retroClaimStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string claimDate {
            get {
                return this.claimDateField;
            }
            set {
                this.claimDateField = value;
                this.RaisePropertyChanged("claimDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string partnerCode {
            get {
                return this.partnerCodeField;
            }
            set {
                this.partnerCodeField = value;
                this.RaisePropertyChanged("partnerCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string confirmationSentDate {
            get {
                return this.confirmationSentDateField;
            }
            set {
                this.confirmationSentDateField = value;
                this.RaisePropertyChanged("confirmationSentDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string confirmedDate {
            get {
                return this.confirmedDateField;
            }
            set {
                this.confirmedDateField = value;
                this.RaisePropertyChanged("confirmedDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public string processingDate {
            get {
                return this.processingDateField;
            }
            set {
                this.processingDateField = value;
                this.RaisePropertyChanged("processingDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=13)]
        public string reasonCode {
            get {
                return this.reasonCodeField;
            }
            set {
                this.reasonCodeField = value;
                this.RaisePropertyChanged("reasonCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=14)]
        public string remarks {
            get {
                return this.remarksField;
            }
            set {
                this.remarksField = value;
                this.RaisePropertyChanged("remarks");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=15)]
        public string requiredAction {
            get {
                return this.requiredActionField;
            }
            set {
                this.requiredActionField = value;
                this.RaisePropertyChanged("requiredAction");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=16)]
        public string voucherIdentifier {
            get {
                return this.voucherIdentifierField;
            }
            set {
                this.voucherIdentifierField = value;
                this.RaisePropertyChanged("voucherIdentifier");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("accrualInternalIdentifier", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=17)]
        public AccrualInternalIdentifier[] accrualInternalIdentifier {
            get {
                return this.accrualInternalIdentifierField;
            }
            set {
                this.accrualInternalIdentifierField = value;
                this.RaisePropertyChanged("accrualInternalIdentifier");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("accrualResponseDetail", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=18)]
        public AccrualResponseDetail[] accrualResponseDetail {
            get {
                return this.accrualResponseDetailField;
            }
            set {
                this.accrualResponseDetailField = value;
                this.RaisePropertyChanged("accrualResponseDetail");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=19)]
        public AirRetroclaimRequest airRetroclaimRequest {
            get {
                return this.airRetroclaimRequestField;
            }
            set {
                this.airRetroclaimRequestField = value;
                this.RaisePropertyChanged("airRetroclaimRequest");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=20)]
        public NonAirRetroclaimRequest nonAirRetroclaimRequest {
            get {
                return this.nonAirRetroclaimRequestField;
            }
            set {
                this.nonAirRetroclaimRequestField = value;
                this.RaisePropertyChanged("nonAirRetroclaimRequest");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("retroClaimPartnerAttributes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=21)]
        public RetroClaimPartnerAttribute[] retroClaimPartnerAttributes {
            get {
                return this.retroClaimPartnerAttributesField;
            }
            set {
                this.retroClaimPartnerAttributesField = value;
                this.RaisePropertyChanged("retroClaimPartnerAttributes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/")]
    public partial class RetroClaimResponseData : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string membershipNumberField;
        
        private string retroclaimReferenceNumberField;
        
        private string statusField;
        
        private string reasonField;
        
        private string businessTypeField;
        
        private string activityDateField;
        
        private string givenNameField;
        
        private string familyNameField;
        
        private string claimDateField;
        
        private string partnerCodeField;
        
        private string ianValueField;
        
        private string oprahValueField;
        
        private AccruedPointDetail[] accruedPointDetailsField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string retroclaimReferenceNumber {
            get {
                return this.retroclaimReferenceNumberField;
            }
            set {
                this.retroclaimReferenceNumberField = value;
                this.RaisePropertyChanged("retroclaimReferenceNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string reason {
            get {
                return this.reasonField;
            }
            set {
                this.reasonField = value;
                this.RaisePropertyChanged("reason");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=4)]
        public string businessType {
            get {
                return this.businessTypeField;
            }
            set {
                this.businessTypeField = value;
                this.RaisePropertyChanged("businessType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string activityDate {
            get {
                return this.activityDateField;
            }
            set {
                this.activityDateField = value;
                this.RaisePropertyChanged("activityDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string givenName {
            get {
                return this.givenNameField;
            }
            set {
                this.givenNameField = value;
                this.RaisePropertyChanged("givenName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string familyName {
            get {
                return this.familyNameField;
            }
            set {
                this.familyNameField = value;
                this.RaisePropertyChanged("familyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string claimDate {
            get {
                return this.claimDateField;
            }
            set {
                this.claimDateField = value;
                this.RaisePropertyChanged("claimDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string partnerCode {
            get {
                return this.partnerCodeField;
            }
            set {
                this.partnerCodeField = value;
                this.RaisePropertyChanged("partnerCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string ianValue {
            get {
                return this.ianValueField;
            }
            set {
                this.ianValueField = value;
                this.RaisePropertyChanged("ianValue");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string oprahValue {
            get {
                return this.oprahValueField;
            }
            set {
                this.oprahValueField = value;
                this.RaisePropertyChanged("oprahValue");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("accruedPointDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public AccruedPointDetail[] accruedPointDetails {
            get {
                return this.accruedPointDetailsField;
            }
            set {
                this.accruedPointDetailsField = value;
                this.RaisePropertyChanged("accruedPointDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class RetroClaimRequestData1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/", Order=0)]
        public Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData RetroClaimRequestData;
        
        public RetroClaimRequestData1() {
        }
        
        public RetroClaimRequestData1(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData RetroClaimRequestData) {
            this.RetroClaimRequestData = RetroClaimRequestData;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class RetroClaimResponseData1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iloyal/accrual/retroclaimrequest/type/", Order=0)]
        public Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData RetroClaimResponseData;
        
        public RetroClaimResponseData1() {
        }
        
        public RetroClaimResponseData1(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData RetroClaimResponseData) {
            this.RetroClaimResponseData = RetroClaimResponseData;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public interface RaiseRetroClaimRequestChannel : Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public partial class RaiseRetroClaimRequestClient : System.ServiceModel.ClientBase<Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest>, Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest {
        
        public RaiseRetroClaimRequestClient() {
        }
        
        public RaiseRetroClaimRequestClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public RaiseRetroClaimRequestClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RaiseRetroClaimRequestClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RaiseRetroClaimRequestClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData1 Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest.raiseRetroClaimRequest(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1 request) {
            return base.Channel.raiseRetroClaimRequest(request);
        }
        
        public Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData raiseRetroClaimRequest(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData RetroClaimRequestData) {
            Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1 inValue = new Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1();
            inValue.RetroClaimRequestData = RetroClaimRequestData;
            Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData1 retVal = ((Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest)(this)).raiseRetroClaimRequest(inValue);
            return retVal.RetroClaimResponseData;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData1> Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest.raiseRetroClaimRequestAsync(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1 request) {
            return base.Channel.raiseRetroClaimRequestAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.ClaimFlightService.RetroClaimResponseData1> raiseRetroClaimRequestAsync(Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData RetroClaimRequestData) {
            Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1 inValue = new Ubimecs.IBS.Services.ClaimFlightService.RetroClaimRequestData1();
            inValue.RetroClaimRequestData = RetroClaimRequestData;
            return ((Ubimecs.IBS.Services.ClaimFlightService.RaiseRetroClaimRequest)(this)).raiseRetroClaimRequestAsync(inValue);
        }
    }
}
