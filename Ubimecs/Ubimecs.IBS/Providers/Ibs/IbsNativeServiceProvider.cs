using AncillaryPortServiceReference;
using Passbook.Generator;
using PricePortServiceReference;
using System.Diagnostics;
using System.Diagnostics.Tracing;
using System.Globalization;
using System.Linq;
using System.Net;
using Ubimecs.IBS.Mappers.NativeApiMappers;
using Ubimecs.IBS.Mappers.RequestMappers;
using Ubimecs.IBS.Models;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Models.Response;
using Ubimecs.IBS.Requesters;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Caching.Models;
using Ubimecs.Infrastructure.Contracts.File;
using Ubimecs.Infrastructure.CMS.Contracts;
using Ubimecs.Infrastructure.CMS.Models;
using Ubimecs.Infrastructure.CMS.Services;
using Ubimecs.Infrastructure.Contracts.ChannelConfig;
using Ubimecs.Infrastructure.Contracts.File;
using Ubimecs.Infrastructure.Contracts.Gda;
using Ubimecs.Infrastructure.Contracts.InvoicePortal;
using Ubimecs.Infrastructure.CRM.Configuration;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Exceptions;
using Ubimecs.Infrastructure.CRM.Extensions.FlightDetail;
using Ubimecs.Infrastructure.CRM.Extensions.TravelCompanion;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses.Dto;
using Ubimecs.Infrastructure.CSM.Contracts;
using Ubimecs.Infrastructure.CSM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CSM.Models.Next4Biz.Responses;
using Ubimecs.Infrastructure.Database.DatabaseContext;
using Ubimecs.Infrastructure.Extensions;
using Ubimecs.Infrastructure.FileService.Constants;
using Ubimecs.Infrastructure.FileService.Contracts;
using Ubimecs.Infrastructure.Ftp.Contracts;
using Ubimecs.Infrastructure.HolidayExtras.Contracts;
using Ubimecs.Infrastructure.HolidayExtras.Models;
using Ubimecs.Infrastructure.HolidayExtras.Models.Mappings;
using Ubimecs.Infrastructure.HolidayExtras.Models.Requests;
using Ubimecs.Infrastructure.HolidayExtras.Models.Responses;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Configuration;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.GroupBooking;
using Ubimecs.Infrastructure.Models.DTO.Seat;
using Ubimecs.Infrastructure.Models.DTO.Service;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.Other.InvoicePortal.Request;
using Ubimecs.Infrastructure.Models.Other.InvoicePortal.Response;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Request.Gda;
using Ubimecs.Infrastructure.Models.Request.Offer;
using Ubimecs.Infrastructure.Models.Response;
using Ubimecs.Infrastructure.Models.Response.Agency;
using Ubimecs.Infrastructure.Models.Response.Gda;
using Ubimecs.Infrastructure.Models.Response.OfferWidgets;
using Ubimecs.Infrastructure.Models.Response.Pagination;
using Ubimecs.Infrastructure.Providers;
using Convert = System.Convert;
using Flight = Ubimecs.Infrastructure.Ftp.Models.FareCache.Flight;
using ModifyBookingRQ = ReservationsPortServiceReference.ModifyBookingRQ;
using Ubimecs.Infrastructure.Models.DTO.GroupBooking;
using Ubimecs.Infrastructure.HolidayExtras.Contracts;
using Ubimecs.Infrastructure.HolidayExtras.Models;
using Ubimecs.Infrastructure.HolidayExtras.Models.Mappings;
using Ubimecs.Infrastructure.HolidayExtras.Models.Requests;
using Ubimecs.Infrastructure.HolidayExtras.Models.Responses;
using Ubimecs.Infrastructure.Models.Response.Pagination;
using Ubimecs.Infrastructure.Extensions;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.Infrastructure.Models.Configuration;
using Ubimecs.Infrastructure.Models.DTO.Seat;
using Ubimecs.Infrastructure.Models.Other.InvoicePortal.Request;
using Ubimecs.Infrastructure.Models.Other.InvoicePortal.Response;
using Ubimecs.Infrastructure.Models.Request.Gda;
using Ubimecs.Infrastructure.Models.Response.Gda;
using LogoutResponse = Ubimecs.Infrastructure.Models.Response.Gda.LogoutResponse;
using Ubimecs.Infrastructure.Communication.Contracts;
using Ubimecs.Infrastructure.Communication.Models.MobilDev.Requests;
using Ubimecs.Infrastructure.Database.DatabaseContext;
using Ubimecs.Infrastructure.FileService.Constants;
using Ubimecs.Infrastructure.FileService.Contracts;
using Ubimecs.Infrastructure.Models.DTO.Gda;
using FileResponse = Ubimecs.Infrastructure.Models.Response.FileResponse;
using RegisterRequest = Ubimecs.Infrastructure.Models.Request.RegisterRequest;

namespace Ubimecs.IBS.Providers.Ibs
{
    public class IbsNativeServiceProvider : IUbimecsServiceProvider
    {
        private ISftpService _sftpService;
        private IExcelFileReader _excelFileReader;
        private IAirportService _airportService;
        private ISpecialOfferService _specialOfferService;
        private readonly IChannelConfigurationProvider _channelConfigProvider;
        public IbsNativeServiceProvider(SessionCache session, string serviceName)
        {
            Session = session;
            ServiceName = serviceName;
            _channelConfigProvider = new ChannelConfigurationProvider(session);
        }
        public string ServiceName { get; }
        private SessionCache Session { get; set; }
        public AddFamilyMemberResponse AddFamilyMember(AddFamiltyMemberRequest request)
        {
            throw new NotImplementedException();
        }

        public AddFriendResponse AddFriend(AddFriendRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<PrintBoardingPassResponse> SmsPrintBoardingPass(SmsPrintBoardingPassRequest request)
        {
            var channelConfig = CachedData.GetChannelConfiguration(EnvironmentTypeEnum.WebBrowser, ChannelTypeEnum.WebCheckin);
            
            var ibsNativeRequest = CheckInMappers.Map(request,channelConfig);

            var response = IbsNativeRequester.CallCheckInSoapService(ibsNativeRequest, GeneralConstants.CHECKIN_PORT_URL,channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) =>  client.printBoardingPassAsync(req), Session?.SessionId);
            if (response.CHK_PrintBoardingPassRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CHK_PrintBoardingPassRS.ErrorType.errorCode, response.CHK_PrintBoardingPassRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map(Session);
        }

        public AddToWalletResponse AddToWallet(AddToWalletRequest request)
        {
             string stringGeneratedPass = "";
            AddToWalletSignRequest addToWalletSignRequest = null;
            try
            {             
                 var boardingPassInfo = Session.CurrentFlow.BoardingPasses?.FirstOrDefault(f => f.SegmentTmobId == request.FlightSegmentTmobId).BoardingPasses?.FirstOrDefault(t => t.PaxKey == request.PassengerId);
                 var segmentInfo = Session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == request.FlightSegmentTmobId);
                 DateTimeOffset relevantDate = segmentInfo.DepartureDate.Date.Add(DateTime.Parse(segmentInfo.DepartureTime).TimeOfDay);
                 var country = CachedData.GetCountryCodeByAirportCode(boardingPassInfo.BoardpointInformation?.AirportCode);
                 var offsetValue = Ubimecs.Infrastructure.Utilities.Utility.GetOffsetTimeForUtc(country);
                 DateTimeOffset relevantDateChanged = relevantDate.ToOffset(new TimeSpan(offsetValue, 0, 0));
                 DateTimeOffset relevateDateDateChanged = relevantDateChanged.AddHours(3 - offsetValue);
                 addToWalletSignRequest = new AddToWalletSignRequest(boardingPassInfo.FlightInformation.FlightDate.ToString(),
                                                                         boardingPassInfo.BoardpointInformation.AirportName,
                                                                         boardingPassInfo.BoardpointInformation.AirportCode,
                                                                         boardingPassInfo.OffpointInformation.AirportName,
                                                                         boardingPassInfo.OffpointInformation.AirportCode,
                                                                         boardingPassInfo.FlightInformation?.AirlineCode + " " + boardingPassInfo.FlightInformation?.FlightNumber,
                                                                         boardingPassInfo.DepartureTime.ToString("HH:mm"),
                                                                         boardingPassInfo.ArrivalTime.ToString("HH:mm"),
                                                                         boardingPassInfo.BoardingTime.ToString("HH:mm"),
                                                                         boardingPassInfo.SeatInformation.NewSeatNumber,
                                                                         boardingPassInfo.GuestName,
                                                                         boardingPassInfo.Terminal,
                                                                         boardingPassInfo.GateNumber,
                                                                         boardingPassInfo.IFEAccessCode,
                                                                         relevateDateDateChanged.ToString(),
                                                                         boardingPassInfo.Barcode);
                 
                 PassGenerator generator = new PassGenerator();
                 var passGeneratorRequest = CheckInMappers.MapAddToWalletRequest(request, Session);
                 byte[] generatedPass = new byte[] { };
                 generatedPass = generator.Generate(passGeneratorRequest);
                 stringGeneratedPass = Convert.ToBase64String(generatedPass);

                 //Wallet ı test etmek için file generate edildi. https://pkvd.app/ web sitesi içerisine pkpass dosyası upload edildiği zaman görseline bakılabilir. 
                 /*string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                 string outputPath = System.IO.Path.Combine(baseDirectory, "test.pkpass");
                 System.IO.File.WriteAllBytes(outputPath, generatedPass);*/
             }
             catch (Exception ex)
             {
                 Logger.Instance.ErrorLog(Session.SessionId, ServiceName, ex);
                 stringGeneratedPass = null;
             }
            
             return new AddToWalletResponse
             {
                 Pass = stringGeneratedPass
             };
        }

        public ChangeCurrencyResponse BookingChangeCurrency()
        {
            var response = new ChangeCurrencyResponse();
            response.FlightServices = Session.CurrentFlow.Services;
            var tmobId = Session.CurrentFlow.PNR.Flights.FirstOrDefault()?.TmobId;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, tmobId), null);

            var priceInformation = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (priceInformation.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsException(priceInformation.ConfirmPriceRS.ErrorType.errorCode, priceInformation.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            response.PriceInformation = PriceMappers.MapToBasePriceResponse(priceInformation, Session);

            return response;
        }

        public BasePriceResponse BookingGetInstallmentFee(GetInstallmentFeeRequest request)
        {
            // TODO : Session dan payment bilgileri alınacak ve ilgili service çağrılacak. (M.E.)
            var tmobId = Session.CurrentFlow.PNR.Flights.FirstOrDefault()?.TmobId;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, tmobId), null);
            var priceInformation = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (priceInformation.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsException(priceInformation.ConfirmPriceRS.ErrorType.errorCode, priceInformation.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(priceInformation, Session);
        }

        public BasePriceResponse BookingReSelectMeal(BookingReSelectMealRequest request)
        {
            var segmentTmobId = request.SegmentMeals.Select(segment => segment.SegmentTmobId).FirstOrDefault();
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.Segments.Any(a => a.TmobId == segmentTmobId));
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, flight.TmobId), null);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse BookingReSelectSeat(BookingReSelectSeatRequest request)
        {
            var segmentTmobId = request.SegmentSeats.Select(segment => segment.SegmentTmobId).FirstOrDefault();
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.Segments.Any(a => a.TmobId == segmentTmobId));
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, flight.TmobId), null);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }

            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse BookingReSetSportEquipment(BookingReSetSportEquipmentRequest request)
        {
            var tmobId = request.SportEquipments.FirstOrDefault()?.TmobId;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, tmobId), null);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);

            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BookingSearchFlightResponse BookingSearchFlight(BookingSearchFlightRequest request)
        {
            //Session.CurrentFlow.BookingSearchFlightResponse = null;
            //var parsedDate = DateTime.ParseExact(request., "yyyy-MM-dd", CultureInfo.GetCultureInfo("en-US"));
            var lastSearchFlightDates = Session.CurrentFlow.BookingSearchFlightResponse
                ?.Flights
                ?.SelectMany(t => t.FlightOptions
                    .Select(k => DateTime.ParseExact(
                        k.FlightDate?.ToString("dd.MM.yyyy"),
                        "dd.MM.yyyy",
                        CultureInfo.InvariantCulture
                    ))
                )
                .Distinct()
                .ToList();
            if (lastSearchFlightDates != null && lastSearchFlightDates.Count > 0 && request.Flights.Any(t => Session.CurrentFlow.BookingSearchFlightResponse.Flights.Select(k => k.ArrivalCode).Contains(t.ArrivalAirport)) && request.Flights.Any(t => Session.CurrentFlow.BookingSearchFlightResponse.Flights.Select(k => k.DepartureCode).Contains(t.DepartureAirport)))
            {
                var currentSearchFlightDates = request.Flights
                    ?.SelectMany(t =>
                    {
                        DateTime departureDate;
                        bool isDepartureDateValid = DateTime.TryParseExact(
                            t.DepartureDate.ToString("dd.MM.yyyy"),
                            "dd.MM.yyyy",
                            CultureInfo.InvariantCulture,
                            DateTimeStyles.None,
                            out departureDate
                        );

                        DateTime? returnDepartureDate = null;
                        if (t.ReturnDepartureDate != null)
                        {
                            DateTime parsedReturnDate;
                            bool isReturnDateValid = DateTime.TryParseExact(
                                t.ReturnDepartureDate.Value.ToString("dd.MM.yyyy"),
                                "dd.MM.yyyy",
                                CultureInfo.InvariantCulture,
                                DateTimeStyles.None,
                                out parsedReturnDate
                            );
                            returnDepartureDate = isReturnDateValid ? parsedReturnDate : (DateTime?)null;
                        }

                        return new[]
                        {
                            isDepartureDateValid ? departureDate : (DateTime?)null,
                            returnDepartureDate
                        };
                    })
                    .Where(date => date.HasValue) // Null olmayan tarihleri filtrele
                    .Select(date => date.Value) // DateTime? -> DateTime
                    .ToList(); foreach (var date in lastSearchFlightDates)
                {

                    if (!currentSearchFlightDates.Any(t => t == date))
                    {
                        var deletingFlight = Session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(t => t.Segments?.FirstOrDefault()?.DepartureDate == Convert.ToDateTime(date));
                        var deletingFlightTmobId = deletingFlight?.TmobId;
                        var deletingFlightSegmentTmobIds = deletingFlight?.Segments?.Select(t => t.TmobId).ToList();
                        if (deletingFlightTmobId != null && deletingFlightSegmentTmobIds.Count > 0 && deletingFlightSegmentTmobIds != null)
                        {
                            Session.CurrentFlow?.PNR?.Flights?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.SportEquipments?.RemoveAll(t => t.FlightTmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.Seats?.RemoveAll(t => deletingFlightSegmentTmobIds.Contains(t.SegmentTmobId));
                            Session.CurrentFlow?.PNR?.ExtraBaggages?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.Coronas?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.Meals?.RemoveAll(t => deletingFlightSegmentTmobIds.Contains(t.SegmentTmobId));
                            Session.CurrentFlow?.PNR?.IfeServices?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                        }
                    }
                }
            }
            else
            {
                Session.CurrentFlow.PNR.Flights.Clear();
                Session.CurrentFlow.PNR.ClearServices();
            }

            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var IbsRequest = request.MapSearchFlightRequest(Session, channelConfig);
            var response = IbsNativeRequester.CallAvailabilitySoapService(IbsRequest, GeneralConstants.AVAILABILITY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.getAirAvailabilityAsync(req), Session?.SessionId);
            if (response.AirAvailabilityRS.ErrorType != null)
            {
                throw new IbsNativeException(response.AirAvailabilityRS.ErrorType.errorCode, response.AirAvailabilityRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.AirAvailabilityRS, IbsDataTypeEnum.ShopAirResponse);
            var result = response.MapSearchFlightResponse(request, Session);

            var timeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
            var now = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);
            foreach (var flight in result.Flights)
            {
                var filteredOptions = flight.FlightOptions
                    .Where(opt => opt.Segments.All(seg => seg.CompleteDepartureTime > now.AddHours(1)))
                    .ToList();

                flight.FlightOptions = filteredOptions;


                foreach (var flightOption in flight.FlightOptions)
                {
                    ProcessFlightSegments(flightOption, flightOption.Id);
                }

                var sortedOptions = flight.FlightOptions.OrderBy(opt => opt.IsSoldOut)
                    .ThenBy(opt => opt.IsConnectedFlight)
                    .ThenBy(opt => opt.Segments.FirstOrDefault()?.CompleteDepartureTime)
                    .ToList();

                flight.FlightOptions = sortedOptions;
            }

            Session.CurrentFlow.PNR.Currency = result.Currency ?? CurrencyTypeEnum.TRY.ToString();
            Session.CurrentFlow.PNR.PromoCode = result.PromoCode;
            Session.CurrentFlow.BookingSearchFlightResponse = result;
            Session.CurrentFlow.PNR.Passengers = result.Passengers.Select(s => new Ubimecs.Infrastructure.Models.PNR.Passenger
            {
                GlobalId = s.Id,
                LocalId = s.Id,
                Name = null,
                PassengerType = (PassengerTypeEnum)Enum.Parse(typeof(PassengerTypeEnum), s.PassengerType, true),
                Surname = null
            }).ToList();

            // Check if a flight has been selected before and if there are any flight options available
            if (Ubimecs.Infrastructure.Utilities.Utility.FlightSelectedBefore(Session) &&
                result.Flights.SelectMany(t => t.FlightOptions).Any(k => k != null))
            {
                // Extract flight dates (departure and return) from the request
                var currentSearchFlightDates = request.Flights?
                    .SelectMany(t => new[]
                    {
                        DateTime.ParseExact(
                            t.DepartureDate.ToString("dd.MM.yyyy"),
                            "dd.MM.yyyy",
                            CultureInfo.InvariantCulture
                        ),
                        t.ReturnDepartureDate.HasValue
                            ? DateTime.ParseExact(
                                t.ReturnDepartureDate.Value.ToString("dd.MM.yyyy"),
                                "dd.MM.yyyy",
                                CultureInfo.InvariantCulture
                            )
                            : (DateTime?)null
                    })
                    .Where(date => date.HasValue)
                    .Select(date => date.Value)
                    .ToList();


                var selectingFlights = new List<string>();

                // Check if there are any valid search flight dates
                if (currentSearchFlightDates != null && currentSearchFlightDates.Any())
                {
                    // Iterate over each search flight date
                    foreach (var date in currentSearchFlightDates)
                    {
                        // Check if there are any flights in the current PNR session
                        var pnrFlights = Session.CurrentFlow?.PNR?.Flights;
                        if (pnrFlights != null && pnrFlights.Any(flight => flight != null))
                        {
                            // Find the flight with a matching departure date
                            var existingFlight = pnrFlights
                                .FirstOrDefault(flight => flight.Segments
                                    .FirstOrDefault()?.DepartureDate.Date == date);

                            // If a matching flight is found, add its ID to the selectingFlights list
                            if (existingFlight != null)
                            {
                                selectingFlights.Add(existingFlight.TmobId);
                            }
                        }
                    }
                }

                // If there are any selected flights, proceed with booking
                if (selectingFlights.Any())
                {
                    BookingSelectFlight(new FlightSelectRequest { FlightIds = selectingFlights });
                }
            }

            result.Flights = result.Flights
                .Where(flight => flight.FlightOptions != null && flight.FlightOptions.Any())
                .ToList();

            return result;
        }

        public BookingSelectBundleResponse BookingSelectBundle(BookingSelectBundleRequest request)
        {
            BookingSelectBundleResponse selectBundleResponse = new BookingSelectBundleResponse();
            List<SessionPnrFlightDTO> flights = new List<SessionPnrFlightDTO>();
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            
            if (request.IsUpgradeBundle && Session.CurrentFlow.FlightsType != FlightListType.Direkt)
            {
                flights = Session.CurrentFlow.PNR.Flights;
            }
            else
            {
                var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == request.TmobId);
                if (flight != null)
                {
                    flights.Add(flight);
                }
            }

            Session.CurrentFlow.PNR.FlexServices.Clear();

            foreach (var flight in flights)
            {
                bool isbundleTypeChanging = flight.SelectedBundleCode != request.BundleCode;
                flight.SelectedBundleCode = request.BundleCode;
                if (request.IsUpgradeBundle && Session.CurrentFlow.FlightsType != FlightListType.Direkt)
                {
                    isbundleTypeChanging = true;
                    flight.SelectedBundleCode = Session.CurrentFlow.PNR.Bundles
                        .FirstOrDefault(x => x.TmobId == flight.TmobId)?.Code;

                }

                Session.CurrentFlow.PNR.Meals.RemoveAll(r => flight.Segments.Any(a => a.TmobId == r.SegmentTmobId));

                PricePortServiceReference.ConfirmPriceRQ confirmPriceRQ = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, request.TmobId));

                Session.CurrentFlow.SaveIbsData(confirmPriceRQ, IbsDataTypeEnum.LatestOfferPriceRequest);

                var response = IbsNativeRequester.CallPriceSoapService(confirmPriceRQ, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
                if (response.ConfirmPriceRS.ErrorType != null)
                {
                    throw new IbsException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
                }
                Session.CurrentFlow.SaveIbsData(response.ConfirmPriceRS, IbsDataTypeEnum.LatestOfferPriceResponse);

                selectBundleResponse = new BookingSelectBundleResponse(PriceMappers.MapToBasePriceResponse(response, Session));
            }

            if (flights.Any())
            {
                return selectBundleResponse;
            }

            return null;

        }

        public FlightSelectResponse BookingSelectFlight(FlightSelectRequest request)
        {
            var bundleSelections = Session.CurrentFlow.PNR.Flights.Select(s =>
            new
            {
                s.TmobId,
                s.SelectedBundleCode
            }).ToList();

            var deletingFlights = Session.CurrentFlow?.PNR?.Flights?.Where(f => request.FlightIds.Contains(f.TmobId)).ToList();
            if (deletingFlights != null && deletingFlights.Count > 0)
            {
                foreach (var flight in deletingFlights)
                {
                    var segmentTmobIds = flight.Segments.Select(x => x.TmobId).ToList();
                    Session.CurrentFlow?.PNR?.Flights?.RemoveAll(t => t.TmobId == flight.TmobId);
                    Session.CurrentFlow?.PNR?.SportEquipments?.RemoveAll(t => t.FlightTmobId == flight.TmobId);
                    Session.CurrentFlow?.PNR?.Seats?.RemoveAll(t => segmentTmobIds.Contains(t.SegmentTmobId));
                    Session.CurrentFlow?.PNR?.ExtraBaggages?.RemoveAll(t => t.TmobId == flight.TmobId);
                    Session.CurrentFlow?.PNR?.Coronas?.RemoveAll(t => t.TmobId == flight.TmobId);
                    Session.CurrentFlow?.PNR?.Meals?.RemoveAll(t => segmentTmobIds.Contains(t.SegmentTmobId));
                    Session.CurrentFlow?.PNR?.IfeServices?.RemoveAll(t => t.TmobId == flight.TmobId);
                }
            }
            else
            {
                Session.CurrentFlow.PNR.Flights.Clear();
                Session.CurrentFlow.PNR.ClearServices();
            }

            Session.CurrentFlow.PNR.Flights = request.FlightIds.Select(s => Session.CurrentFlow.BookingSearchFlightResponse.GetSessionPnrFlightDTO(s)).Any(k => k != null) ?
                                                    request.FlightIds.Select(s => Session.CurrentFlow.BookingSearchFlightResponse.GetSessionPnrFlightDTO(s)).ToList() :
                                                    new List<Ubimecs.Infrastructure.Models.DTO.Flight.SessionPnrFlightDTO>();
            
            IbsUtility.ValidateFlightSegments(Session.CurrentFlow.PNR.Flights,Session);
            
            var timeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
            var now = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

            foreach (var item in Session.CurrentFlow.PNR.Flights)
            {
                foreach (var segment in item.Segments)
                {
                    if (segment.CompleteDepartureTime <= now.AddHours(1))
                    {
                        throw new TmobException("Selected flight cannot be booked as it departs in less than 1 hour.");
                    }
                }                

                if (item != null && item.TmobId != null)
                {
                    item.SelectedBundleCode = bundleSelections.FirstOrDefault(f => f.TmobId == item.TmobId)?.SelectedBundleCode ?? "EABN";
                    //item.SelectedBundleCode = Session.CurrentFlow.PNR.Flights.Select(f => f.SelectedBundleCode).FirstOrDefault() ?? "EABN"; //TODO: EABN default eklendi. Sonrasında Config e eklenecek. (S.Y)
                }
            }

            Session.Save();

            //BookingSaveMetaData();

            FlightSelectResponse result = new FlightSelectResponse();
            ServicesResponse services = new ServicesResponse();
            List<Task> tasks = new List<Task>();
            try
            {
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();
                tasks.Add(Task.Run(() =>
                {
                    var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
                    var ibsRequst = Session.MapToConfirmPrice(channelConfig, request.FlightIds, null);

                    var response = IbsNativeRequester.CallPriceSoapService(ibsRequst, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
                    if (response.ConfirmPriceRS.ErrorType != null)
                    {
                        throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
                    }

                    result = new FlightSelectResponse(PriceMappers.MapToBasePriceResponse(response, Session), Session.CurrentFlow.Type);

                }));

                //tasks.Add(Task.Run(() =>
                //{
                //    services = GetServiceList(tmobId, false);
                //}));

                Task.WaitAll(tasks.ToArray());
                stopwatch.Stop();
                //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            }
            catch (System.Exception ex)
            {
                if (ex.InnerException != null)
                {
                    throw ex.InnerException;
                }
                else
                {
                    throw ex;
                }
            }

            //result.FlightServices = services;
            //Session.CurrentFlow.Services = services;
            Session.CurrentFlow.BookingSelectFlightResponse = result;
            return result;
        }

        public BasePriceResponse BookingSelectGolfBundle(SelectGolfBundleRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse BookingSelectIfe(SelectIfeRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse BookingSelectMeal(BookingSelectMealRequest request)
        {
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.Segments.Any(a => a.TmobId == request.SegmentTmobId));
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, flight.TmobId), null);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse BookingSelectSeat(BookingSelectSeatRequest request)
        {
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.Segments.Any(a => a.TmobId == request.SegmentTmobId));

            GuaranteeAncillary();
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice( channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, flight.TmobId), request.SegmentTmobId);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }


            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse BookingSetCorona(SetCoronaRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse BookingSetExtraBaggage(SetExtraBaggageRequest request)
        {
            var tmobId = request.Baggages.FirstOrDefault()?.TmobId;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice( channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, tmobId), null);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);

            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse BookingSetSportEquipment(BookingReSetSportEquipmentRequest request)
        {
            ConfirmPriceRQ ibsNativeRequest = new ConfirmPriceRQ();
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            foreach (var sportEquipment in request.SportEquipments)
            {
                ibsNativeRequest = Session.MapToConfirmPrice( channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, sportEquipment.TmobId), null);
            }

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);

            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }

            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse CancelFlight(CancelFlightRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.TmobId, Session, channelConfig);

            var modifiyBookingResponse = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (modifiyBookingResponse.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(modifiyBookingResponse.ModifyBookingRS.ErrorType.errorCode, modifiyBookingResponse.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            return PriceMappers.MapToBasePriceResponse(modifiyBookingResponse, Session);
        }

        public GetPnrInfoResponse CompleteCancelFlight(CancelFlightRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var saveModifyBookingRequest = ReservationMappers.MapToSaveModifyBooking(Session, channelConfig, request.TmobId);
            var response = IbsNativeRequester.CallReservationSoapService(saveModifyBookingRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public BasePriceResponse CancelPnr()
        {
            throw new NotImplementedException();
        }

        public CheckInGuestResponse CheckIn(CheckInGuestRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            if (IFlyResControlled(Session, request) is FlightSegmentDTO segment && !string.IsNullOrEmpty(segment.DepartureCode))
            {
                var getApisRequest = request.MapToGetApisRequest(segment, Session, channelConfig);
                var getApisResponse = IbsNativeRequester.CallCheckInSoapService(getApisRequest, GeneralConstants.CHECKIN_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.getApisAsync(req), Session?.SessionId);

                var segments = Session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(t => t.TmobId == request.TmobId)?.Segments;
                CheckinPortServiceReference.checkinGuestResponse responseFlyResControlled = new();
                foreach (var item in segments)
                {
                    var checkInRequest = request.MapToCheckInGuestRequest(Session, channelConfig, item.TmobId);
                    responseFlyResControlled = IbsNativeRequester.CallCheckInSoapService(checkInRequest, GeneralConstants.CHECKIN_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.checkinGuestAsync(req), Session?.SessionId);
                    if (responseFlyResControlled.CHK_CheckinGuestRS.ErrorType != null)
                    {
                        throw new IbsNativeException(responseFlyResControlled.CHK_CheckinGuestRS.ErrorType.errorCode, responseFlyResControlled.CHK_CheckinGuestRS.ErrorType.errorValue, Session.Language);
                    }
                }
                return new CheckInGuestResponse
                {
                    GateNumber = responseFlyResControlled.MapCheckInGuestResponse()
                };
            }
            else
            {
                CheckinPortServiceReference.checkinGuestResponse responseFlyResControlled = new();

                var checkInRequest = request.MapToCheckInGuestRequest(Session, channelConfig);
                responseFlyResControlled = IbsNativeRequester.CallCheckInSoapService(checkInRequest, GeneralConstants.CHECKIN_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.checkinGuestAsync(req), Session?.SessionId);
                if (responseFlyResControlled.CHK_CheckinGuestRS.ErrorType != null)
                {
                    throw new IbsNativeException(responseFlyResControlled.CHK_CheckinGuestRS.ErrorType.errorCode, responseFlyResControlled.CHK_CheckinGuestRS.ErrorType.errorValue, Session.Language);
                }
                
                return new CheckInGuestResponse
                {
                    GateNumber = responseFlyResControlled.MapCheckInGuestResponse()
                };
            }
        }

        public FlightSegmentDTO IFlyResControlled(SessionCache session, CheckInGuestRequest request)
        {
            var iFlyResControlledList = CachedData.CheckInIFlyResControl;
            SessionPnrFlightDTO checkInFlight = session.CurrentFlow.PNR.Flights.FirstOrDefault(k => k.TmobId == request.TmobId);
            if (checkInFlight != null &&
                iFlyResControlledList.Any(k => checkInFlight.Segments.Select(t => t.DepartureCode).ToList().Contains(k)) &&
                checkInFlight.Segments.FirstOrDefault(t => iFlyResControlledList.Contains(t.DepartureCode)) is FlightSegmentDTO iFlyResControlledFlight)
            {
                return iFlyResControlledFlight;
            }
            return new FlightSegmentDTO();
        }

        public CheckInIFlyResControlResponse CheckInIFlyResControl(CheckInIFlyResControlRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            var ibsNativeRequest = CheckInMappers.MapToRetrieveParametersRequest(channelConfig);
            var ibsNativeResponse = IbsNativeRequester.CallCheckInUtilitySoapService(ibsNativeRequest, GeneralConstants.CHECKIN_IFLY_RES_CONTROL_SERVICE_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveParametersAsync(req), Session?.SessionId);
            return ibsNativeResponse.MapCheckInIFlyResControlResponse();
        }

        public CheckLoyaltyNumberResponse CheckLoyaltyNumber(CheckLoyaltyNumberRequest request)
        {
            throw new NotImplementedException();
        }

        public ClaimFlightResponse ClaimFlight(ClaimFlightRequest request)
        {
            throw new NotImplementedException();
        }

        public CompleteCancelPnrResponse CompleteCancelPnr()
        {
            throw new NotImplementedException();
        }

        public GetPnrInfoResponse ChangeRebookingTravelDocument(ChangeRebookingTravelDocumentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            var saveModifyBookingRq = ReservationMappers.MapToSaveModifyBooking(Session, channelConfig, request.TmobId, null, false, null, request);
            var response = IbsNativeRequester.CallReservationSoapService(saveModifyBookingRq, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            var map = GetPnrInfoMappers.MapPnrInfoResponse(response, Session);

            GetPnrInfo(new GetPnrInfoRequest
            {
                Surname = map.Surname,
                OrderId = map.PNRNumber
            });
            
            return map;
        }

        public GetPnrInfoResponse ContactInfoChange(RebookingContactInfoChangeRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var saveModifyBookingRQ = ReservationMappers.MapToSaveModifyBooking(Session, channelConfig, null, contactInfoChangeRequest: request);
            var saveModifyBookingResponse = IbsNativeRequester.CallReservationSoapService(saveModifyBookingRQ, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (saveModifyBookingResponse.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(saveModifyBookingResponse.SaveModifyBookingRS.ErrorType.errorCode, saveModifyBookingResponse.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            return GetPnrInfoMappers.MapPnrInfoResponse(saveModifyBookingResponse, Session);
        }

        public GetPnrInfoResponse CraeteOrder(CreateOrderRequest request, ICrmService crmService)
        {
            if (request.PaymentInformation != null && !string.IsNullOrEmpty(request.PaymentInformation.VoucherNumber))
            {
                Session.CurrentFlow.PNR.GiftVoucherAmount = GetGiftVoucherAmount(new GiftVoucherRequest
                {
                    GiftNumber = request.PaymentInformation.VoucherNumber
                })?.Amount ?? 0;
            }

            ReservationsPortServiceReference.CreateBookingRQ ibsNativeRequest;
            if (Session.CurrentFlow.BookingType != BookingTypeEnum.GroupBooking)
            {
                AdjustFlightInventory(isForMarking: false);
            }
            
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            if (request.Is3dFinishingRequest)
            {
                OrderViewRS latestOrderView = Session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.cavv = request.SecurePaymentInfomation.cavv;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.xid = request.SecurePaymentInfomation.xid;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.eci = request.SecurePaymentInfomation.eci;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.md = request.SecurePaymentInfomation.md;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.SPMTxnReference = latestOrderView?.GetSecurePaymentInfo()?.SPMTxnReference;
                ibsNativeRequest = ReservationMappers.MapToCreateBookingRQ(Session.CurrentFlow.CreateOrderRequest, Session, channelConfig);
            }
            else
            {
                ibsNativeRequest = request.MapToCreateBookingRQ(Session, channelConfig);
                Session.CurrentFlow.CreateOrderRequest = request;
            }
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveCreateBookingAsync(req), Session?.SessionId);
            stopwatch.Stop();
            if (response.CreateBookingRS.ErrorType != null)
            {
                ReleaseAncillary(Session.CurrentFlow.PNR.Seats);
                throw new IbsNativeException(response.CreateBookingRS.ErrorType.errorCode, response.CreateBookingRS.ErrorType.errorValue, Session.Language);
            }

            if (Session.CurrentFlow.BookingType != BookingTypeEnum.GroupBooking)
            {
                Session.CurrentFlow.PNR.Passengers.ForEach(passenger =>
                {
                    var requestPassenger = request.PassengerList.FirstOrDefault(x => x.Id == passenger.Id);
                    if (requestPassenger != null)
                    {
                        passenger.Name = requestPassenger.GivenName.ToUpper();
                        passenger.Surname = requestPassenger.Surname.ToUpper();
                    }
                });
            }
            var getPnrInfoResponse = GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
            
            var map = getPnrInfoResponse.Map(Session);
            crmService.CreateBooking(map);
            
            return getPnrInfoResponse;
        }

        public RegisterResponse EditUser(EditMemberRequest request)
        {
            throw new NotImplementedException();
        }

        public LoginResponse FacebookLogin(FacebookLoginRequest request)
        {
            throw new NotImplementedException();
        }

        public ForgotPasswordResponse ForgotPassword(ForgotPasswordRequest request)
        {
            throw new NotImplementedException();
        }

        public AirportListResponse GetAirportList()
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2CDirect);
            var ibsNativeRequest = AvailabilityMappers.GetAllOandDPairsRequestMap(channelConfig);
            var response = IbsNativeRequester.CallAvailabilitySoapService(ibsNativeRequest, GeneralConstants.AVAILABILITY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.getAllOandDPairsAsync(req), Session?.SessionId);

            if (response.RetrieveOandDPairsRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveOandDPairsRS.ErrorType.errorCode, response.RetrieveOandDPairsRS.ErrorType.errorValue, Session.Language);
            }

            return response.Map();
        }

        public GetBaggageAllowanceResponse GetBaggageAllowance(GetBaggageAllowanceRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var IbsRequest = request.GetBaggageAllowanceRequestMap(Session, channelConfig);

            var response = IbsNativeRequester.CallAncillarySoapService(IbsRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listBaggageServicesAsync(req), Session?.SessionId);
            if (response.ListBaggageServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListBaggageServicesRS.ErrorType.errorCode, response.ListBaggageServicesRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.ListBaggageServicesRS, IbsDataTypeEnum.BaggageCharge.ToString() + "_" + request.TmobId);

            var result = response.GetBaggageAllowanceResponseMap(Session, request.TmobId);

            return result;
        }

        public List<BestOfferDTO> GetBestOffers(GetFlightsRequest request)
        {
            return new List<BestOfferDTO>();
        }

        public GetBoomiFlightStatusResponse GetBoomiFlightStatus(GetBoomiFlightStatusRequest request)
        {
            throw new NotImplementedException();
        }

        public Dictionary<DateTime, int> GetCalendarAvailability(GetFlightsRequest request)
        {
            throw new NotImplementedException();
        }

        public GiftVoucherResponse GetGiftVoucherAmount(GiftVoucherRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig, Session);

            var response = IbsNativeRequester.CallGiftVoucherSoapService(ibsNativeRequest, GeneralConstants.GIFT_VOUCHER_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveGiftCertificateDetailsAsync(req), Session?.SessionId);
            if (response.RetrieveGiftCertificateDetailsRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveGiftCertificateDetailsRS.ErrorType.errorCode, response.RetrieveGiftCertificateDetailsRS.ErrorType.errorValue, Session.Language);
            }
            var voucherAmount = Convert.ToDecimal(response.RetrieveGiftCertificateDetailsRS?.GiftCertificateBalanceAmount ?? 0);

            Session.CurrentFlow.PNR.GiftVoucherAmount = voucherAmount;
            return new GiftVoucherResponse
            {
                Amount = voucherAmount,
                Currency = response.RetrieveGiftCertificateDetailsRS?.GiftCertificateCurrencyCode
            };
        }
        
        public RetrieveGiftCertificateDetailsResponse GetGiftVoucherDetail(GiftVoucherRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig, Session);

            var response = IbsNativeRequester.CallGiftVoucherSoapService(ibsNativeRequest, GeneralConstants.GIFT_VOUCHER_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveGiftCertificateDetailsAsync(req), Session?.SessionId);
            if (response.RetrieveGiftCertificateDetailsRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveGiftCertificateDetailsRS.ErrorType.errorCode, response.RetrieveGiftCertificateDetailsRS.ErrorType.errorValue, Session.Language);
            }

            return response.RetrieveGiftCertificateDetailsRS.Map();
        }
        
        public GetMealsResponse GetMeals(GetMealsRequest request)
        {
            var response = GetListSaleable(request.SegmentTmobId, false);
            var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.MEALS);
            var mealsResponse = ancillaryServices.MapMeals(request.SegmentTmobId, Session);

            Session.CurrentFlow.ServiceMeals = new GetServiceMealList();
            Session.CurrentFlow.ServiceMeals.Meals = mealsResponse.Meals.Select(x => new ServiceMealList()
            {
                Code = x.Code,
                PassengerIds = x.PassengerIds,
                SegmentIds = x.SegmentIds,
                BundleCode = x.BundleCode,
                BundleCategoryCode = x.BundleCategoryCode,
                IsBundleIncluded = x.IsBundleIncluded
            }).ToList();
            return mealsResponse;
        }

        public GetExtraServicesResponse GetExtraServices(GetExtraServicesRequest request)
        {
            var customOrder = new List<string> { "ANIMALS", "WHEELCHAIR", "ASSISTANCE", "CAR_PARKING", "GROUND_TRANSFER" };
            var sessionFlightIds = IbsUtility.GetFlightIdsByFlightType(Session);
            var flightIds = new List<string>();

            var allExtraServices = new Dictionary<string, List<BaseServiceDTO>>();

            foreach (var sessionFlightId in sessionFlightIds)
            {
                var segmentTmodId = Session.CurrentFlow.PNR.Flights
                    .FirstOrDefault(x => x.TmobId == sessionFlightId)?.Segments.FirstOrDefault()?.TmobId;

                if (string.IsNullOrEmpty(segmentTmodId))
                {
                    continue; // Skip if no segment is found for the flight
                }

                var response = GetListSaleable(segmentTmodId, false);
                var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.OTHERS);
                var mappedExtraServices = ancillaryServices.MapExtraServices();

                foreach (var serviceGroup in mappedExtraServices.ExtraServices)
                {
                    if (allExtraServices.ContainsKey(serviceGroup.Key))
                    {
                        foreach (var baseServiceDto in serviceGroup.Value)
                        {
                            var serviceGroupValue = allExtraServices[serviceGroup.Key].FirstOrDefault(x => x.Id == baseServiceDto.Id);
                            if (serviceGroupValue == null)
                            {
                                if (!baseServiceDto.FlightIds.Contains(sessionFlightId))
                                {
                                    baseServiceDto.FlightIds.Add(sessionFlightId);
                                }
                                allExtraServices[serviceGroup.Key].Add(baseServiceDto);
                            }
                            else
                            {
                                if (!serviceGroupValue.FlightIds.Contains(sessionFlightId))
                                {
                                    serviceGroupValue.FlightIds.Add(sessionFlightId);
                                }
                            }

                        }
                    }
                    else
                    {
                        foreach (var baseServiceDto in serviceGroup.Value)
                        {
                            if (!baseServiceDto.FlightIds.Contains(sessionFlightId))
                            {
                                baseServiceDto.FlightIds.Add(sessionFlightId);
                            }
                        }
                        allExtraServices[serviceGroup.Key] = serviceGroup.Value;
                    }
                }
            }

            var sortedExtraServices = allExtraServices
                .OrderBy(kvp => customOrder.Contains(kvp.Key) ? customOrder.IndexOf(kvp.Key) : int.MaxValue)
                .ThenBy(kvp => kvp.Key)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            return new GetExtraServicesResponse
            {
                ExtraServices = sortedExtraServices
            };
        }

        public BasePriceResponse SetExtraServices(SetExtraServicesRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice(channelConfig, IbsUtility.GetFlightIdsByFlightType(Session, request.TmobId), null);

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);

            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }

            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public GetFlexesResponse GetFlexes(GetFlexesRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = FlexMappers.Map(Session, channelConfig, request.SegmentTmobId);
            var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);
            if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
            }
            return response.MapToFlexes(Session, request.SegmentTmobId);
        }

        public BasePriceResponse BookingSetFlex(SetFlexRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = Session.MapToConfirmPrice( channelConfig, IbsUtility.GetFlightIdsByFlightType(Session));

            var response = IbsNativeRequester.CallPriceSoapService(ibsNativeRequest, GeneralConstants.PRICE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.confirmPriceAsync(req), Session?.SessionId);
            if (response.ConfirmPriceRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ConfirmPriceRS.ErrorType.errorCode, response.ConfirmPriceRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        private void ProcessFlightSegments(Infrastructure.Models.DTO.Flight.SessionPnrFlightDTO flight)
        {
            ProcessFlightSegmentsInternal(flight, flight.Id);
        }

        private void ProcessFlightSegments(Infrastructure.Models.DTO.Flight.SearchFlightFlightDTO flightOption, string flightId)
        {
            ProcessFlightSegmentsInternal(flightOption, flightId);
        }

        private void ProcessFlightSegmentsInternal<T>(T flightObject, string flightId) where T : class
        {
            var segmentsProperty = flightObject.GetType().GetProperty("Segments");
            if (segmentsProperty == null) return;

            var segments = segmentsProperty.GetValue(flightObject) as List<FlightSegmentDTO>;
            if (segments == null) return;

            if (segments.Any(x => x.Stops > 0))
            {
                var segmentsToAdd = new List<FlightSegmentDTO>();
                foreach (var segment in segments)
                {
                    var flightNumber = segment.FlightNumber;
                    var flightDate = segment.DepartureDate;

                    var newSegments = GetFlightSegments(new FlightSegmentRequest
                    {
                        FlightNumber = flightNumber,
                        FlightDate = flightDate
                    });

                    if (newSegments != null && newSegments.Any())
                    {
                        int segmentCounter = 1;
                        foreach (var newSegment in newSegments)
                        {
                            newSegment.AircraftInfo = new();
                            var parsedFlightId = IbsUtility.ParseFullId(flightId);
                            var newSegmetId = IbsUtility.GetFullSegmentId(parsedFlightId + segmentCounter);
                            newSegment.Id = newSegmetId;
                            newSegment.FareBasisCode = segment.FareBasisCode;
                            newSegment.SeatAvailablity = segment.SeatAvailablity;
                            newSegment.AircraftInfo.Type = segment.AircraftInfo.Type;
                            newSegment.AircraftInfo.Version = segment.AircraftInfo.Version;
                            newSegment.CarrierName = segment.CarrierName;
                            newSegment.CabinClass = segment.CabinClass;
                            newSegment.PriceClassName = segment.PriceClassName;

                            segmentCounter++;
                            segmentsToAdd.Add(newSegment);
                        }
                    }
                }

                segments.Clear();
                segments.AddRange(segmentsToAdd);
            }
        }

        public GetPnrInfoResponse GetPnrInfo(GetPnrInfoRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.MapPnrInfoRequest(channelConfig, Session);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveBookingAsync(req), Session?.SessionId);
            if (response.RetrieveBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveBookingRS.ErrorType.errorCode, response.RetrieveBookingRS.ErrorType.errorValue, Session.Language);
            }

            var result = GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
            request.OrderId = request.OrderId ?? result.PNRNumber;
            var pnrExtractRequest = request.MapToPnrExtractRequest(channelConfig, Session);
            var pnrExtractResponse = IbsNativeRequester.CallReservationSoapService(
                pnrExtractRequest,
                GeneralConstants.RESERVATION_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.extractReservationAsync(req),
                Session?.SessionId);

            // Override payment info with transactional PNRExtract payments
            if (pnrExtractResponse?.PNRExtractRS?.TotalPaymentDetails?.PaymentDetails != null)
            {
                result.PaymentDetails = GetPnrInfoMappers.GetPaymentDetailsFromExtract(pnrExtractResponse.PNRExtractRS);
            }

            Session.CurrentFlow.PnrInfoResponse = result;

            Session.CurrentFlow.PNR.Flights = result.FlightDestionationList.Select(s => new Infrastructure.Models.DTO.Flight.SessionPnrFlightDTO
            {
                BasePrice = s.BasePrice,
                BasePriceDescription = s.BasePriceDescription,
                Id = s.Id,
                JourneyTime = s.JourneyTime,
                Segments = s.Segments,
                Title = s.Title,
                TotalPriceDescription = s.TotalPriceDescription,
                SelectedBundle = s.SelectedBundleCategoryId,
                SelectedBundleCode = s.SelectedBundleCode,
                Price = s.FlightPrice,
                Currency = result.Currency,
                CabinClass = s.CabinClass,
                State = s.SegmentStatus == "CANCELLED" ? OfferRebookingStateEnum.Removed : (Session.CurrentFlow.RebookingType == RebookingTypeEnum.OptionalPNR ? OfferRebookingStateEnum.OptionalPNR : OfferRebookingStateEnum.NotChanged) //GetPnrInfoMappers.MapState(s.SegmentStatus) 
            }).ToList();

            foreach (var flight in Session.CurrentFlow.PNR.Flights)
            {
                ProcessFlightSegments(flight);
            }

            Session.CurrentFlow.PNR.Passengers = result.PassengerList.Select(t => new Infrastructure.Models.PNR.Passenger
            {
                GlobalId = t.Id,
                LocalId = t.Id,
                Name = t.GivenName,
                Surname = t.Surname,
                NameTitle = t.NameTitle,
                Gender = t.Gender,
                BirthDate = t.Birthdate,
                FamilyId = t.FamilyId,
                ParentGuestID = t.ParentGuestID,
                PassengerType = ((PassengerTypeEnum)Enum.Parse(typeof(PassengerTypeEnum), t.PassengerType, true)),
                IFEAccessCode = t.IFEAccessCode ?? string.Empty
            }).ToList();
            Session.CurrentFlow.PnrInfoResponse.ContactList = result.ContactList;
            Session.CurrentFlow.PNR.Currency = result.Currency;

            //flightIds bilgisi session.CurrentFlow.Flights ile dolduğu için Flights ı tanımladıktan sonra tekrar services 'lara istek attık
            var allServices = GeneralMappers.GetAllServicesNative(response, Session, response.RetrieveBookingRS.Itinerary);
            Session.CurrentFlow.PnrInfoResponse.Services = allServices;
            //if (Session.CurrentFlow.RebookingType == RebookingTypeEnum.OptionalPNR && result.StatusCode != "NOK")
            //{
            //    result.PriceInformation = GetReshopResponseFromSession();
            //}


            //OrderRetrieveSaveMetaData();
            return result;
        }

        public GetPaymentBalanceResponse GetPnrPaymentBalance()
        {
            throw new NotImplementedException();
        }

        public ServicesResponse GetServices()
        {
            throw new NotImplementedException();
        }

        public GetPnrInfoResponse GetTourOperatorPnrInfo(GetTourOperatorPnrInfoRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.MapTourOperatorPnrRequest(channelConfig, Session);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveBookingAsync(req), Session?.SessionId);
            if (response.RetrieveBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveBookingRS.ErrorType.errorCode, response.RetrieveBookingRS.ErrorType.errorValue, Session.Language);
            }

            var result = GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
            result.TourOperatorNumber = request.TourOpearatorNumber;
            Session.CurrentFlow.PnrInfoResponse = result;
            Session.CurrentFlow.PNR.Flights = result.FlightDestionationList.Select(s => new Infrastructure.Models.DTO.Flight.SessionPnrFlightDTO
            {
                BasePrice = s.BasePrice,
                BasePriceDescription = s.BasePriceDescription,
                Id = s.Id,
                JourneyTime = s.JourneyTime,
                Segments = s.Segments,
                Title = s.Title,
                CabinClass = s.CabinClass,
                TotalPriceDescription = s.TotalPriceDescription,
                SelectedBundle = s.SelectedBundleCategoryId,
                SelectedBundleCode = s.SelectedBundleCode,
                State = Session.CurrentFlow.RebookingType == RebookingTypeEnum.OptionalPNR ? OfferRebookingStateEnum.OptionalPNR : OfferRebookingStateEnum.NotChanged
            }).ToList();

            foreach (var flight in Session.CurrentFlow.PNR.Flights)
            {
                ProcessFlightSegments(flight);
            }

            Session.CurrentFlow.PNR.Passengers = result.PassengerList.Select(t => new Infrastructure.Models.PNR.Passenger
            {
                GlobalId = t.Id,
                LocalId = t.Id,
                Name = t.GivenName,
                Surname = t.Surname,
                NameTitle = t.NameTitle,
                FamilyId = t.FamilyId,
                Gender = t.Gender,
                BirthDate = t.Birthdate,
                ParentGuestID = t.ParentGuestID,
                PassengerType = ((PassengerTypeEnum)Enum.Parse(typeof(PassengerTypeEnum), t.PassengerType, true)),
                IFEAccessCode = t.IFEAccessCode ?? string.Empty
            }).ToList();
            Session.CurrentFlow.PnrInfoResponse.ContactList = result.ContactList;
            Session.CurrentFlow.PNR.Currency = result.Currency;

            var allServices = GeneralMappers.GetAllServicesNative(response, Session, response.RetrieveBookingRS.Itinerary);
            Session.CurrentFlow.PnrInfoResponse.Services = allServices;
            return result;
        }

        public GetTourOperatorsResponse GetTourOperators(GetTourOperatorsRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig);
            var response = IbsNativeRequester.CallAgencySoapService(ibsNativeRequest, GeneralConstants.AGENCY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveAgenciesAsync(req), Session?.SessionId);
            if (response.RetrieveAgenciesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveAgenciesRS.ErrorType.errorCode, response.RetrieveAgenciesRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map();
        }

        public UserInfoResponse GetUserInfo(UserInfoRequest request)
        {
            throw new NotImplementedException();
        }

        public LoginResponse Login(LoginRequest request)
        {
            throw new NotImplementedException();
        }

        public GetMealsResponse RebookingGetMeals(GetMealsRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = AncillaryMappers.MapRebookingAncillaryServicesRQ(request.SegmentTmobId, Session, channelConfig);
            var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);
            if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.ListSaleableAncillaryServicesRS, IbsDataTypeEnum.ServiceListNotifable);
            var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.MEALS);
            var mealsResponse = ancillaryServices.MapMeals(request.SegmentTmobId, Session);

            Session.CurrentFlow.ServiceMeals = new GetServiceMealList();
            Session.CurrentFlow.ServiceMeals.Meals = mealsResponse.Meals.Select(x => new ServiceMealList()
            {
                Code = x.Code,
                PassengerIds = x.PassengerIds,
                SegmentIds = x.SegmentIds,
                BundleCode = x.BundleCode,
                BundleCategoryCode = x.BundleCategoryCode,
                IsBundleIncluded = x.IsBundleIncluded
            }).ToList();

            return mealsResponse;
        }

        public SportEquipmentResponse RebookingGetSportEquipments(SportEquipmentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = AncillaryMappers.MapRebookingAncillaryServicesRQ(request.SegmentTmobId, Session, channelConfig);
            var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);
            if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.ListSaleableAncillaryServicesRS, IbsDataTypeEnum.ServiceListNotifable);
            var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.SPORTS_EQUIPMENTS);
            return ancillaryServices.MapSportEquipments();
        }

        public BasePriceResponse RebookingSetExtraServices(SetExtraServicesRequest request)
        {
            bool isBundleChangingType = IbsUtility.IsBundleUpgraded(Session, request.TmobId);
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.TmobId, Session, channelConfig, isBundleChangingType);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);

            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public GetFlexesResponse RebookingGetFlexes(GetFlexesRequest request)
        {
            // Check if all flights have EABN or ECBN bundle and flex services
            var allFlightsHaveEasyOrEcoBird = Session.CurrentFlow.PNR.Flights.All(flight =>
                flight.SelectedBundleCode == FlightPackage.EABN.ToString() || flight.SelectedBundleCode == FlightPackage.ECBN.ToString());

            var allFlightsHaveFlex = Session.CurrentFlow.PNR.Flights.All(flight =>
                flight.Segments.All(segment =>
                    Session.CurrentFlow.PnrInfoResponse.Services.FlexServices.Any(flex =>
                        flex.SegmentIds.Contains(segment.Id))));

            if (allFlightsHaveEasyOrEcoBird && allFlightsHaveFlex)
            {
                // If all flights have EasyBird/EcoBird bundle and flex, return empty flex list
                return new GetFlexesResponse { Flexes = new List<FlexResponseDTO>() };
            }

            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = AncillaryMappers.MapRebookingAncillaryServicesRQ(request.SegmentTmobId, Session, channelConfig);
            var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);
            if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.ListSaleableAncillaryServicesRS, IbsDataTypeEnum.ServiceListNotifable);
            return response.MapToFlexes(Session, request.SegmentTmobId);
        }

        public BasePriceResponse RebookingSetFlex(SetFlexRequest request)
        {
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.Segments.Any(a => a.TmobId == request.SegmentTmobId));
            bool isBundleChangingType = IbsUtility.IsBundleUpgraded(Session, flight.TmobId);
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(flight.TmobId, Session, channelConfig, isBundleChangingType);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public PrintItinaryResponse PrintItinary(PrintItinaryRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig, Session);
            var response = IbsNativeRequester.CallCheckInSoapService(ibsNativeRequest,
                GeneralConstants.CHECKIN_PORT_URL, 
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.printItineraryAsync(req), Session?.SessionId);
            if (response.CHK_PrintItineraryRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CHK_PrintItineraryRS.ErrorType.errorCode, response.CHK_PrintItineraryRS.ErrorType.errorValue, Session.Language);
            }
            return response.CHK_PrintItineraryRS.Map();
        }

        public GetPnrInfoResponse OrderChange(ChangeOrderRequest request, ICrmService crmService)
        {
            AdjustFlightInventory(isForMarking: false);
            Session.CurrentFlow.ChangeOrderRequest = request;
            bool isBundleChangingType = (Session.CurrentFlow.RebookingType == RebookingTypeEnum.AddFlight || Session.CurrentFlow.IsBundleUpgrade) ? true : false;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var paymentAwareRequest = request.PaymentInformation != null ? request : null;
            var ibsNativeRequest = ReservationMappers.MapToSaveModifyBooking(Session, channelConfig,null, paymentAwareRequest, isBundleChangingType);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            var getPnrInfoResponse = GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
            var map = getPnrInfoResponse.Map(Session);

            crmService.ModifyBooking(map);
            
            return getPnrInfoResponse;
        }

        public GetPnrInfoResponse PassengerNameChange(RebookingNameChangeRequest request)
        {
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault();
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(flight.TmobId, Session, channelConfig, false, null, null, nameChangeRequest: request);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            PriceMappers.MapToBasePriceResponse(response, Session);
            var result = GetPnrInfoMappers.MapPnrInfoResponse(response, Session);

            foreach (var resultPassenger in result.PassengerList)
            {
                var sessionPassenger = Session.CurrentFlow.PNR.Passengers.FirstOrDefault(x=>x.Id == resultPassenger.Id);
                if (sessionPassenger != null)
                {
                    sessionPassenger.Name = resultPassenger.GivenName;
                    sessionPassenger.Surname = resultPassenger.Surname;
                    sessionPassenger.NameTitle = resultPassenger.NameTitle;
                    sessionPassenger.Gender = resultPassenger.Gender;
                    sessionPassenger.BirthDate = resultPassenger.Birthdate;
                }
            }

            Session.CurrentFlow.PNR.TravelDocumentChangedList = result.TravelDocuments.Select(t =>
                new RebookingTravelDocumentChangeRequest
                {
                    PassengerID = t.PassengerID,
                    GivenName = t.GivenName,
                    Surname = t.Surname,
                    DocumentNumber = t.DocumentNumber,
                    ResidenceCountryCode = t.ResidenceCountryCode,
                    BirthDate = t.BirthDate,
                    TravelDocumentId = t.TravelDocumentId
                }).ToList();
            
            return result;
        }

        public PasswordChangeResponse PasswordChange(PasswordChangeRequest request)
        {
            throw new NotImplementedException();
        }

        public PrintBoardingPassResponse PrintBoardingPass(PrintBoardingPassRequest request)
        {
            var restrictedAirPortsControl = RestrictedAirportsControl(Session, request);
            if (restrictedAirPortsControl)
            {
                var errorMessage = CachedData.GetCMS(Session.Language, "boarding_pass_cant_used_desc");
                throw new TmobException(errorMessage);
            }

            GetPnrInfoResponse pnrInfoResponse;
            bool tourOperatorOperation = !string.IsNullOrEmpty(Session.CurrentFlow?.PnrInfoResponse.TourOperatorNumber);
            try
            {
                if (!tourOperatorOperation)
                {
                    //var travelDoc = Session.CurrentFlow?.PnrInfoResponse?.TravelDocuments.FirstOrDefault();
                    var currentPassenger = Session.CurrentFlow.PNR.Passengers.FirstOrDefault();
                    pnrInfoResponse = GetPnrInfo(new GetPnrInfoRequest
                    {
                        OrderId = String.IsNullOrEmpty(request.PnrNumber) ? Session.CurrentFlow?.PNR?.Number : request.PnrNumber,
                        Surname = String.IsNullOrEmpty(request.Surname) ? currentPassenger.Surname : request.Surname
                    });
                    pnrInfoResponse = Session.CurrentFlow.PnrInfoResponse;
                }
                else
                {
                    var currentFlight = Session.CurrentFlow.PNR?.Flights
                                                               ?.FirstOrDefault(t => t.TmobId.Equals(request.FlightSegmentTmobId)
                                                                                 || t.Segments.Any(k => k.TmobId.Equals(request.FlightSegmentTmobId)));
                    var currentPassenger = Session.CurrentFlow.PnrInfoResponse.PassengerList.FirstOrDefault(t => t.Id == request.PassengerId);
                    string tourOperatorNumber = !string.IsNullOrEmpty(Session.CurrentFlow.PnrInfoResponse.TourOperatorNumber)
                                                ? Session.CurrentFlow.PnrInfoResponse.TourOperatorNumber
                                                : string.Empty;


                    pnrInfoResponse = GetTourOperatorPnrInfo(new GetTourOperatorPnrInfoRequest
                    {
                        TourOperatorCode = null,// request.TourOperatorCode,//response.GuestList.FirstOrDefault().RecordLocator,
                        TourOpearatorNumber = tourOperatorNumber,
                        Surname = currentPassenger != null ? currentPassenger.Surname : string.Empty,
                        Name = currentPassenger != null ? currentPassenger.GivenName : string.Empty,
                        Flights = new List<BookingSearchFlightRequest.SearchFlightInfo>() {
                        new BookingSearchFlightRequest.SearchFlightInfo()
                        {
                            ArrivalAirport=currentFlight != null ? currentFlight.ArrivalCode : string.Empty,
                            DepartureAirport=currentFlight != null ? currentFlight.DepartureCode : string.Empty,
                            DepartureDate= default
                        }
                    }
                    });

                    pnrInfoResponse = Session.CurrentFlow.PnrInfoResponse;
                }
            }
            catch (Exception ex)
            {
            }
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            var ibsNativeRequest = CheckInMappers.Map(request, Session, channelConfig);

            var response = IbsNativeRequester.CallCheckInSoapService(ibsNativeRequest, GeneralConstants.CHECKIN_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.printBoardingPassAsync(req), Session?.SessionId);
            if (response.CHK_PrintBoardingPassRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CHK_PrintBoardingPassRS.ErrorType.errorCode, response.CHK_PrintBoardingPassRS.ErrorType.errorValue, Session.Language);
            }

            var mappedResponse = response.Map(Session);

            if (Session.CurrentFlow.BoardingPasses == null || Session.CurrentFlow.BoardingPasses.Count == 0)
            {
                Session.CurrentFlow.BoardingPasses = new List<PrintBoardingPassResponse>();
            }
            
            mappedResponse.SegmentTmobId = request.FlightSegmentTmobId;
            mappedResponse.BoardingPasses.ForEach(x =>
            {
                x.PaxKey = request.PassengerId;
            });
            var boardingPass = Session.CurrentFlow.BoardingPasses.FirstOrDefault(x => x.SegmentTmobId == request.FlightSegmentTmobId);
            if (boardingPass != null)
            {
                boardingPass.BoardingPasses.AddRange(mappedResponse.BoardingPasses);
            }
            else
            {
                Session.CurrentFlow.BoardingPasses.Add(mappedResponse);
            }

            return mappedResponse;
        }

        public ServicesResponse RebookingAddExtrasServices()
        {
            return GetServiceList(String.Empty, default);
        }

        public ChangeCurrencyResponse RebookingChangeCurrency()
        {
            var result = new ChangeCurrencyResponse();

            bool isBundleChangingType = Session.CurrentFlow.RebookingType == RebookingTypeEnum.AddFlight ? true : false;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(null, Session, channelConfig, isBundleChangingType);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);

            return result;
        }

        public BasePriceResponse RebookingGetInstallmentFee(GetInstallmentFeeRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse RebookingReSelectMeal(BookingReSelectMealRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.SegmentMeals.FirstOrDefault().SegmentTmobId, Session, channelConfig);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse RebookingReSelectSeat(RebookingReSelectSeatRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse RebookingReSetSportEquipment(RebookingReSetSportEquipmentRequest request)
        {

            var tmobId = request.SportEquipments.FirstOrDefault()?.TmobId;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(tmobId, Session, channelConfig);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BookingSearchFlightResponse RebookingSearchFlight(RebookingSearchFlightRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.MapRebookingSearchFLightRequest(Session, channelConfig);
            var response = IbsNativeRequester.CallAvailabilitySoapService(ibsNativeRequest, GeneralConstants.AVAILABILITY_PORT_URL,
                channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey,
                (client, req) => client.getEnhancedAirAvailabilityAsync(req), Session?.SessionId);

            if (response.EnhancedAirAvailabilityRS.ErrorType != null)
            {
                throw new IbsNativeException(response.EnhancedAirAvailabilityRS.ErrorType.errorCode, response.EnhancedAirAvailabilityRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.EnhancedAirAvailabilityRS, IbsDataTypeEnum.ShopAirResponse);

            var result = response.MapSearchFlightResponse(Session, request);//Change flightta eski uçuş seçilememesi için koşul yaz

            var now = DateTime.UtcNow;

            foreach (var flight in result.Flights)
            {
                flight.FlightOptions = flight.FlightOptions
                    .Where(option =>
                        option.Segments != null &&
                        option.Segments.All(segment =>
                            segment.DepartureDate.ToUniversalTime() > now.AddHours(1)
                        )
                    ).ToList();
                foreach (var flightOption in flight.FlightOptions)
                {
                    ProcessFlightSegments(flightOption, flightOption.Id);
                }
            }

            Session.CurrentFlow.RebookingSearchFlightResponse = result;
            Session.CurrentFlow.ChangeFlightOldTmobId = request.OldFlightTmobId;

            result.Flights = result.Flights
                .Where(flight => flight.FlightOptions != null && flight.FlightOptions.Any())
                .ToList();

            return result;
        }

        public BasePriceResponse RebookingSelectBundle(RebookingSelectBundleRequest request)
        {
            var flight = new SessionPnrFlightDTO();
            if (Session.CurrentFlow.PNR.IsTourOperatorOperation)
            {
                Session.CurrentFlow.PNR.Flights.RemoveAll(t => t.State == OfferRebookingStateEnum.Removed);

                flight = Session.CurrentFlow.PNR?.Flights?.FirstOrDefault(f => f.TmobId == request.TmobId);
            }
            else
            {
                flight = Session.CurrentFlow.PNR?.Flights?.FirstOrDefault(f => f.TmobId == request.TmobId);
            }

            flight.SelectedBundleCode = request.BundleCode;

            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.TmobId, Session, channelConfig, true);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }

            if (Session.CurrentFlow.RebookingType != RebookingTypeEnum.AddFlight && Session.CurrentFlow.RebookingType != RebookingTypeEnum.ChangeFlightNative)
            {
                flight.State = OfferRebookingStateEnum.Upgraded;
            }

            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public FlightSelectResponse RebookingSelectFlight(RebookingSelectFlightRequest request)
        {
            //if (Session.CurrentFlow.PNR.IsTourOperatorOperation)
            //{
            //    Session.CurrentFlow.RebookingType = RebookingTypeEnum.TourOperator;
            //    var selectedFlight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(t => t.TmobId == request.TmobId);
            //    if (selectedFlight != null)
            //    {
            //        selectedFlight.State = OfferRebookingStateEnum.ServiceChanging;
            //    }
            //}
            //else
            //{
            var flight = Session.CurrentFlow?.RebookingSearchFlightResponse?.Flights?.SelectMany(s => s.FlightOptions).FirstOrDefault(f => f.TmobId == request.TmobId);
            Session.CurrentFlow.PNR.Flights.RemoveAll(r => r.State == OfferRebookingStateEnum.Added);
            Session.CurrentFlow.PNR.ClearServices();
            Session.Save();
            var removedFlight = Session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Removed);

            try
            {
                IbsUtility.ValidateFlightSegments(Session.CurrentFlow.PNR.Flights
                    .Where(x => x.State != OfferRebookingStateEnum.Removed)
                    .Concat(new[] { new SessionPnrFlightDTO
                    {
                        BasePrice = flight.BasePrice,
                        Id = flight.Id,
                        JourneyTime = flight.JourneyTime,
                        Segments = flight.Segments,
                        State = OfferRebookingStateEnum.Added,
                        OldTmobId = Session.CurrentFlow.ChangeFlightOldTmobId,
                        CabinClass = flight.CabinClass,
                        SelectedBundleCode = removedFlight?.SelectedBundleCode ?? "EABN"
                    }})
                    .ToList(), Session);

                Session.CurrentFlow.PNR.Flights.Add(new SessionPnrFlightDTO
                {
                    BasePrice = flight.BasePrice,
                    Id = flight.Id,
                    JourneyTime = flight.JourneyTime,
                    Segments = flight.Segments,
                    State = OfferRebookingStateEnum.Added,
                    OldTmobId = Session.CurrentFlow.ChangeFlightOldTmobId,
                    CabinClass = flight.CabinClass,
                    SelectedBundleCode = removedFlight?.SelectedBundleCode ?? "EABN"
                });
            }
            catch (Exception ex)
            {
                throw new TmobException(ex.Message);
            }

            FlightSelectResponse result = new FlightSelectResponse();
            ServicesResponse services = new ServicesResponse();
            try
            {
                //services = GetServices();
                // if (services != null) Session.CurrentFlow.Services = services;
                var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
                var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.TmobId, Session, channelConfig);
                var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
                if (response.ModifyBookingRS.ErrorType != null)
                {
                    throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
                }

                result = new FlightSelectResponse(PriceMappers.MapToBasePriceResponse(response, Session), Session.CurrentFlow.Type);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    throw ex.InnerException;
                }
                else
                {
                    throw ex;
                }
            }

            //result.FlightServices = services;

            Session.CurrentFlow.RebookingSelectFlightResponse = result;
            return result;
        }

        public BasePriceResponse RebookingSelectGolfBundle(SelectGolfBundleRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse RebookingSelectIfe(SelectIfeRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.TmobId, Session, channelConfig);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse RebookingSelectMeal(BookingSelectMealRequest request)
        {
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(x => x.Segments.Any(segment => segment.TmobId == request.SegmentTmobId));
            bool isBundleChangingType = IbsUtility.IsBundleUpgraded(Session, flight.TmobId);
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(flight.TmobId, Session,channelConfig, isBundleChangingType);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse RebookingSelectSeat(RebookingSelectSeatRequest request)
        {
            var fligt = Session.CurrentFlow.PNR.Flights.FirstOrDefault(x => x.Segments.Any(segment => segment.TmobId == request.SegmentTmobId));
            bool isBundleChangingType = IbsUtility.IsBundleUpgraded(Session, fligt.TmobId);
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(fligt.TmobId, Session, channelConfig, isBundleChangingType);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse RebookingSetCorona(SetCoronaRequest request)
        {
            throw new NotImplementedException();
        }

        public BasePriceResponse RebookingSetExtraBaggage(SetExtraBaggageRequest request)
        {
            var tmobId = request.Baggages.FirstOrDefault()?.TmobId;
            bool isBundleChangingType = IbsUtility.IsBundleUpgraded(Session, tmobId);
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(tmobId, Session, channelConfig, isBundleChangingType);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public BasePriceResponse RebookingSetSportEquipment(RebookingReSetSportEquipmentRequest request)
        {
            ModifyBookingRQ ibsNativeRequest = new ModifyBookingRQ();
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            foreach (var sportEquipment in request.SportEquipments)
            {
                bool isBundleChangingType = IbsUtility.IsBundleUpgraded(Session, sportEquipment.TmobId);
                ibsNativeRequest = ReservationMappers.MapToModifyBooking(sportEquipment.TmobId, Session, channelConfig, isBundleChangingType);
            }
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return PriceMappers.MapToBasePriceResponse(response, Session);
        }

        public ServicesResponse RebookingUpgradeBundleServices(RebookingAddExtrasServiceRequest request)
        {
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == request.TmobId);
            var segmentTmobId = flight.Segments.FirstOrDefault()?.TmobId;
            return GetServiceList(segmentTmobId, false);
        }

        public GetBaggageAllowanceResponse RebookingGetBaggageAllowance(GetBaggageAllowanceRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var IbsRequest = request.RebookingGetBaggageAllowanceRequestMap(Session, channelConfig);

            var response = IbsNativeRequester.CallAncillarySoapService(IbsRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listBaggageServicesAsync(req), Session?.SessionId);
            if (response.ListBaggageServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListBaggageServicesRS.ErrorType.errorCode, response.ListBaggageServicesRS.ErrorType.errorValue, Session.Language);
            }
            Session.CurrentFlow.SaveIbsData(response.ListBaggageServicesRS, IbsDataTypeEnum.BaggageCharge.ToString() + "_" + request.TmobId);

            var result = response.GetBaggageAllowanceResponseMap(Session, request.TmobId);

            return result;
        }

        public async Task<GetPnrInfoResponse> RebookingMakePayment(RebookingMakePaymentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public async Task<GetPnrInfoResponse> RebookingAddFlightInProfile(AddBookingToProfileRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public RegisterResponse Register(RegisterRequest request)
        {
            throw new NotImplementedException();
        }

        public GetPnrInfoResponse SearchGuest(SearchGuestRequest request, bool isWithCaptcha)
        {
            GetPnrInfoResponse retrieveResponse = new GetPnrInfoResponse();
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            
            if (!string.IsNullOrEmpty(request.TicketNumber))
            {
                retrieveResponse = GetPnrInfo(new GetPnrInfoRequest
                {
                    TicketNumber = request.TicketNumber,
                    GivenName = request.FirstName.Trim(),
                    Surname = request.LastName.Trim()
                });
            }
            else if (string.IsNullOrEmpty(request.TourOperatorNumber))
            {
                retrieveResponse = GetPnrInfo(new GetPnrInfoRequest
                {
                    OrderId = request.PnrNumber,
                    Surname = request.LastName.Trim()
                });
            }
            else if (!string.IsNullOrEmpty(request.TourOperatorNumber))
            {
                retrieveResponse = GetTourOperatorPnrInfo(new GetTourOperatorPnrInfoRequest
                {
                    TourOperatorCode = request.TourOperatorCode,//response.GuestList.FirstOrDefault().RecordLocator,
                    TourOpearatorNumber = request.TourOperatorNumber,
                    DepartureCode = request.Departure,
                    ArrivalCode = request.Arrival,
                    Surname = request.LastName.Trim(),
                    Name = request.FirstName.Trim(),
                    Flights = new List<BookingSearchFlightRequest.SearchFlightInfo>() {
                        new BookingSearchFlightRequest.SearchFlightInfo()
                        {
                            ArrivalAirport=request.Arrival,
                            DepartureAirport=request.Departure,
                            DepartureDate= default
                        }
                    }
                });

            }

            if (retrieveResponse != null
                && retrieveResponse.FlightDestionationList != null
                && retrieveResponse.PassengerList != null
                && retrieveResponse.FlightDestionationList.Count > 0
                && retrieveResponse.PassengerList.Count > 0)
            {
                // 1. Restricted SSR Kodlarını Cache'ten Al
                //var restrictedSsrs = CachedData.RestrictedSsrValuesControl ?? new List<string>();

                var checkInResControlRq = new CheckInIFlyResControlRequest();
                var restrictedSsrs = CheckInIFlyResControl(checkInResControlRq)?.RestrictedSsrValues;

                // 2. Tüm SSR servislerini birleştir
                var allSsrs = new List<BaseServiceDTO>();
                allSsrs.AddRange(retrieveResponse.Services.Meals);
                allSsrs.AddRange(retrieveResponse.Services.Seats);
                allSsrs.AddRange(retrieveResponse.Services.SportEquipments);
                allSsrs.AddRange(retrieveResponse.Services.FlexServices);
                allSsrs.AddRange(retrieveResponse.Services.GolfBundles);
                allSsrs.AddRange(retrieveResponse.Services.Others);
                allSsrs.AddRange(retrieveResponse.Services.IfeServices);

                // 3. INF yolcu kontrolü
                if (restrictedSsrs.Contains("INFT") && retrieveResponse.PassengerList.Any(p => p.PassengerType?.ToUpperInvariant() == "INF"))
                {
                    throw new TmobException("infant_checkin_not_allowed_text");
                }

                // 4. SSR Kodları eşleşiyor mu kontrol et
                var matchedSsr = allSsrs.Any(ssr => restrictedSsrs.Contains(ssr.Code?.ToUpperInvariant()));
                if (matchedSsr)
                {
                    throw new TmobException("restricted_ssrs_check_in_pop_up_text");
                }

                if (retrieveResponse.FlightDestionationList.FirstOrDefault(t => !t.IsFlown)?.CheckInTimeOut ?? true)
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "flown_flight_check_in"));
                }

                if (retrieveResponse.FlightDestionationList.FirstOrDefault(t => !t.IsFlown).IsCheckInButtonDisabled)
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "checkin_warning"));
                }

                if (retrieveResponse.FlightDestionationList.All(a => a.IsCheckInRestricted))
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "checkin_restricted_text"));
                }

                if (request.Departure != null
                 && retrieveResponse.FlightDestionationList
                                    .FirstOrDefault(t => !t.IsFlown)
                                    .Segments.Any(k => k.FlightSegmentType == "SB"))
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "checkin_standbypax_error"));
                }
            }

            CheckinPortServiceReference.searchGuestResponse response;
            if (string.IsNullOrEmpty(request.TourOperatorNumber))
            {
                var searchGuestRequest = CheckInMappers.MapToSearchGuestRequest(Session, channelConfig, request);
                response = IbsNativeRequester.CallCheckInSoapService(searchGuestRequest, GeneralConstants.CHECKIN_PORT_URL,
                                                                                                        channelConfig.Username,
                                                                                                        channelConfig.Password,
                                                                                                        channelConfig.ApiAccessKey,
                                                                                                        (client, request) => client.searchGuestAsync(request), Session?.SessionId);
            }
            else
            {
                var currentFlight = retrieveResponse.FlightDestionationList.FirstOrDefault(t => t.ArrivalCode == request.Arrival && t.DepartureCode == request.Departure);
                var tourOperatorSearchGuestRequest = new SearchGuestRequest
                {
                    Arrival = request.Arrival,
                    Departure = request.Departure,
                    FirstName = null,//request.FirstName,
                    LastName = null,//request.LastName,
                    NumberInParty = request.NumberInParty,
                    PnrNumber = retrieveResponse.PNRNumber,
                    RecordLocator = request.RecordLocator,
                    Title = request.Title,
                    TourOperatorCode = null,//request.TourOperatorCode,
                    TourOperatorNumber = null// request.TourOperatorNumber
                };
                var TourOperatorSearchGuestRequest = CheckInMappers.MapToSearchGuestRequest(Session, channelConfig, tourOperatorSearchGuestRequest);
                response = IbsNativeRequester.CallCheckInSoapService(TourOperatorSearchGuestRequest, GeneralConstants.CHECKIN_PORT_URL,
                    channelConfig.Username,
                    channelConfig.Password,
                    channelConfig.ApiAccessKey,
                    (client, request) => client.searchGuestAsync(request), Session?.SessionId);

                try
                {
                    IbsUtility.CheckedInPassengersEvaluationNative(response.CHK_SearchGuestRS, ref retrieveResponse, retrieveResponse.PassengerList.Count);
                }
                catch (Exception ex)
                {
                    Ubimecs.Infrastructure.Logging.Logger.Instance.ErrorLog(Session.SessionId, ServiceName, ex);
                }
            }

            bool IsCheckinPossible = true;

            if (response.CHK_SearchGuestRS.GuestList?.FirstOrDefault()?.SegmentGuestDetails?.Length == 1)
            {
                IsCheckinPossible = response.CHK_SearchGuestRS.GuestList.FirstOrDefault().SegmentGuestDetails.FirstOrDefault().IsCheckinPossible;
            }


            if (retrieveResponse != null
                && retrieveResponse.FlightDestionationList != null
                && retrieveResponse.FlightDestionationList.Count > 0
                && retrieveResponse.FlightDestionationList.Count == 1)
            {
                if (!IsCheckinPossible &&
                    retrieveResponse.FlightDestionationList.First().IsFlown &&
                    retrieveResponse.FlightDestionationList.First().CheckInRemainingTime.Contains("-"))
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "check_in_error_a"));
                }

                if (retrieveResponse.FlightDestionationList.First().IsFlown)
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "flown_flight_check_in"));
                }

                if (retrieveResponse.FlightDestionationList.First().IsFlown &&
                    retrieveResponse.FlightDestionationList.First().Segments.Count == 1)
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "checkin_warning"));
                }

                if (retrieveResponse.FlightDestionationList.First().IsFlown &&
                    retrieveResponse.FlightDestionationList.First().Segments.Count == 2)
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "check_in_error"));
                }

                if (retrieveResponse.FlightDestionationList.First().Segments.Count == 0)
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "checkin_warning"));
                }
            }


            return retrieveResponse;
        }

        public GetFlightSeatsResponse SeatAvailability(GetFlightSeatsRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var IbsRequest = request.ShowSeatsRequestMap(Session, channelConfig);

            var response = IbsNativeRequester.CallFlightSoapService(IbsRequest, GeneralConstants.FLIGHT_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.showSeatMapAsync(req), Session?.SessionId);

            if (response.ShowSeatMapRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ShowSeatMapRS.ErrorType.errorCode, response.ShowSeatMapRS.ErrorType.errorValue, Session.Language);
            }

            Session.CurrentFlow.SaveIbsData(response.ShowSeatMapRS, IbsDataTypeEnum.Seats.ToString() + "_" + request.SegmentTmobId);

            var result = response.ShowSeatsResponseMap();

            Session.CurrentFlow.LatestSeatResponse = result;

            return result;
        }

        public TransferPointResponse TransferPoint(TransferPointRequest request)
        {
            throw new NotImplementedException();
        }

        public GetPnrInfoResponse TravelDocumentChange(RebookingTravelDocumentChangeRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReservationMappers.MapToModifyBooking(request.TmobId, Session, channelConfig);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.modifyBookingAsync(req), Session?.SessionId);
            if (response.ModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyBookingRS.ErrorType.errorCode, response.ModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public async Task<List<CalendarRetriveResponse>> BookingCalendarRetrive(ISftpService sftpService, IAirportService airportService, CalendarRetriveRequest calendarRetriveRequest)
        {
            try
            {
                _sftpService = sftpService;
                _airportService = airportService;

                string cacheKey = string.Format("FareCache:EDF----FH-{0}{1}", calendarRetriveRequest.DepartureAirport, calendarRetriveRequest.ArrivalAirport);

                List<Flight> allFlights = CacheManager.Instance.GetOrSetBytes(cacheKey, () =>
                {
                    return sftpService.RetrieveCalendarFares(calendarRetriveRequest.DepartureAirport, calendarRetriveRequest.ArrivalAirport);
                }, -1);

                var sftpCurrency = allFlights.FirstOrDefault()?.Currency;
                var requestCurrency = calendarRetriveRequest.Currency ?? "EUR";
                decimal exchangeRate = 0;

                if (!string.Equals(sftpCurrency, requestCurrency, StringComparison.OrdinalIgnoreCase))
                {
                    var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
                    var ibsNativeRequest = BookingUtilityMappers.Map(Session, channelConfig, sftpCurrency, requestCurrency);
                    var response = IbsNativeRequester.CallBookingUtilitySoapService(ibsNativeRequest, GeneralConstants.BOOKING_UTILITY_PORT_URL,
                        channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey,
                        (client, req) => client.currencyConvertorAsync(req), Session?.SessionId);

                    if (response.CurrencyConvertorRS.ErrorType != null)
                    {
                        throw new IbsNativeException(response.CurrencyConvertorRS.ErrorType.errorCode, response.CurrencyConvertorRS.ErrorType.errorValue, Session.Language);
                    }

                    exchangeRate = Convert.ToDecimal(response.CurrencyConvertorRS.ExchangeRate);
                }

                bool isAgentBooking = Session.CurrentFlow.BookingType == BookingTypeEnum.AgentBooking;
                decimal agentDiscountAmount = decimal.TryParse(GeneralConstants.AGENT_BOOKING_DISCOUNT_AMOUNT_EUR, out var result) ? result : 0m;
                decimal discountAmount = isAgentBooking ? (exchangeRate > 0 ? agentDiscountAmount * exchangeRate : agentDiscountAmount) : 0;

                decimal CalculateDisplayAmount(decimal rawAmount)
                {
                    decimal calculated = exchangeRate > 0 ? rawAmount * exchangeRate : rawAmount;
                    if (discountAmount > 0)
                        calculated = Math.Max(calculated - discountAmount, 0);
                    return Math.Round(calculated, 2, MidpointRounding.AwayFromZero);
                }

                DateTime beginDateValue = calendarRetriveRequest.BeginDate ?? DateTime.Today;
                DateTime startOfNextMonth = new DateTime(beginDateValue.Year, beginDateValue.Month, 1).AddMonths(1);
                DateTime endOfNextMonth = new DateTime(startOfNextMonth.Year, startOfNextMonth.Month, DateTime.DaysInMonth(startOfNextMonth.Year, startOfNextMonth.Month));

                var filteredFlightsByDepartureDate = allFlights
                    .Where(x => x.DepartureDate >= beginDateValue && x.DepartureDate <= endOfNextMonth)
                    .ToList();

                List<CalendarRetriveResponse> calendarRetriveResponses = new List<CalendarRetriveResponse>();

                if (beginDateValue.Date == DateTime.Today.Date)
                {
                    var departureAirport = filteredFlightsByDepartureDate.FirstOrDefault()?.Segments?.FirstOrDefault()?.DepartureAirport;
                    if (departureAirport != null)
                    {
                        var timezoneDate = Infrastructure.Utilities.Utility.GetLocalTimeByAirportCode(departureAirport);
                        var timeFilteredFlights = filteredFlightsByDepartureDate
                            .Where(x => x.DepartureTime.TimeOfDay >= timezoneDate.TimeOfDay)
                            .Select(x => new CalendarRetriveResponse
                            {
                                ArrivalCode = x.Segments?.FirstOrDefault()?.ArrivalAirport,
                                DepartureCode = x.Segments?.FirstOrDefault()?.DepartureAirport,
                                Currency = exchangeRate > 0 ? requestCurrency : x.Currency,
                                FlightDate = x.DepartureDate,
                                DisplayAmount = CalculateDisplayAmount(Convert.ToDecimal(x.GetDisplayAmount()))
                            }).ToList();

                        if (!timeFilteredFlights.Any())
                        {
                            calendarRetriveResponses = filteredFlightsByDepartureDate
                                .Where(x => x.DepartureDate > timezoneDate)
                                .GroupBy(y => y.DepartureDate)
                                .Select(z => new CalendarRetriveResponse
                                {
                                    ArrivalCode = z.FirstOrDefault()?.Segments?.FirstOrDefault()?.ArrivalAirport,
                                    DepartureCode = z.FirstOrDefault()?.Segments?.FirstOrDefault()?.DepartureAirport,
                                    FlightDate = z.Key,
                                    Currency = exchangeRate > 0 ? requestCurrency : z.FirstOrDefault()?.Currency,
                                    DisplayAmount = CalculateDisplayAmount(Convert.ToDecimal(z.Min(z => z.GetDisplayAmount())))
                                }).ToList();
                        }
                        else
                        {
                            calendarRetriveResponses = filteredFlightsByDepartureDate
                                .GroupBy(x => x.DepartureDate)
                                .Select(y => new CalendarRetriveResponse
                                {
                                    ArrivalCode = y.FirstOrDefault()?.Segments?.FirstOrDefault()?.ArrivalAirport,
                                    DepartureCode = y.FirstOrDefault()?.Segments?.FirstOrDefault()?.DepartureAirport,
                                    FlightDate = y.Key,
                                    Currency = exchangeRate > 0 ? requestCurrency : y.FirstOrDefault()?.Currency,
                                    DisplayAmount = CalculateDisplayAmount(Convert.ToDecimal(y.Min(z => z.GetDisplayAmount())))
                                }).ToList();
                        }
                    }
                    else
                    {
                        calendarRetriveResponses = filteredFlightsByDepartureDate
                            .GroupBy(x => x.DepartureDate)
                            .Select(y => new CalendarRetriveResponse
                            {
                                ArrivalCode = y.FirstOrDefault()?.Segments?.FirstOrDefault()?.ArrivalAirport,
                                DepartureCode = y.FirstOrDefault()?.Segments?.FirstOrDefault()?.DepartureAirport,
                                FlightDate = y.Key,
                                Currency = exchangeRate > 0 ? requestCurrency : y.FirstOrDefault()?.Currency,
                                DisplayAmount = CalculateDisplayAmount(Convert.ToDecimal(y.Min(z => z.GetDisplayAmount())))
                            }).ToList();
                    }
                }
                else
                {
                    calendarRetriveResponses = filteredFlightsByDepartureDate
                        .GroupBy(x => x.DepartureDate)
                        .Select(y => new CalendarRetriveResponse
                        {
                            ArrivalCode = y.FirstOrDefault()?.Segments?.FirstOrDefault()?.ArrivalAirport,
                            DepartureCode = y.FirstOrDefault()?.Segments?.FirstOrDefault()?.DepartureAirport,
                            FlightDate = y.Key,
                            Currency = exchangeRate > 0 ? requestCurrency : y.FirstOrDefault()?.Currency,
                            DisplayAmount = CalculateDisplayAmount(Convert.ToDecimal(y.Min(z => z.GetDisplayAmount())))
                        }).ToList();
                }

                var dateRange = Infrastructure.Utilities.Utility.GetDateRange(beginDateValue, endOfNextMonth);
                var responseDates = calendarRetriveResponses.Select(c => c.FlightDate.Value).ToList();
                var alternateFlightDates = dateRange.Except(responseDates).ToList();

                //var alternateDepartures = airportService.GetAlternativeAirportsByCode(calendarRetriveRequest.DepartureAirport);

                var getAllFlights = await airportService.GetAllAirportsAsync();
                
                
                List<Flight> alternateFlights = new List<Flight>();

                foreach (var alternateFlightDate in alternateFlightDates)
                {
                    #region AlternateFlight Search Description

                    /*
                   * Örneğin DUS-AYT uçuşu arıyorum.
                     DUS için CGN alternatif havalimanı girildi
                     AYT için de BJV alternatif havalimanı girildi.
                   *
                   * DUS - BJV (To ya göre)
                   * CGN - AYT (From a göre)
                   * CGN - BJV olacak yoksa da boş olacak
                   */

                    #endregion
                    List<Flight> alternateFlightByDate = new List<Flight>();
                    
                     string? arrivalAlternative = getAllFlights
                         .FirstOrDefault(x => x.Code == calendarRetriveRequest.ArrivalAirport)?.AlternativeAirports.FirstOrDefault()?.Code; // To alternative

                     string? departureAlternative = getAllFlights
                         .FirstOrDefault(x => x.Code == calendarRetriveRequest.DepartureAirport)?.AlternativeAirports
                         .FirstOrDefault()?.Code; // From alternative
                     
                     Flight? alternativeFlight = null;
                     if (!string.IsNullOrEmpty(arrivalAlternative))
                     {
                         //To Alternative
                         alternativeFlight = GetAlternativeFlight(_sftpService,
                              calendarRetriveRequest.DepartureAirport,arrivalAlternative, alternateFlightDate);
                     }
                     
                     if (alternativeFlight == null)
                     {
                         //From Alternative
                         if (!string.IsNullOrEmpty(departureAlternative))
                         {
                             alternativeFlight = GetAlternativeFlight(_sftpService,
                                 departureAlternative,calendarRetriveRequest.ArrivalAirport, alternateFlightDate);
                         }
                        
                     }

                     if (alternativeFlight == null)
                     {
                         //FromAndTo Alternative
                         if (!string.IsNullOrEmpty(arrivalAlternative) && !string.IsNullOrEmpty(departureAlternative))
                         {
                             alternativeFlight = GetAlternativeFlight(_sftpService,
                                 departureAlternative,arrivalAlternative, alternateFlightDate);
                         }
                     }
                     
                     if (alternativeFlight != null)
                     {
                         alternateFlightByDate.Add(alternativeFlight);
                     }
                     
                    /*foreach (var departureCode in alternateDepartures)
                    {
                        string altCacheKey = string.Format("FareCache:EDF----FH-{0}{1}", departureCode, calendarRetriveRequest.ArrivalAirport);

                        var allAltFlights = CacheManager.Instance.GetOrSetBytes(altCacheKey, () =>
                        {
                            return sftpService.RetrieveCalendarFares(departureCode, calendarRetriveRequest.ArrivalAirport);
                        });

                        var altFlight = _sftpService.GetAlternateFlight(allAltFlights, alternateFlightDate);
                        if (altFlight != null)
                            alternateFlightByDate.Add(altFlight);
                    }*/

                    var minAltFlight = alternateFlightByDate.MinBy(x => x.GetDisplayAmount());
                    if (minAltFlight != null)
                        alternateFlights.Add(minAltFlight);
                }

                if (alternateFlights.Any())
                {
                    var alternates = alternateFlights.Select(a => new CalendarRetriveResponse
                    {
                        ArrivalCode = a.Segments.FirstOrDefault()?.ArrivalAirport,
                        DepartureCode = a.Segments.FirstOrDefault()?.DepartureAirport,
                        FlightDate = a.DepartureDate.Date,
                        Currency = exchangeRate > 0 ? requestCurrency : a.Currency,
                        DisplayAmount = CalculateDisplayAmount(Convert.ToDecimal(a.GetDisplayAmount())),
                        IsAlternativeFlight = true
                    }).ToList();

                    calendarRetriveResponses.AddRange(alternates);
                }

                var finalResponseDates = calendarRetriveResponses.Select(x => x.FlightDate.Value.Date).ToHashSet();
                var notIncludedDates = dateRange.Except(finalResponseDates).Select(date => new CalendarRetriveResponse
                {
                    DisplayAmount = 0,
                    FlightDate = date.Date,
                    ArrivalCode = null,
                    DepartureCode = null,
                    IsAlternativeFlight = false,
                    Currency = calendarRetriveResponses.FirstOrDefault()?.Currency
                }).ToList();

                calendarRetriveResponses.AddRange(notIncludedDates);

                return calendarRetriveResponses.OrderBy(d => d.FlightDate).ToList();
            }
            catch (Exception)
            {
                return new List<CalendarRetriveResponse>();
            }
        }

        
        private Flight? GetAlternativeFlight(ISftpService _sftpService, string departureCode,string arrivalCode,DateTime alternateFlightDate)
        {
            string altCacheKey = string.Format("FareCache:EDF----FH-{0}{1}", departureCode, arrivalCode);
            var allAltFlights = CacheManager.Instance.GetOrSetBytes(altCacheKey, () =>
            {
                return _sftpService.RetrieveCalendarFares(departureCode, arrivalCode);
            });
            return _sftpService.GetAlternateFlight(allAltFlights, alternateFlightDate);
        }
        
        public BundleResponse GetBundle(BundleRequest request)
        {
            var response = GetListSaleable(request.SegmentTmobId);
            Session.CurrentFlow.SaveIbsData(response.ListSaleableAncillaryServicesRS, IbsDataTypeEnum.ServiceListNotifable);
            return response.Map();
        }

        public listSaleableAncillaryServicesResponse GetListSaleable(string segmentTmobId, bool isNotifiableSsr = true)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var listSaleableAncillaryServicesRQ = AncillaryMappers.Map(segmentTmobId, Session, channelConfig, isNotifiableSsr);
            listSaleableAncillaryServicesResponse response = IbsNativeRequester.CallAncillarySoapService(listSaleableAncillaryServicesRQ, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);
            if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
            }
            return response;
        }

        public ServicesResponse GetServiceList(string tmobId, bool isNotifiableSsr)
        {
            var response = GetListSaleable(tmobId, isNotifiableSsr);

            return response.MapToServicesResponse();
        }

        public List<FlightSegmentDTO> GetFlightSegments(FlightSegmentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var IbsRequest = request.MapToFlightSegmentRequest(channelConfig, Session);

            var response = IbsNativeRequester.CallFlightSoapService(IbsRequest, GeneralConstants.FLIGHT_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.getThroughFlightDetailsAsync(req), Session?.SessionId);
            if (response.ThroughFlightDetailsRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ThroughFlightDetailsRS.ErrorType.errorCode, response.ThroughFlightDetailsRS.ErrorType.errorValue, Session.Language);
            }
            return response.MapToFlightSegmentResponse(Session);
        }

        public SportEquipmentResponse BookingGetSportEquipments(SportEquipmentRequest request)
        {
            var response = GetListSaleable(request.SegmentTmobId, false);
            var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.SPORTS_EQUIPMENTS);
            return ancillaryServices.MapSportEquipments();
        }

        public BundleResponse GetRebookingBundle(BundleRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = AncillaryMappers.MapRebookingAncillaryServicesRQ(request.SegmentTmobId, Session, channelConfig, true);
            var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);
            if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
            }
            var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.BUNDLE);

            return ancillaryServices.MapRebookingBundles(Session, request.SegmentTmobId);
        }

        public SendBoardingPassResponse SendBoardingPass(SendBoardingPassRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            var ibsNativeRequest = request.Map(Session, channelConfig);

            var response = IbsNativeRequester.CallCheckInSoapService(ibsNativeRequest, GeneralConstants.CHECKIN_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.sendBoardingPassAsync(req), Session?.SessionId);
            if (response.CHK_SendBoardingPassRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CHK_SendBoardingPassRS.ErrorType.errorCode, response.CHK_SendBoardingPassRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map();
        }

        public SessionAgencyInfo AgencyLogin(IAgencyService agencyService, AgencyLoginRequest request,UbimecsDbContext dbContext)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(channelConfig);

            var response = IbsNativeRequester.CallAuthenticatorSoapService(ibsNativeRequest, GeneralConstants.AUTHENTICATOR_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.authenticateAgentLoginAsync(req), Session?.SessionId);

            if (response.AuthenticateTravelAgentLoginRS.ErrorType != null)
            {
                throw new IbsNativeException(response.AuthenticateTravelAgentLoginRS.ErrorType.errorCode, response.AuthenticateTravelAgentLoginRS.ErrorType.errorValue, Session.Language);
            }

            var data = response.AuthenticateTravelAgentLoginRS.Map();

            if (data.IsLoginPwdSysGenerated || data.DaysToExpireLoginPwd <= 0)
            {
                Session.AgencyInfo = null;
                data.IsResetPasswordRequired = true;
                return data;
            }

            var agencyPermission = agencyService.GetAgencyPermissions(request.AgentId, request.ProjectId);

            if (agencyPermission != null)
            {
                data.AgencyPermissions = agencyPermission.Select(p => new AgencyPermission
                {
                    Id = p.Id,
                    Code = p.Code,
                    DisplayName = p.DisplayName,
                    IsActive = p.IsActive
                }).ToList();
            }
            data.ImageUrl = dbContext.Agencies?.FirstOrDefault(x=>x.AgencyCode == data.AgencyCode)?.ImageUrl;
            
            Session.AgencyInfo = data;
            try
            {
                var detailRequest = new GetTravelAgentDetailRequest
                {
                    UserId = request.AgentId
                };

                var detail = GetTravelAgentDetail(detailRequest);
                data.IsDefaultAgent = detail.Result.IsDefaultAgent;
            }
            catch (Exception ex)
            {
                Logger.Instance.ErrorLog(Session.SessionId, ServiceName, ex);
            }

            Session.AgencyInfo = data;
            return data;
        }

        public AgencyForgotPasswordResponse AgencyForgotPassword(AgencyForgotPasswordRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(channelConfig);

            var response = IbsNativeRequester.CallAuthenticatorSoapService(ibsNativeRequest, GeneralConstants.AUTHENTICATOR_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.changeTravelAgentPasswordAsync(req), Session?.SessionId);

            if (response.ChangeTravelAgentPasswordRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ChangeTravelAgentPasswordRS.ErrorType.errorCode, response.ChangeTravelAgentPasswordRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map();
        }

        public AgencyChangePasswordResponse AgencyChangePassword(AgencyChangePasswordRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(channelConfig);

            var response = IbsNativeRequester.CallAuthenticatorSoapService(ibsNativeRequest, GeneralConstants.AUTHENTICATOR_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.changeTravelAgentPasswordAsync(req), Session?.SessionId);

            if (response.ChangeTravelAgentPasswordRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ChangeTravelAgentPasswordRS.ErrorType.errorCode, response.ChangeTravelAgentPasswordRS.ErrorType.errorValue, Session.Language);
            }
            return new AgencyChangePasswordResponse
            {
                Status = response.ChangeTravelAgentPasswordRS.Status
            };
        }

        public CreateAgencyRegistrationResponse CreateAgencyRegistration(CreateAgencyRegistrationRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(channelConfig);

            var response = IbsNativeRequester.CallAgencySoapService(ibsNativeRequest, GeneralConstants.AGENCY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.createAgencyRegistrationAsync(req), Session?.SessionId);

            if (response.CreateAgencyRegistrationRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CreateAgencyRegistrationRS.ErrorType.errorCode, response.CreateAgencyRegistrationRS.ErrorType.errorValue, Session.Language);
            }

            return new CreateAgencyRegistrationResponse
            {
                Status = "STATUS_OK"
            };
        }

        public AgencyGetDashboardSalesReportResponse GetDashboardSalesReports(AgencyGetDashboardSalesReportRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = new ReportsPortServiceReference.AgencySalesReportRQ
            {
                AirlineCode = GeneralConstants.AIRLINECODE,
                AgencyCode = Session.AgencyInfo.AgencyCode,
                SalesFromDate = DateTime.Now.AddYears(-2).Date,
                SalesToDate = DateTime.Now.Date,
                BookingChannel = new ReportsPortServiceReference.BookingChannelKeyType
                {
                    Channel = IbsUtility.GetChannel(Session),
                    ChannelType = channelConfig.ChannelCode, //GeneralConstants.BOOKING_CHANNEL_TYPE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(Session.AgencyInfo.SessionId) ? Session.AgencyInfo.SessionId : null
                }
            };

            var response = IbsNativeRequester.CallReportsSoapService(ibsNativeRequest, GeneralConstants.REPORTS_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveAgencySalesReportAsync(req), Session?.SessionId);
            if (response.AgencySalesReportRS.ErrorType != null)
            {
                throw new IbsNativeException(response.AgencySalesReportRS.ErrorType.errorCode, response.AgencySalesReportRS.ErrorType.errorValue, Session.Language);
            }

            return response.Map();
        }

        public CreateIssueResponse CreateLostBaggageIssue(ICsmService csmService, LostBaggageRequest request)
        {
            return csmService.CreateLostBaggageIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateForgottenPersonalItemsIssue(ICsmService csmService, ForgottenPersonalItemsRequest request)
        {
            return csmService.CreateForgottenPersonalItemsIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateDelayedDeliveryRequestsIssue(ICsmService csmService,
            DelayedDeliveryRequestsIssueRequest request)
        {
            return csmService.CreateDelayedDeliveryRequestsIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateDamagedBaggageRequestsIssue(ICsmService csmService,
            DamagedBaggageRequestsIssueRequest request)
        {
            return csmService.CreateDamagedBaggageRequestsIssue(request).GetAwaiter().GetResult();
        }

        public GetRequestStatusResponse GetRequestStatus(ICsmService csmService, GetRequestStatusRequest request)
        {
            return csmService.GetRequestStatus(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse BoardingDenial(ICsmService csmService, BoardingDenialRequest request)
        {
            return csmService.CreateBoardingDenialIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse FlightCancellationIssue(ICsmService csmService, FlightCancelRequest request)
        {
            return csmService.FlightCancellationIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateDelayedFlightIssue(ICsmService csmService, DelayedFlightIssueRequest request)
        {
            return csmService.CreateDelayedFlightIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateGeneralFormIssue(ICsmService csmService, GeneralFormIssueRequest request)
        {
            return csmService.CreateGeneralFormIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateSecurityIncidentReportIssue(ICsmService csmService, SecurityIncidentReportRequest request)
        {
            return csmService.CreateSecurityIncidentReportIssue(request).GetAwaiter().GetResult();
        }

        public CreateIssueResponse CreateWheelChairReportIssue(ICsmService csmService, WheelChairReportRequest request)
        {
            return csmService.CreateWheelChairReportIssue(request).GetAwaiter().GetResult();
        }

        public async Task<FlightStatusResponse> FlightStatus(ICsmService csmService, FlightStatusRequest request)
        {
            return await csmService.GetFlightStatus(request);
        }
        
        public CreatePersonResponse CreatePerson(ICrmService crmService, CreatePersonRequestDto request)
        {
            //TODO: GDA implementation should be added here.    


            var getPersonResponse = crmService.GetPerson(new PersonRequestDto { Email = request.Email }).Result;

            if (getPersonResponse != null && getPersonResponse.Person.Any())
            {
                throw new Next4BizCrmException(HttpStatusCode.BadRequest, "Person already exists");
            }

            var response = crmService.CreatePerson(request).Result;

            return new CreatePersonResponse
            {
                PersonId = response.PersonId,
            };
        }

        public async Task<UpdatePersonDateOfBirthResponse> UpdatePersonDateOfBirth(ICrmService crmService,
            UpdatePersonDateOfBirthRequestDto request)
        {
            var response = await crmService.UpdatePersonDateOfBirth(request);
            return new UpdatePersonDateOfBirthResponse
            {
                Success = response.Success,
            };
        }

        public async Task<UpdatePersonNationalityResponse> UpdatePersonNationality(ICrmService crmService, UpdatePersonNationalityRequestDto request)
        {
            var response = await crmService.UpdatePersonNationality(request);
            return new UpdatePersonNationalityResponse
            {
                Success = response.Success
            };
        }

        public async Task<UpdatePersonIbsProfileResponse> UpdatePersonIbsProfile(ICrmService crmService, UpdatePersonIbsProfileIdRequestDto request)
        {
            var response = await crmService.UpdatePersonIbsProfile(request);
            return new UpdatePersonIbsProfileResponse
            {
                Success = response.Success
            };
        }

        public SignInResponse SignIn(ICrmService crmService, SignInRequestDto request)
        {
            var getPersonResponse = crmService.GetPerson(new PersonRequestDto { Email = request.Email }).Result;

            return new SignInResponse
            {
                Person = getPersonResponse.Person?.FirstOrDefault()
            };
        }

        public async Task<List<GetPnrInfoResponse>> GetPastFlight(ICrmService crmService, Next4BizCrmSettings next4BizCrmSettings, PastFlightRequestDto request)
        {
            List<GetPnrInfoResponse> pnrList = new();
            var pastFlightResponse = await crmService.GetPerson(new PersonRequestDto { Email = request.Email });
            string? surname = pastFlightResponse.Person?.FirstOrDefault()?.Surname.ToUpperInvariant();
            List<FlightDetailResponseDto> flights = pastFlightResponse.Person?.FirstOrDefault()?.FlightDetails
                ?.Select(f => f.ToResponseDto(next4BizCrmSettings.Environment)).ToList() ?? new();

            if (flights.Any() && !string.IsNullOrEmpty(surname))
            {
                var pagedFlights = flights.ToPagedList(request.Page);
                pagedFlights.ForEach(flight =>
                {
                    try
                    {
                        var pnrInfoResponse = GetPnrInfo(new GetPnrInfoRequest{OrderId = flight.PnrNumber,Surname = surname});
                        pnrList.Add(pnrInfoResponse);
                    }
                    catch (Exception ex)
                    {
                        
                    }
                });
                return pnrList;
            }
            throw new Next4BizCrmException(HttpStatusCode.InternalServerError, "Flights not found or surname not found");
        }

        public CreateTravelCompanionResponse CreateTravelCompanion(ICrmService crmService, Next4BizCrmSettings next4BizCrmSettings,
            CreateTravelCompanionRequestDto request)
        {
            return crmService.CreateTravelCompanion(request).Result;
        }

        public GetTravelCompanionResponse GetTravelCompanion(ICrmService crmService, GetTravelCompanionRequestDto request, Next4BizCrmSettings next4BizCrmSettings)
        {
            var personResponse = crmService.GetPerson(new PersonRequestDto { Email = request.Email }).Result;
            return new GetTravelCompanionResponse()
            {
                TravelCompanions = personResponse.Person?.FirstOrDefault()?.TravelCompanions?.Select(f => f.ToResponseDto(next4BizCrmSettings.Environment)).ToList()
            };
        }

        public async Task<GetCommunicationPermissionResponse> GetCommunicationPermissions(ICrmService crmService, GetCommunicationPermissionRequestDto request,
            Next4BizCrmSettings next4BizCrmSettings)
        {
            var emailPermissionResponse =  await crmService.GetPersonEmailPermission(request);
            var phonePermissionResponse =  await crmService.GetPersonPhonePermission(request);

            return new GetCommunicationPermissionResponse
            {
               IsMailAuthorized = emailPermissionResponse.IsAuthorized,
               IsPhoneAuthorized = phonePermissionResponse.IsCallAuthorized,
               IsSmsAuthorized = phonePermissionResponse.IsSmsAuthorized
            };

        }

        public async Task<UpdateCommunicationPermissionResponse> UpdateCommunicationPermissions(ICrmService crmService, UpdateCommunicationPermissionRequestDto request,
            Next4BizCrmSettings next4BizCrmSettings,
            ICommunicationService communicationService)
        {
            try
            {
                var emailPermissionResponse =  await crmService.UpdatePersonEmailPermission(request);
                var phonePermissionResponse =  await crmService.UpdatePersonPhonePermission(request);
                
                var communicationResponse = await communicationService.RegisterAsync(new CreateSubscriptionRequest
                {
                    Name = Session.IdentityInfo?.Name,
                    Surname = Session.IdentityInfo?.Surname,
                    Email = Session.IdentityInfo?.Email,
                    PhoneNumber = Session.IdentityInfo?.PhoneNumber,
                    EmailPermission = request.IsEmailAuthorized,
                    SmsPermission = request.IsSmsAuthorized
                }, Session.SessionId);
                
                return new UpdateCommunicationPermissionResponse
                {
                    Success = true
                };
            }
            catch (Exception ex)
            {
                return new UpdateCommunicationPermissionResponse
                {
                    Success = false
                };
            }
           
        }

        public RemoveTravelCompanionResponse RemoveTravelCompanion(ICrmService crmService, RemoveTravelCompanionRequestDto request,
            Next4BizCrmSettings next4BizCrmSettings)
        {
            var removeTravelCompanionResponse = crmService.RemoveTravelCompanion(request).Result;
            return new RemoveTravelCompanionResponse
            {
                Status = removeTravelCompanionResponse.Status ? "Success" : "Fail",
            };
        }

        public async Task<GetProfileUpcomingFlightResponse> GetProfileUpcomingFlights(GetProfileUpcomingFlightRequest request)
        {
            GetProfileUpcomingFlightResponse upcomingFlightResponse = new GetProfileUpcomingFlightResponse();
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig, Session);
            var response = await IbsNativeRequester.CallCustomerProfileSoapService(ibsNativeRequest, GeneralConstants.CUSTOMER_PROFILE_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveCustomerBookedListAsync(req), Session?.SessionId);
            if (response.CustomerBookedListRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CustomerBookedListRS.ErrorType.errorCode, response.CustomerBookedListRS.ErrorType.errorValue, Session.Language);
            }
            if (response.CustomerBookedListRS.PNRList.Any())
            {
                var now = DateTime.UtcNow.Date;
                var upcomingPnrList = response.CustomerBookedListRS.PNRList.Where(p =>p.DepDateTime.Date >= now).ToList();
                upcomingFlightResponse.PnrList = new();
                var filteredPnrList = upcomingPnrList.ToPagedList(request.Page);
                filteredPnrList.ForEach(pnr =>
                {
                    var surname = pnr.CustomerDetails.FirstOrDefault()?.CustomerName?.Split('/').FirstOrDefault();
                    var pnrInfoResponse = GetPnrInfo(new GetPnrInfoRequest
                    {
                        OrderId = pnr.PnrNumber,
                        Surname = surname
                    });
                    upcomingFlightResponse.PnrList.Add(pnrInfoResponse);
                });
            }
            return upcomingFlightResponse;
        }


        public CancelBookingResponse CancelBooking(CancelBookingRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig, Session);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.cancelBookingAsync(req), Session?.SessionId);
            if (response.CancelBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CancelBookingRS.ErrorType.errorCode, response.CancelBookingRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map(Session);
        }

        public GetPnrInfoResponse CompleteCancelBooking(CompleteCancelBookingRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(channelConfig, Session);

            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public GetExtraServicesResponse RebookingGetExtraServices(RebookingGetExtraServicesRequest request)
        {
            var fligtTmobId = Session.CurrentFlow.PNR.Flights
                .FirstOrDefault(x => x.Segments.Any(y => y.TmobId == request.SegmentTmobId))?.TmobId;
            var sessionFlightIds = IbsUtility.GetFlightIdsByFlightType(Session, fligtTmobId);
            var flightIds = new List<string>();

            var allExtraServices = new Dictionary<string, List<BaseServiceDTO>>();
            foreach (var sessionFlightId in sessionFlightIds)
            {
                var segmentTmodId = Session.CurrentFlow.PNR.Flights
                    .FirstOrDefault(x => x.TmobId == sessionFlightId)?.Segments.FirstOrDefault()?.TmobId;

                if (string.IsNullOrEmpty(segmentTmodId))
                {
                    continue; // Skip if no segment is found for the flight
                }

                var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
                var ibsNativeRequest = AncillaryMappers.MapRebookingAncillaryServicesRQ(segmentTmodId, Session, channelConfig);
                var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listSaleableAncillaryServicesAsync(req), Session?.SessionId);

                if (response.ListSaleableAncillaryServicesRS.ErrorType != null)
                {
                    throw new IbsNativeException(response.ListSaleableAncillaryServicesRS.ErrorType.errorCode, response.ListSaleableAncillaryServicesRS.ErrorType.errorValue, Session.Language);
                }
                Session.CurrentFlow.SaveIbsData(response.ListSaleableAncillaryServicesRS, IbsDataTypeEnum.ServiceListNotifable);

                var ancillaryServices = response.MapToAncillaryService(NativeAPIAncillaryConstants.OTHERS);
                var mappedExtraServices = ancillaryServices.MapExtraServices();

                foreach (var serviceGroup in mappedExtraServices.ExtraServices)
                {
                    if (allExtraServices.ContainsKey(serviceGroup.Key))
                    {
                        flightIds.Add(sessionFlightId);
                        foreach (var baseServiceDto in serviceGroup.Value)
                        {
                            baseServiceDto.FlightIds = flightIds.ToList();
                        }
                        allExtraServices[serviceGroup.Key].AddRange(serviceGroup.Value);
                    }
                    else
                    {
                        foreach (var baseServiceDto in serviceGroup.Value)
                        {
                            baseServiceDto.FlightIds = new List<string> { sessionFlightId };
                        }

                        allExtraServices[serviceGroup.Key] = serviceGroup.Value;
                    }
                }
            }

            return new GetExtraServicesResponse
            {
                ExtraServices = allExtraServices
            };
        }

        public AdjustFlightInventoryResponse AdjustFlightInventory(bool isForMarking = false)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = AdjustFlightInventoryMappers.Map(Session, channelConfig, isForMarking);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.adjustFlightInventoryAsync(req), Session?.SessionId);

            if (response.AdjustFlightInventoryRS.Status != null && response.AdjustFlightInventoryRS.Status == "STATUS_OK")
            {
                Session.CurrentFlow.PNR.IbsSessionId = response.AdjustFlightInventoryRS.PnrSessionId;
            }
            else
            {
                Session.CurrentFlow.PNR.IbsSessionId = null;
            }

            return response.Map();
        }

        public void GuaranteeAncillary()
        {
            var seats = Session.CurrentFlow.PNR.Seats;
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = GuaranteeAncillaryMappers.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.guaranteeAncillaryAsync(req), Session?.SessionId);
            if (response.GuaranteeAncillaryRS.ErrorType != null)
            {
                Logger.Instance.FileLog(Session.SessionId, "GuaranteeAncillaryService", response.GuaranteeAncillaryRS, ResponseStatusCodeEnum.ERROR_OCCURED);
            }
            else
            {
                foreach (var seatAssignmentDetails in response.GuaranteeAncillaryRS.SeatAssignmentDetails)
                {
                    seats.ForEach(seat =>
                    {
                        string seatNumber = string.Format("{0}{1}", seat.Number, seat.Column);
                        var guestSeatDetail = seatAssignmentDetails.GuestSeatDetails.FirstOrDefault(x => x.SeatNumbers == seatNumber);
                        if (guestSeatDetail != null)
                        {
                            seat.SeatMarkingId = guestSeatDetail.SeatMarkingId;
                        }
                    });
                }

                Session.Save();
            }

        }

        public void ReleaseAncillary(List<SegmentPassengerSeat> seats)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = ReleaseAncillaryMappers.Map(seats, Session, channelConfig);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.releaseAncillaryAsync(req), Session?.SessionId);
            if (response.ReleaseAncillaryRS.ErrorType != null)
            {
                Infrastructure.Logging.Logger.Instance.FileLog(Session.SessionId, "ReleaseAncillaryService", response.ReleaseAncillaryRS, ResponseStatusCodeEnum.ERROR_OCCURED);
            }
        }

        public bool RestrictedAirportsControl(SessionCache session, PrintBoardingPassRequest request)
        {
            var restrictedAirportList = CachedData.RestrictedAirportControl;
            var departureCode = request.FlightSegmentTmobId.Split('|').FirstOrDefault();
            if (restrictedAirportList != null)
            {
                return restrictedAirportList.Any(r => r == departureCode);
            }
            return false;
        }

        public GetInvoiceFieldMetadataResponse GetInvoiceFieldMetadata(GetInvoiceFieldMetadataRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallAncillarySoapService(ibsNativeRequest, GeneralConstants.ANCILLARY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveSSRFieldMetaDataAsync(req), Session?.SessionId);
            if (response.RetrieveSSRFieldMetaDataRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveSSRFieldMetaDataRS.ErrorType.errorCode, response.RetrieveSSRFieldMetaDataRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map();
        }

        public async Task<PaginatedResponse<AgencySalesSummary>> GetSalesReports(AgencyGetSalesReportRequest request, PaginationSettings paginationSettings)
        {
            AgencyGetSalesReportResponse response;
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            if (request.ReportType != ReportType.Summary)
            {
                var detailedReportRequest = request.MapDetailedReportRequest(Session, channelConfig);
                var detailedReportResponse = IbsNativeRequester.CallReportsSoapService(detailedReportRequest, GeneralConstants.REPORTS_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.listAgentInvoiceAsync(req), Session?.SessionId);
                if (detailedReportResponse.ListAgentInvoicesRS.ErrorType != null)
                {
                    throw new IbsNativeException(detailedReportResponse.ListAgentInvoicesRS.ErrorType.errorCode, detailedReportResponse.ListAgentInvoicesRS.ErrorType.errorValue, Session.Language);
                }
                response = detailedReportResponse.Map(Session);
            }
            else
            {
                var summaryReportRequest = request.Map(Session, channelConfig);
                var summaryReportResponse = IbsNativeRequester.CallReportsSoapService(summaryReportRequest, GeneralConstants.REPORTS_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.bimonthlySalesSummaryReportAsync(req), Session?.SessionId);
                if (summaryReportResponse.BimonthlySalesSummaryReportRS.ErrorType != null)
                {
                    throw new IbsNativeException(summaryReportResponse.BimonthlySalesSummaryReportRS.ErrorType.errorCode, summaryReportResponse.BimonthlySalesSummaryReportRS.ErrorType.errorValue, Session.Language);
                }
                response = summaryReportResponse.Map(Session);
            }
            if (request.IsPagingEnabled)
                return response.SalesSummaries.ToPaginatedResponse(request.PageNumber, request.PageSize);
            
            return new PaginatedResponse<AgencySalesSummary>
            {
                PageNumber = 1,
                PageSize = response.SalesSummaries.Count,
                Items = response.SalesSummaries,
                TotalItems = response.SalesSummaries.Count
            };
        }

        public AgencyGetInvoiceDetailsResponse GetAgencyInvoiceDetails(AgencyGetInvoiceDetailsRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            
            var mappedResponse = new AgencyGetInvoiceDetailsResponse();
            if (request.ReportType == ReportType.Detailed)
            {
                var ibsNativeRequest = request.Map(Session, channelConfig);
                var response = IbsNativeRequester.CallAgencySoapService(ibsNativeRequest, GeneralConstants.AGENCY_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveAgentInvoiceDetailsAsync(req), Session?.SessionId);
                if (response.RetrieveAgentInvoiceDetailsRS.ErrorType != null)
                {
                    throw new IbsNativeException(response.RetrieveAgentInvoiceDetailsRS.ErrorType.errorCode, response.RetrieveAgentInvoiceDetailsRS.ErrorType.errorValue, Session.Language);
                }

                mappedResponse = response.Map();
            }
            else if (request.ReportType == ReportType.Summary)
            {
                var ibsNativeRequest = request.BimonthlySalesDetailedReportsMap(Session, channelConfig);
                var response = IbsNativeRequester.CallReportsSoapService(ibsNativeRequest, GeneralConstants.REPORTS_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.bimonthlySalesDetailedReportAsync(req), Session?.SessionId);
                if (response.BimonthlySalesDetailedReportRS.ErrorType != null)
                {
                    throw new IbsNativeException(response.BimonthlySalesDetailedReportRS.ErrorType.errorCode, response.BimonthlySalesDetailedReportRS.ErrorType.errorValue, Session.Language);
                }
                
                mappedResponse = response.Map();
            }

            return mappedResponse;
        }

        public GetGroupBookingsResponse GetGroupBookings(GetGroupBookingsRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveReservationSummaryAsync(req), Session?.SessionId);
            if (response.RetrieveReservationSummaryRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveReservationSummaryRS.ErrorType.errorCode, response.RetrieveReservationSummaryRS.ErrorType.errorValue, Session.Language);
            }
            return response.Map(request);
        }

        public GetPnrInfoResponse ModifyGroupBookingComment(ModifyGroupBookingCommentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public GetPnrInfoResponse ModifyGroupBookingContact(ModifyGroupBookingContactRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public GetPnrInfoResponse MakeGroupBookingPayment(MakeGroupBookingPaymentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public GetPnrInfoResponse UploadGroupBookingPassengerList(UploadPassengerListDTO passengerListDto)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = passengerListDto.Map(Session, channelConfig);
            
            var response = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.saveModifyBookingAsync(req), Session?.SessionId);
            if (response.SaveModifyBookingRS.ErrorType != null)
            {
                throw new IbsNativeException(response.SaveModifyBookingRS.ErrorType.errorCode, response.SaveModifyBookingRS.ErrorType.errorValue, Session.Language);
            }
            return GetPnrInfoMappers.MapPnrInfoResponse(response, Session);
        }

        public AgencyGetInvoiceDetailsResponse GroupBookingInvoiceAccess(GroupBookingInvoiceAccessRequest request)
        {
            var latestPnrResponse = Session.CurrentFlow.PnrInfoResponse;
            AgencyGetInvoiceDetailsResponse invoiceDetailsResponse = new AgencyGetInvoiceDetailsResponse();
            if (latestPnrResponse != null)
            {
                invoiceDetailsResponse = GetAgencyInvoiceDetails(new AgencyGetInvoiceDetailsRequest
                {
                    InvoiceDate = latestPnrResponse.PaymentDetails.FirstOrDefault()?.PaymentDate,
                    InvoiceNumber = latestPnrResponse.PNRNumber,
                    FopType = latestPnrResponse.GroupPnrFopType,
                    Currency = latestPnrResponse.PaymentDetails.FirstOrDefault()?.PaymentCurrency
                });

                if (invoiceDetailsResponse.AgentInvoiceDetails != null)
                {
                    invoiceDetailsResponse.AgentInvoiceDetails = invoiceDetailsResponse.AgentInvoiceDetails
                        .Where(x => x.PnrNumber == latestPnrResponse.PNRNumber)
                        .ToList();
                }
            }

            return invoiceDetailsResponse;
        }

        public async Task<PaginatedResponse<ReservationDetail>> GetSearchReservations(AgencySearchReservationServiceRequest request, PaginationSettings paginationSettings)
        {
            AgencySearchReservationServiceResponse response;
            
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);
            
            var ibsResponse = IbsNativeRequester.CallReservationSoapService(ibsNativeRequest, GeneralConstants.RESERVATION_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.retrieveReservationSummaryAsync(req), Session?.SessionId);
            if (ibsResponse.RetrieveReservationSummaryRS.ErrorType != null)
            {
                throw new IbsNativeException(ibsResponse.RetrieveReservationSummaryRS.ErrorType.errorCode, ibsResponse.RetrieveReservationSummaryRS.ErrorType.errorValue, Session.Language);
            }
            response = ibsResponse.Map();
            if (request.IsPagingEnabled)
                return response.Reservations.ToPaginatedResponse(request.PageNumber, request.PageSize);
            return new PaginatedResponse<ReservationDetail>
            {
                PageNumber = 1,
                PageSize = response.Reservations.Count,
                Items = response.Reservations,
                TotalItems = response.Reservations.Count
            };
        }

        public CreateTravelAgentResponse CreateTravelAgent(CreateTravelAgentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallAgencySoapService(ibsNativeRequest, GeneralConstants.AGENCY_PORT_URL, channelConfig.Username, channelConfig.Password,channelConfig.ApiAccessKey, (client, req) => client.createTravelAgentAsync(req), Session?.SessionId);

            if (response.CreateTravelAgentRS.Status != "CREATED")
            {
                throw new IbsNativeException(response.CreateTravelAgentRS.ErrorType.errorCode, response.CreateTravelAgentRS.ErrorType.errorValue, Session.Language);
            }

            return response.Map();
        }

        public UpdateTravelAgentResponse UpdateTravelAgent(IAgencyService agencyService, UpdateTravelAgentRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var soapRequest = request.Map(Session, channelConfig);

            var response = IbsNativeRequester.CallAgencySoapService(
                soapRequest,
                GeneralConstants.AGENCY_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.modifyTravelAgentAsync(req),
                Session?.SessionId
            );

            if (response.ModifyTravelAgentRS.Status != "MODIFIED")
            {
                throw new IbsNativeException(response.ModifyTravelAgentRS.ErrorType.errorCode, response.ModifyTravelAgentRS.ErrorType.errorValue, Session.Language);
            }

            agencyService.UpdateAgencyPermissions(request.UserID, request.ProjectId, request.Permissions);

            return response.Map();
        }

        public async Task<PaginatedResponse<TravelAgentDetail>> ListTravelAgents(IAgencyService agencyService,ListTravelAgentsRequest request, PaginationSettings paginationSettings)
        {
            ListTravelAgentsResponse response;
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var ibsNativeResponse = IbsNativeRequester.CallAgencySoapService(
                ibsNativeRequest,
                GeneralConstants.AGENCY_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.listTravelAgentsAsync(req),
                Session?.SessionId
            );

            response = ibsNativeResponse.Map();
            
            var updatedList = new List<TravelAgentDetail>();

            var agentPermissions = await agencyService.GetAllAgencyPermissions(
                new List<string> { Session.AgencyInfo.AgencyCode },
                request.ProjectId);


            foreach (var agent in response.TravelAgents)
            {
                try
                {
                    var detailRequest = new GetTravelAgentDetailRequest
                    {
                        UserId = agent.UserID
                    };

                    var detail = await GetTravelAgentDetail(detailRequest);
                    agent.IsDefaultAgent = detail.IsDefaultAgent;
                    var agentPermission = agentPermissions.FirstOrDefault(x => x.AgencyId == agent.AgencyCode);
                    agent.Permissions = agentPermission?.Permissions;
                }
                catch (Exception ex)
                {
                    Logger.Instance.ErrorLog(Session.SessionId, ServiceName, ex);
                }

                updatedList.Add(agent);
            }

            return updatedList.ToPaginatedResponse(request.PageNumber, request.PageSize);
        }

        public async Task<TravelAgentDetail> GetTravelAgentDetail(GetTravelAgentDetailRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);
            var ibsNativeResponse = IbsNativeRequester.CallAgencySoapService(
                ibsNativeRequest,
                GeneralConstants.AGENCY_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.retrieveTravelAgentDetailsAsync(req),
                Session?.SessionId
            );
            if (ibsNativeResponse.RetrieveTravelAgentDetailsRS.ErrorType != null)
            {
                throw new IbsNativeException(ibsNativeResponse.RetrieveTravelAgentDetailsRS.ErrorType.errorCode, ibsNativeResponse.RetrieveTravelAgentDetailsRS.ErrorType.errorValue, Session.Language);
            }
            return ibsNativeResponse.Map();
        }

        public ChangeTravelAgentStatusResponse ActivateTravelAgents(ChangeTravelAgentStatusRequest request)
        {
            var result = new ChangeTravelAgentStatusResponse();

            foreach (var userId in request.UserIds)
            {
                var singleRequest = new ChangeTravelAgentStatusRequest
                {
                    UserIds = new List<string> { userId },
                    AgencyCode = request.AgencyCode
                };

                var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
                var soapRequest = singleRequest.MapToActive(Session, channelConfig);
                var response = IbsNativeRequester.CallAgencySoapService(
                    soapRequest,
                    GeneralConstants.AGENCY_PORT_URL,
                    channelConfig.Username,
                    channelConfig.Password,
                    channelConfig.ApiAccessKey,
                    (client, req) => client.activateTravelAgentAsync(req),
                    Session?.SessionId
                );

                if (response.ActivateTravelAgentRS.ErrorType != null)
                {
                    throw new IbsNativeException(response.ActivateTravelAgentRS.ErrorType.errorCode, response.ActivateTravelAgentRS.ErrorType.errorValue, Session.Language);
                }

                var mapped = response.MapToActive(userId);
                result.Results.Add(new ChangeTravelAgentStatusResult
                {
                    UserID = userId,
                    IsSuccess = mapped.IsSuccess
                });
            }

            return result;
        }

        public ChangeTravelAgentStatusResponse DeactivateTravelAgents(ChangeTravelAgentStatusRequest request)
        {
            var result = new ChangeTravelAgentStatusResponse();

            foreach (var userId in request.UserIds)
            {
                var singleRequest = new ChangeTravelAgentStatusRequest
                {
                    UserIds = new List<string> { userId },
                    AgencyCode = request.AgencyCode
                };

                var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
                var soapRequest = singleRequest.MapToDeactivate(Session, channelConfig);
                var response = IbsNativeRequester.CallAgencySoapService(
                    soapRequest,
                    GeneralConstants.AGENCY_PORT_URL,
                    channelConfig.Username,
                    channelConfig.Password,
                    channelConfig.ApiAccessKey,
                    (client, req) => client.deactivateTravelAgentAsync(req),
                    Session?.SessionId
                );

                if (response.DeactivateTravelAgentRS.ErrorType != null)
                {
                    throw new IbsNativeException(response.DeactivateTravelAgentRS.ErrorType.errorCode, response.DeactivateTravelAgentRS.ErrorType.errorValue, Session.Language);
                }

                var mapped = response.MapToDeactivate(userId);
                result.Results.Add(new ChangeTravelAgentStatusResult
                {
                    UserID = userId,
                    IsSuccess = mapped.IsSuccess
                });
            }

            return result;
        }

        public UpdateAgencyProfileResponse UpdateAgencyProfile(UpdateAgencyProfileRequest request, IFileStorageService fileStorageService,UbimecsDbContext dbContext)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var soapRequest = request.Map(Session, channelConfig);
            string imageUrl = null;   
            var response = IbsNativeRequester.CallAgencySoapService(
                soapRequest,
                GeneralConstants.AGENCY_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.modifyAgencyProfileAsync(req),
                Session?.SessionId
            );

            if (response.ModifyAgencyProfileRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ModifyAgencyProfileRS.ErrorType.errorCode, response.ModifyAgencyProfileRS.ErrorType.errorValue, Session.Language);
            }

            if (!string.IsNullOrEmpty(request.ImageUrl))
            {
                var streamImage = fileStorageService.GetFileStream(request.ImageUrl);
                string fileName = Guid.NewGuid().ToString();
                imageUrl = fileStorageService
                    .UploadFileAsync(streamImage, FolderType.B2BAgencyProfile, fileName, request.ContentType).Result;
            }
            var map = response.Map();
            map.ImageUrl = imageUrl;
            
            var agency = dbContext.Agencies.FirstOrDefault(x => x.AgencyCode == Session.AgencyInfo.AgencyCode);
            if (agency != null)
            {
                string key = agency.ImageUrl?.Split('/').Last();
                agency.ImageUrl = imageUrl;
                var deleteResponse = fileStorageService.DeleteFileAsync(key, FolderType.B2BAgencyProfile).Result;
            }
            else
            {
                dbContext.Agencies.Add(new Domain.Entities.UbimecsEntities.Agency()
                    { AgencyCode = Session.AgencyInfo.AgencyCode, ImageUrl = imageUrl });
            }

            dbContext.SaveChanges();
            Session.AgencyInfo.ImageUrl = imageUrl;
            return map;
        }

        public async Task<PaginatedResponse<QueuedPNR>> ListReservationQueue(ListReservationQueueRequest request, PaginationSettings paginationSettings)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var ibsNativeRequest = request.Map(Session, channelConfig);

            ListReservationQueueResponse response;
            var ibsNativeResponse = IbsNativeRequester.CallAirlineQueueSoapService(
                ibsNativeRequest,
                GeneralConstants.AIRLINE_QUEUE_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.viewReservationsInQueueAsync(req),
                Session?.SessionId
            );

            if (ibsNativeResponse.ViewPNRsInQueueRS.ErrorType != null)
            {
                throw new IbsNativeException(ibsNativeResponse.ViewPNRsInQueueRS.ErrorType.errorCode, ibsNativeResponse.ViewPNRsInQueueRS.ErrorType.errorValue, Session.Language);
            }

            response = ibsNativeResponse.Map();
            return response.QueuedPNRs.ToPaginatedResponse(request.PageNumber, request.PageSize);
        }

        public RemoveReservationFromQueueResponse RemoveReservationFromQueue(RemoveReservationFromQueueRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            var soapRequest = request.Map(Session, channelConfig);

            var response = IbsNativeRequester.CallAirlineQueueSoapService(
                soapRequest,
                GeneralConstants.AIRLINE_QUEUE_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.removeMultipleReservationsFromQueueAsync(req),
                Session?.SessionId
            );

            if (response.RemoveMultiplePnrsFromQueueRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RemoveMultiplePnrsFromQueueRS.ErrorType.errorCode, response.RemoveMultiplePnrsFromQueueRS.ErrorType.errorValue, Session.Language);
            }

            return response.Map();
        }

        public ProcessDisruptedFlightResponse AcceptDisruptedFlight(ProcessDisruptedFlightRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2CDirect);
            if (Session.AgencyInfo != null)
            {
                channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            }
            var soapRequest = request.MapToAcceptRQ(Session, channelConfig);

            var response = IbsNativeRequester.CallReservationSoapService(
                soapRequest,
                GeneralConstants.RESERVATION_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.acceptScAsync(req),
                Session?.SessionId
            );

            if (response.AcceptScRS.ErrorType != null)
            {
                throw new IbsNativeException(response.AcceptScRS.ErrorType.errorCode, response.AcceptScRS.ErrorType.errorValue, Session.Language);
            }

            return response?.Map();
        }

        public ProcessDisruptedFlightResponse RejectDisruptedFlight(ProcessDisruptedFlightRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2CDirect);
            if (Session.AgencyInfo != null)
            {
                channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent);
            }
            var soapRequest = request.MapToRejectRQ(Session, channelConfig);

            var response = IbsNativeRequester.CallReservationSoapService(
                soapRequest,
                GeneralConstants.RESERVATION_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.rejectScAsync(req),
                Session?.SessionId
            );

            if (response.RejectScRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RejectScRS.ErrorType.errorCode, response.RejectScRS.ErrorType.errorValue, Session.Language);
            }

            return response?.Map();
        }

        public ListDutyCodesForAgencyResponse ListDutyCodesForAgency(ListDutyCodesForAgencyRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent); 
            var soapRequest = request.Map(Session, channelConfig);

            var response = IbsNativeRequester.CallAgencySoapService(
                soapRequest,
                GeneralConstants.AGENCY_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.retrieveDutyCodesForAgencyAsync(req),
                Session?.SessionId
            );

            if (response.RetrieveDutyCodesForAgencyRS.ErrorType != null)
            {
                throw new IbsNativeException(response.RetrieveDutyCodesForAgencyRS.ErrorType.errorCode, response.RetrieveDutyCodesForAgencyRS.ErrorType.errorValue, Session.Language);
            }

            return response?.Map();
        }

        public GetAgencyProfileResponse GetAgencyProfile(GetAgencyProfileRequest request, UbimecsDbContext ubimecsDbContext)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.B2BAgent); 
            var soapRequest = request.Map(Session, channelConfig);

            var response = IbsNativeRequester.CallAgencySoapService(
                soapRequest,
                GeneralConstants.AGENCY_PORT_URL,
                channelConfig.Username,
                channelConfig.Password,
                channelConfig.ApiAccessKey,
                (client, req) => client.viewAgencyProfileAsync(req),
                Session?.SessionId
            );

            if (response.ViewAgencyProfileRS.ErrorType != null)
            {
                throw new IbsNativeException(response.ViewAgencyProfileRS.ErrorType.errorCode, response.ViewAgencyProfileRS.ErrorType.errorValue, Session.Language);
            }
            
            var data = response.Map(Session);
            var agencyCode = Session.AgencyInfo?.AgencyCode;
            data.ImageUrl = ubimecsDbContext.Agencies?.FirstOrDefault(x=>x.AgencyCode == agencyCode)?.ImageUrl;
            return data;
        }

        public List<SpecialOfferResponse> GetSpecialOffers(
            SpecialOfferRequest request,
            ISftpService sftpService,
            IAirportService airportService,
            ISpecialOfferService specialOfferService)
        {
            _sftpService = sftpService;
            _airportService = airportService;
            _specialOfferService = specialOfferService;

            var rule = specialOfferService.GetSpecialOfferRules(request.Origin, request.ProjectId)
                .OrderByDescending(r => r.Priority)
                .FirstOrDefault();
            if (rule == null) return new List<SpecialOfferResponse>();

            var today = DateTime.UtcNow.Date;
            var seenODs = new HashSet<string>();
            var flightCandidates = new List<SpecialOfferResponse>();

            var routeResults = sftpService.GetRoutes(new List<string> { rule.Origin });
            var allODs = routeResults.SelectMany(x => x.ArrivalAirportCodes.Select(dest => new { rule.Origin, Destination = dest }));

            foreach (var od in allODs)
            {
                var odKey = $"{od.Origin}-{od.Destination}";
                if (seenODs.Contains(odKey)) continue;

                string cacheKey = $"FareCache:EDF----FH-{od.Origin}{od.Destination}";
                var allFlights = CacheManager.Instance.GetOrSetBytes(cacheKey, () =>
                    sftpService.RetrieveCalendarFares(od.Origin, od.Destination));

                var startDate = rule.FlightStartDate < today ? today : rule.FlightStartDate;

                var bestFlight = allFlights
                    .Where(f => f.DepartureDate >= startDate && f.DepartureDate <= rule.FlightEndDate)
                    .Where(f => rule.Prices.Any(p => p.Currency == f.Currency && f.GetDisplayAmount() <= (double)p.MaxPrice))
                    .OrderBy(f => f.GetDisplayAmount()).ThenBy(f => f.DepartureDate)
                    .FirstOrDefault();

                if (bestFlight != null)
                {
                    var airport = airportService.GetAirportImageByCode(od.Destination);

                    flightCandidates.Add(new SpecialOfferResponse
                    {
                        Origin = od.Origin,
                        Destination = od.Destination,
                        Currency = bestFlight.Currency,
                        Price = Convert.ToDecimal(bestFlight.GetDisplayAmount()),
                        OutboundDate = bestFlight.DepartureDate,
                        LowestFare = true,
                        File = airport?.File != null ? new FileResponse
                        {
                            FileS3Key = airport.File.FileS3Key,
                            Extension = airport.File.Extension,
                            AltText = airport.File.AltText
                        } : null
                    });
                    seenODs.Add(odKey);
                }
            }

            return flightCandidates
                .GroupBy(f => $"{f.Origin}-{f.Destination}")
                .Select(g => g.First())
                .OrderBy(f => f.Price)
                .ThenBy(f => f.OutboundDate)
                .Take(4)
                .ToList();
        }


        public List<CustomizableOfferWidgetResponse> GetCustomizableOffers(
            CustomizableOfferRequest request,
            ISftpService sftpService,
            IAirportService airportService,
            ISpecialOfferService specialOfferService)
        {
            _sftpService = sftpService;
            _airportService = airportService;
            _specialOfferService = specialOfferService;

            var widgetResults = new List<CustomizableOfferWidgetResponse>();
            var allRoutes = new List<(CustomizableOfferWidgetResponse Widget, OfferRoute Route)>();
            var seenODs = new HashSet<string>();
            var maxCount = request.MaxVisibleOffers;

            var ruleSets = specialOfferService.GetCustomizableWidgets(request.ProjectId);

            foreach (var rule in ruleSets)
            {
                var widget = new CustomizableOfferWidgetResponse
                {
                    Id = rule.Id,
                    Title = rule.Title,
                    Visible = rule.Visible,
                    FlightStartDate = rule.FlightStartDate,
                    FlightEndDate = rule.FlightEndDate,
                    Routes = new List<OfferRoute>()
                };

                foreach (var route in rule.Routes)
                {
                    var odKey = $"{route.Origin}-{route.Destination}";
                    if (seenODs.Contains(odKey))
                        continue;

                    var effectiveStartDate = rule.FlightStartDate < DateTime.UtcNow.Date
                        ? DateTime.UtcNow.Date
                        : rule.FlightStartDate;

                    string cacheKey = $"FareCache:EDF----FH-{route.Origin}{route.Destination}";
                    var flights = CacheManager.Instance.GetOrSetBytes(cacheKey, () =>
                    sftpService.RetrieveCalendarFares(route.Origin, route.Destination));

                    flights.Where(f => f.DepartureDate >= effectiveStartDate && f.DepartureDate <= rule.FlightEndDate)
                        .Where(f => route.Prices.Any(p => p.Currency == f.Currency && f.GetDisplayAmount() <= (double)p.MaxPrice))
                        .OrderBy(f => f.GetDisplayAmount())
                        .ThenBy(f => f.DepartureDate)
                        .ToList();

                    var best = flights.FirstOrDefault();
                    if (best != null)
                    {
                        var airport = airportService.GetAirportImageByCode(route.Destination);
                        var offerRoute = new OfferRoute
                        {
                            Origin = route.Origin,
                            Destination = route.Destination,
                            Currency = best.Currency,
                            Price = Convert.ToDecimal(best.GetDisplayAmount()),
                            FlightDate = best.DepartureDate,
                            LowestFare = true,
                            File = airport?.File != null ? new FileResponse
                            {
                                FileS3Key = airport.File.FileS3Key,
                                Extension = airport.File.Extension,
                                AltText = airport.File.AltText
                            } : null
                        };

                        allRoutes.Add((widget, offerRoute));
                        seenODs.Add(odKey);
                    }
                }
            }

            var selectedRoutes = allRoutes
                .GroupBy(x => $"{x.Route.Origin}-{x.Route.Destination}")
                .Select(g => g.OrderBy(x => x.Route.Price).ThenBy(x => x.Route.FlightDate).First())
                .OrderBy(x => x.Route.Price)
                .ThenBy(x => x.Route.FlightDate)
                .Take(maxCount)
                .ToList();

            foreach (var item in selectedRoutes)
            {
                item.Widget.Routes = new List<OfferRoute> { item.Route };
                widgetResults.Add(item.Widget);
            }

            return widgetResults;
        }

        public List<SpecialOfferResponse> GetDiscoverOffers(
            SpecialOfferRequest request,
            ISftpService sftpService,
            IAirportService airportService,
            ISpecialOfferService specialOfferService)
        {
            _sftpService = sftpService;
            _airportService = airportService;
            _specialOfferService = specialOfferService;

            var rule = specialOfferService.GetDiscoverOfferRules(request.Origin, request.ProjectId)
                .OrderByDescending(r => r.Priority)
                .FirstOrDefault();
            if (rule == null) return new List<SpecialOfferResponse>();

            var today = DateTime.UtcNow.Date;
            var flightCandidates = new List<SpecialOfferResponse>();

            var odList = string.IsNullOrWhiteSpace(request.Destination)
                ? sftpService.GetRoutes(new List<string> { rule.Origin })
                      .SelectMany(x => x.ArrivalAirportCodes.Select(dest => (rule.Origin, dest)))
                      .ToList()
                : new List<(string, string)> { (request.Origin, request.Destination) };

            foreach (var (origin, destination) in odList)
            {
                string cacheKey = $"FareCache:EDF----FH-{origin}{destination}";
                var allFlights = CacheManager.Instance.GetOrSetBytes(cacheKey, () =>
                    sftpService.RetrieveCalendarFares(origin, destination));

                var startDate = rule.FlightStartDate < today ? today : rule.FlightStartDate;

                foreach (var route in rule.Routes)
                {
                    route.Origin ??= rule.Origin;
                }

                var matchingRoute = rule.Routes.FirstOrDefault(r =>
                    r.Origin == origin && r.Destination == destination);

                if (matchingRoute == null || matchingRoute.Prices == null || !matchingRoute.Prices.Any())
                    continue;

                var flights = allFlights
                    .Where(f => f.DepartureDate >= startDate && f.DepartureDate <= rule.FlightEndDate)
                    .Where(f => matchingRoute.Prices.Any(p =>
                        p.Currency == f.Currency && f.GetDisplayAmount() <= (double)p.MaxPrice))
                    .OrderBy(f => f.GetDisplayAmount())
                    .ThenBy(f => f.DepartureDate)
                    .ToList();

                if (flights.Any())
                {
                    var lowest = flights.First().GetDisplayAmount();
                    var lowestGroup = flights.Where(f => Math.Abs(f.GetDisplayAmount() - lowest) < 0.01).ToList();
                    var selected = lowestGroup[new Random().Next(lowestGroup.Count)];
                    var airport = airportService.GetAirportImageByCode(destination);
                    flightCandidates.Add(new SpecialOfferResponse
                    {
                        Origin = origin,
                        Destination = destination,
                        Currency = selected.Currency,
                        Price = Convert.ToDecimal(selected.GetDisplayAmount()),
                        OutboundDate = selected.DepartureDate,
                        LowestFare = true,
                        File = airport?.File != null ? new FileResponse
                        {
                            FileS3Key = airport.File.FileS3Key,
                            Extension = airport.File.Extension,
                            AltText = airport.File.AltText
                        } : null
                    });
                }
            }

            return flightCandidates
                .OrderBy(_ => Guid.NewGuid()) // rastgele shuffle
                .Take(1)
                .ToList();
        }


        public List<SpecialOfferResponse> GetRoundTripOffers(
            SpecialOfferRequest request,
            ISftpService sftpService,
            IAirportService airportService,
            ISpecialOfferService specialOfferService)
        {
            _sftpService = sftpService;
            _airportService = airportService;
            _specialOfferService = specialOfferService;

            var today = DateTime.UtcNow.Date;
            var maxCount = request.MaxVisibleOffers;

            var rules = specialOfferService.GetRoundtripOffers(request.Origin, request.Destination, request.ProjectId);
            var rule = rules.OrderByDescending(r => r.Priority).FirstOrDefault();
            if (rule == null) return new List<SpecialOfferResponse>();

            var flightCandidates = new List<SpecialOfferResponse>();
            var odList = new List<(string Origin, string Destination)>();

            if (!string.IsNullOrWhiteSpace(request.Destination))
            {
                odList.Add((request.Origin, request.Destination));
            }
            else
            {
                var routeResults = sftpService.GetRoutes(new List<string> { rule.Origin });
                odList = routeResults
                    .SelectMany(x => x.ArrivalAirportCodes.Select(dest => (rule.Origin, dest)))
                    .ToList();
            }

            foreach (var (origin, destination) in odList)
            {
                string outCacheKey = $"FareCache:EDF----FH-{origin}{destination}";
                string retCacheKey = $"FareCache:EDF----FH-{destination}{origin}";

                var allOutbound = CacheManager.Instance.GetOrSetBytes(outCacheKey, () =>
                {
                    return sftpService.RetrieveCalendarFares(origin, destination);
                });

                var allReturn = CacheManager.Instance.GetOrSetBytes(retCacheKey, () =>
                {
                    return sftpService.RetrieveCalendarFares(destination, origin);
                });

                var startDate = rule.FlightStartDate < today ? today : rule.FlightStartDate;
                var endDate = rule.FlightEndDate;

                var outboundFlights = allOutbound
                    .Where(f => f.DepartureDate >= startDate && f.DepartureDate <= endDate)
                    .OrderBy(f => f.GetDisplayAmount())
                    .ThenBy(f => f.DepartureDate)
                    .ToList();

                var returnFlights = allReturn
                    .Where(f => f.DepartureDate >= startDate && f.DepartureDate <= endDate)
                    .OrderBy(f => f.GetDisplayAmount())
                    .ThenBy(f => f.DepartureDate)
                    .ToList();

                var bestCombo = (from outF in outboundFlights
                                 from retF in returnFlights
                                 where retF.DepartureDate > outF.DepartureDate
                                 let total = outF.GetDisplayAmount() + retF.GetDisplayAmount()
                                 orderby total, outF.DepartureDate
                                 select new
                                 {
                                     Outbound = outF,
                                     Return = retF,
                                     TotalPrice = total
                                 }).FirstOrDefault();

                if (bestCombo != null)
                {
                    var airport = airportService.GetAirportImageByCode(destination);
                    flightCandidates.Add(new SpecialOfferResponse
                    {
                        Origin = origin,
                        Destination = destination,
                        OutboundDate = bestCombo.Outbound.DepartureDate,
                        ReturnDate = bestCombo.Return.DepartureDate,
                        Currency = bestCombo.Outbound.Currency,
                        Price = Convert.ToDecimal(bestCombo.TotalPrice),
                        LowestFare = true,
                        File = airport?.File != null ? new FileResponse
                        {
                            FileS3Key = airport.File.FileS3Key,
                            Extension = airport.File.Extension,
                            AltText = airport.File.AltText
                        } : null
                    });
                }
            }

            return flightCandidates
                .GroupBy(f => $"{f.Origin}-{f.Destination}")
                .Select(g => g.OrderBy(f => f.Price).ThenBy(f => f.OutboundDate).First())
                .OrderBy(f => f.Price)
                .ThenBy(f => f.OutboundDate)
                .Take(maxCount)
                .ToList();
        }

        public List<SpecialOfferResponse> GetExploreOffers(
            SpecialOfferRequest request,
            ISftpService sftpService,
            IAirportService airportService,
            ISpecialOfferService specialOfferService)
        {
            _sftpService = sftpService;
            _airportService = airportService;
            _specialOfferService = specialOfferService;

            var rule = _specialOfferService.GetExploreOfferRules(request.Origin, request.Destination, request.ProjectId)
                .OrderByDescending(f => f.Priority)
                .FirstOrDefault();

            if (rule == null)
            {
                return default;
            }
            var flightCandidates = new List<SpecialOfferResponse>();
            var seenODs = new HashSet<string>();
            var today = DateTime.UtcNow.Date;

            var odList = new List<(string Origin, string Destination)>();

            if (!string.IsNullOrWhiteSpace(request.Destination))
            {
                odList.Add((request.Origin, request.Destination));
            }
            else
            {
                var routeResults = sftpService.GetRoutes(new List<string> { rule.Origin });
                odList = routeResults
                    .SelectMany(x => x.ArrivalAirportCodes.Select(dest => (rule.Origin, dest)))
                    .ToList();
            }

            foreach (var od in odList)
            {
                var odKey = $"{od.Origin}-{od.Destination}";

                string cacheKey = string.Format("FareCache:EDF----FH-{0}{1}", od.Origin, od.Destination);

                var allFlights = CacheManager.Instance.GetOrSetBytes(cacheKey, () =>
                {
                    return sftpService.RetrieveCalendarFares(od.Origin, od.Destination);
                });

                var effectiveStartDate = rule.FlightStartDate < today ? today : rule.FlightStartDate;

                foreach (var route in rule.Routes)
                {
                    route.Origin ??= rule.Origin;
                }
                var matchingRoute = rule.Routes.FirstOrDefault(r =>
                    r.Origin == od.Origin && r.Destination == od.Destination);

                var matchingFlights = allFlights
                    .Where(f => f.DepartureDate >= effectiveStartDate && f.DepartureDate <= rule.FlightEndDate)
                    .Where(f => matchingRoute.Prices.Any(p => p.Currency == f.Currency && f.GetDisplayAmount() <= (double)p.MaxPrice))
                    .OrderBy(f => f.GetDisplayAmount())
                    .ThenBy(f => f.DepartureDate)
                    .ToList();
                var airport = airportService.GetAirportImageByCode(od.Destination);
                var bestFlight = matchingFlights.FirstOrDefault();
                if (bestFlight != null)
                {
                    flightCandidates.Add(new SpecialOfferResponse
                    {
                        Origin = od.Origin,
                        Destination = od.Destination,
                        Currency = bestFlight.Currency,
                        Price = Convert.ToDecimal(bestFlight.GetDisplayAmount()),
                        OutboundDate = bestFlight.DepartureDate,
                        LowestFare = true,
                        File = airport?.File != null ? new FileResponse
                        {
                            FileS3Key = airport.File.FileS3Key,
                            Extension = airport.File.Extension,
                            AltText = airport.File.AltText
                        } : null
                    });
                }
            }

            var distinctFlights = flightCandidates
                .GroupBy(f => $"{f.Origin}-{f.Destination}")
                .Select(g => g.OrderBy(f => f.Price).ThenBy(f => f.OutboundDate).First())
                .OrderBy(f => f.Price)
                .ThenBy(f => f.OutboundDate)
                .Take(1)
                .ToList();

            return distinctFlights;
        }

        public async Task<List<CarParkingResponse>> CarParking(IHolidayExtrasService holidayExtrasService, CarParkingRequest request)
        {
            List<CarParkingResponse> carParkingResponse = new List<CarParkingResponse>();
            var tokenResponse = await holidayExtrasService.GenerateTokenAsync();
            string token = tokenResponse.APIReply.Token.ToString();
            string filter = CarParkingMappers.MapEnumToCarParkingFilter[request.Filter];
            var carParkResponse = await holidayExtrasService.CarParkingAsync(new CarParkingFilterRequest
            {
                ArrivalCode = request.ArrivalCode,
                ArrivalDate = request.ArrivalDate,
                DepartureDate = request.DepartureDate,
                NumberOfPax = request.NumberOfPax,
                ArrivalTime = request.ArrivalTime,
                DepartureTime = request.DepartureTime
            }, token,
               filter);

            var carparks = carParkResponse.APIReply.CarParks;
            if (carparks.Any())
            {
                foreach (CarParkDetail carpark in carparks)
                {
                    var carParkData = new CarParkingResponse();
                    carParkData.Name = carpark.Name;
                    carParkData.TotalPrice = carpark.TotalPrice;
                    carParkData.Code = carpark.Code;
                    var carParkDetail = await holidayExtrasService.GetCarParkingDetailsAsync(token, carpark.MoreInfoURL);
                    if (carParkDetail.APIReply.Product.Any())
                    {
                        var product = carParkDetail.APIReply.Product.FirstOrDefault();
                        carParkData.CarParkingDetail = new CarParkingDetailResponse
                        {
                            Name = product.Name,
                            Description = product.Description,
                            Image = holidayExtrasService.GetImageUrl(product.MainImage),
                            Latitude = product.Latitude,
                            Longitude = product.Longitude,
                            LeadTime = product.LeadTime,
                            MaxStay = product.MaxStay,
                            MinStay = product.MinStay,
                            Phone = product.Phone,
                            Transfer = product.Transfer
                        };
                    }
                    carParkingResponse.Add(carParkData);
                }
            }
            Session.CurrentFlow.SaveIbsData(carParkingResponse, IbsDataTypeEnum.CarParkingResponse);
            return carParkingResponse;
        }

        public async Task<List<CarParkingFilterResponse>> GetCarParkingFilters(IHolidayExtrasService holidayExtrasService, CarParkingFilterRequest request)
        {
            var tokenResponse = await holidayExtrasService.GenerateTokenAsync();
            List<CarParkingFilterResponse> filterResponses = new List<CarParkingFilterResponse>();
            foreach (string filter in CarParkingMappers.CarParkingFilterStrings)
            {
                var response =
                    await holidayExtrasService.CarParkingAsync(request, tokenResponse.APIReply.Token.ToString(),
                        filter);
                if (response.APIReply.CarParks.Any())
                {
                    filterResponses.Add(new CarParkingFilterResponse
                    {
                        Filter = CarParkingMappers.MapCarParkingFilterEnums[filter],
                        Price = response.APIReply.CarParks?.Min(x => x.TotalPrice),
                        Currency = "" //TODO: Holiday Extras not returned that value
                    });
                }

            }
            return filterResponses;
        }

        public async Task<List<TransferResponse>> GetGroundTransfer(IHolidayExtrasService holidayExtrasService, GroundTransferRequest request)
        {
            List<TransferResponse> groundTransferResponses = new List<TransferResponse>();
            var tokenResponse = await holidayExtrasService.GenerateTokenAsync();
            string token = tokenResponse.APIReply.Token.ToString();
            var groundTransferResponse = await holidayExtrasService.GetGroundTransferAsync(request, token);
            var transfers = groundTransferResponse.APIReply.Transfers;
            if (transfers.Any())
            {
                groundTransferResponses = transfers.Select(transfer => new TransferResponse
                {
                    Name = transfer.Name,
                    Code = transfer.Code,
                    Currency = transfer.SalesCurrency,
                    Features = new List<string>(), //TODO: not returned by service
                    ImageUrl = transfer.Images.FirstOrDefault()?.Src,
                    JourneyTimeMinutes = transfer.OutboundTransfer.JourneyTime,
                    MaxCapacity = transfer.VehicleDetails.MaxCapacity,
                    MaxSuitcases = transfer.VehicleDetails.Bags,
                    TotalPrice = transfer.TotalPrice
                }).ToList();
            }

            Session.CurrentFlow.SaveIbsData(groundTransferResponses, IbsDataTypeEnum.GroundTransferResponse);

            return groundTransferResponses;
        }

        private TimeSpan GetPaginationCacheDuration(PaginationSettings paginationSettings)
        {
            return TimeSpan.FromMinutes(paginationSettings.DefaultCacheDurationInMinutes);
        }

        private void SetIdentityUserInfo(UserInfo userInfo)
        {
            Session.IdentityInfo.Name = userInfo.Name;
            Session.IdentityInfo.Surname = userInfo.Surname;
            Session.IdentityInfo.Email = userInfo.Email;
            Session.IdentityInfo.PhoneNumber = userInfo.Phone;
        }
        public CancelCheckInResponse CancelCheckIn(CancelCheckInRequest request)
        {
            var channelConfig = _channelConfigProvider.Get(ChannelTypeEnum.WebCheckin);
            var cancelCheckInRequest = request.Map(Session, channelConfig);
            var response = IbsNativeRequester.CallCheckInSoapService(cancelCheckInRequest, GeneralConstants.CHECKIN_PORT_URL, channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) => client.uncheckGuestAsync(req), Session?.SessionId);
            if (response.CHK_UncheckGuestRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CHK_UncheckGuestRS.ErrorType.errorCode, response.CHK_UncheckGuestRS.ErrorType.errorValue, Session.Language);
            }

            return new CancelCheckInResponse
            {
                IsSuccessful = true
            };
        }

        public async Task<List<TicketListResponse>> GetTicketListAsync(IInvoicePortalService invoicePortalService, TicketListRequest request)
        {
            var result = await invoicePortalService.GetTicketListAsync(request);

            if (result?.TicketInformation == null)
            {
                throw new TmobException("API response is null or contains no ticket data");
            }

            return result?.TicketInformation.Select(t => new TicketListResponse
            {
                TicketNumber = t.TicketNo,
                Uuid = t.Uuid,
                TicketType = t.TicketType,
                ServiceType = t.ServiceType,
                IssueDateTime = t.IssueDateTime,
                PassengerName = t.PassName,
                PassengerSurname = t.PassSurname,
                Departure = t.Departure,
                Arrival = t.Arrival,
                DepartureDateTime = t.DepartureDateTime,
                ArrivalDateTime = t.ArrivalDateTime,
                StatusCode = t.TicketStatusCode,
                StatusDescription = t.TicketStatusDescription
            }).ToList() ?? new List<TicketListResponse>();
        }

        public async Task<TicketDetailResponse> GetTicketDetailAsync(IInvoicePortalService invoicePortalService, TicketDetailRequest request)
        {
            var result = await invoicePortalService.GetTicketDetailAsync(request);

            return new TicketDetailResponse
            {
                Uuid = result.Uuid,
                TicketNo = result.ObjectNo,
                Type = result.Type,
                ContentType = Infrastructure.Utilities.Utility.DetectFileContentType(result.BinaryData),
                BinaryData = result.BinaryData,
            };
        }
        
        public GetAuthorizationUrlResponse GetGdaAuthorizationUrl(IGdaService gdaService, GetAuthorizationUrlRequest request)
        {
            return new GetAuthorizationUrlResponse
            {
                AuthorizationUrl = gdaService.GetAuthorizationUrl(request.RedirectUri)
            };
        }

        public async Task<PersonResponseDto> GetUserInfoAsync(IGdaService gdaService, ICrmService crmService, GetUserInfoRequest request)
        {
            string cacheKey = $"{CacheDataKeys.Authorization}:{request.AuthorizationCode}";
            int ttlMinutes = -1;
            var response = CacheManager.Instance.Get<SessionIdentityInfo>(cacheKey);
            if (response != null)
            {
                Session.IdentityInfo = response;
                if (Session.IdentityInfo.IsExpired)
                {
                    var refreshTokenResponse = await gdaService.RefreshTokenAsync(Session.IdentityInfo.RefreshToken);
                    if (refreshTokenResponse == null || string.IsNullOrEmpty(refreshTokenResponse.AccessToken))
                    {
                        throw new TmobException("AccessToken alınamadı");
                    }

                    Session.IdentityInfo = new SessionIdentityInfo
                    { 
                        Code = request.AuthorizationCode,
                        AccessToken = refreshTokenResponse.AccessToken,
                        RefreshToken = refreshTokenResponse.RefreshToken,
                        TokenExpires = refreshTokenResponse.ExpiresIn,
                        ReceivedTime = DateTime.UtcNow
                    };
                    
                    ttlMinutes = (int)Math.Ceiling(Session.IdentityInfo.TokenExpires / 60.0);
                    CacheManager.Instance.Set(cacheKey, Session.IdentityInfo, ttlMinutes);
                }
            }
            else
            {
                var tokenResponse = await gdaService.GetAccessTokenAsync(request.AuthorizationCode, request.RedirectUri);
                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
                {
                    throw new TmobException("AccessToken alınamadı");
                }

                Session.IdentityInfo = new SessionIdentityInfo
                { 
                    Code = request.AuthorizationCode,
                    AccessToken = tokenResponse.AccessToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    TokenExpires = tokenResponse.ExpiresIn,
                    ReceivedTime = DateTime.UtcNow
                };
                
                ttlMinutes = (int)Math.Ceiling(Session.IdentityInfo.TokenExpires / 60.0);
                CacheManager.Instance.Set(cacheKey, Session.IdentityInfo, ttlMinutes);
            }

            #region OldCode
            // if (Session.IdentityInfo == null || Session.IdentityInfo.Code != request.AuthorizationCode)
            // {
            //     var tokenResponse = await gdaService.GetAccessTokenAsync(request.AuthorizationCode, request.RedirectUri);
            //     if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
            //     {
            //         throw new TmobException("AccessToken alınamadı");
            //     }
            //
            //     Session.IdentityInfo = new SessionIdentityInfo
            //     { 
            //         Code = request.AuthorizationCode,
            //         AccessToken = tokenResponse.AccessToken,
            //         RefreshToken = tokenResponse.RefreshToken,
            //         TokenExpires = tokenResponse.ExpiresIn,
            //         ReceivedTime = DateTime.UtcNow
            //     };
            // }
            // else if (!Session.IdentityInfo.IsExpired)
            // {
            //     var refreshTokenResponse = await gdaService.RefreshTokenAsync(Session.IdentityInfo.RefreshToken);
            //     if (refreshTokenResponse == null || string.IsNullOrEmpty(refreshTokenResponse.AccessToken))
            //     {
            //         throw new TmobException("AccessToken alınamadı");
            //     }
            //
            //     Session.IdentityInfo = new SessionIdentityInfo
            //     { 
            //         Code = request.AuthorizationCode,
            //         AccessToken = refreshTokenResponse.AccessToken,
            //         RefreshToken = refreshTokenResponse.RefreshToken,
            //         TokenExpires = refreshTokenResponse.ExpiresIn,
            //         ReceivedTime = DateTime.UtcNow
            //     };
            // }
            // else
            // {
            //     Session.IdentityInfo = null;
            //     throw new TmobException("Login required");
            // }
            

            #endregion
           

            var userInfo = await gdaService.GetUserInfoAsync(Session.IdentityInfo.AccessToken);
            SetIdentityUserInfo(userInfo);
            userInfo.Title = userInfo.Title.ToUpper();
            Session.IdentityInfo.Gender = userInfo.Title == "MR" ? "Male" : "Female";
            
            var createCustomerResponse = await GetOrCreateCustomerProfile(userInfo);

            var crmResponse = await crmService.GetOrCreatePerson(userInfo.Email, new CreatePersonRequestDto()
            {
                Name = userInfo.Name,
                Surname = userInfo.Surname,
                Gender = userInfo.Title == "MR" ? "Male" : "Female",
                Email = userInfo.Email,
                Phone = userInfo.Phone.Replace("+",""),
                DateOfBirth = Convert.ToDateTime(userInfo.DateOfBirth),
                Nationality = userInfo.Nationality,
                IbsProfileId = createCustomerResponse.ProfileId
            });
            
            Session.IdentityInfo.CrmPersonId = crmResponse.Person.FirstOrDefault()?.PersonId;
            Session.IdentityInfo.IbsPersonId = createCustomerResponse.ProfileId;
            Session.IdentityInfo.Email = userInfo.Email;
            CacheManager.Instance.Set(cacheKey, Session.IdentityInfo, ttlMinutes);
            crmResponse.Person.ForEach(p => p.IbsPersonId = createCustomerResponse.ProfileId);
            return crmResponse;
        }

        public async Task<DeleteAccountResponse> DeleteAccountAsync(IGdaService gdaService, ICrmService crmService, DeleteAccountRequest request)
        {
            
            DeleteAccountResponse response = new DeleteAccountResponse
            {
                IsSuccessful = false
            };
            
            var result = await gdaService.DeleteAccountAsync(Session.IdentityInfo.AccessToken);

            if (result)
            {
                var crmResponse = await crmService.CloseAccount(new CloseAccountRequestDto
                {
                    PersonId = request.PersonId,
                    IsActive = false
                });
                
                if (crmResponse.Status)
                    response.IsSuccessful = true;
            }

            return response;
        }

        public async Task<LogoutResponse> LogoutAsync(IGdaService gdaService)
        {
            var response = await gdaService.LogoutAsync(Session.IdentityInfo.AccessToken);
            return new LogoutResponse { IsSuccessful = response };
        }

        public async Task<CustomerProfileResponse?> GetCustomerProfile(string email)
        { 
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest = CustomerMappers.Map(email, channelConfig);
            var response = await IbsNativeRequester.CallCustomerProfileSoapService(ibsNativeRequest, GeneralConstants.CUSTOMER_PROFILE_PORT_URL,channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) =>  client.listCustomerProfileAsync(req), Session?.SessionId);
            if (response.ListCustomerProfileRS.ErrorType != null)
            {
                return null;
            }
            
            var profile = response.ListCustomerProfileRS.ListProfile.FirstOrDefault();
            if (profile == null) return null;
            
            return new CustomerProfileResponse
            {
                ProfileId = profile?.ProfileID,
                ProfileType = profile?.ProfileType,
                Status = profile?.Status
            };
        }

        public async Task<CustomerProfileResponse?> CreateCustomerProfile(UserInfo userInfo)
        {
            var channelConfig = _channelConfigProvider.Get(IbsUtility.GetChannelType(Session));
            var ibsNativeRequest =  CustomerMappers.CreateCustomerProfileMap(userInfo, channelConfig);
            var response = await IbsNativeRequester.CallCustomerProfileSoapService(ibsNativeRequest, GeneralConstants.CUSTOMER_PROFILE_PORT_URL,channelConfig.Username, channelConfig.Password, channelConfig.ApiAccessKey, (client, req) =>  client.createCustomerProfileAsync(req), Session?.SessionId);
            if (response.CreateCustomerProfileRS.ErrorType != null)
            {
                throw new IbsNativeException(response.CreateCustomerProfileRS.ErrorType.errorCode, response.CreateCustomerProfileRS.ErrorType.errorValue, Session.Language);
            }

            return new CustomerProfileResponse { ProfileId = response.CreateCustomerProfileRS.ProfileId };
        }

        public async Task<CustomerProfileResponse?> GetOrCreateCustomerProfile(UserInfo userInfo)
        {
            var customerProfile = await GetCustomerProfile(userInfo.Email);
            if (customerProfile != null) return customerProfile;
            return await CreateCustomerProfile(userInfo);
        }
        public async Task<CreateSubscriptionResponse> CreateSubscription(ICommunicationService communicationService,ICrmService crmService, CreateSubscriptionRequest request)
        {
            var crmResponse = await crmService.GetOrCreatePerson(request.Email,new CreatePersonRequestDto
            {
               DateOfBirth = DateTime.MinValue,
               Email = request.Email,
               Name = request.Name,
               Surname = request.Surname,
               IsSubscribed = true
            });
            var response = await communicationService.RegisterAsync(request, Session.SessionId);
            return new CreateSubscriptionResponse { IsSuccessful = response };
        }
    }
}
