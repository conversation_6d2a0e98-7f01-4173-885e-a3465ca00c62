//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AirlineQueuePortServiceReference
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/wsdl", ConfigurationName="AirlineQueuePortServiceReference.AirlineQueuePort")]
    public interface AirlineQueuePort
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#placeReservationsInQueue", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<AirlineQueuePortServiceReference.placeReservationsInQueueResponse> placeReservationsInQueueAsync(AirlineQueuePortServiceReference.placeReservationsInQueueRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#removeMultipleReservationsFromQueue", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueResponse> removeMultipleReservationsFromQueueAsync(AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewReservationsInQueue", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<AirlineQueuePortServiceReference.viewReservationsInQueueResponse> viewReservationsInQueueAsync(AirlineQueuePortServiceReference.viewReservationsInQueueRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PlacePnrsInQueueRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private PNRQueueDetailsType[] pNRQueueDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PNRQueueDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public PNRQueueDetailsType[] PNRQueueDetails
        {
            get
            {
                return this.pNRQueueDetailsField;
            }
            set
            {
                this.pNRQueueDetailsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BookingChannelKeyType
    {
        
        private string channelTypeField;
        
        private string channelField;
        
        private string localeField;
        
        private string sessionIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ChannelType
        {
            get
            {
                return this.channelTypeField;
            }
            set
            {
                this.channelTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Channel
        {
            get
            {
                return this.channelField;
            }
            set
            {
                this.channelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Locale
        {
            get
            {
                return this.localeField;
            }
            set
            {
                this.localeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string SessionId
        {
            get
            {
                return this.sessionIdField;
            }
            set
            {
                this.sessionIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class QueuedPNRType
    {
        
        private string pNRNumberField;
        
        private string queueNumberField;
        
        private string queueOfficeCodeField;
        
        private System.DateTime creationDateField;
        
        private string revenueCompanyField;
        
        private System.DateTime receivedAtGMTField;
        
        private bool receivedAtGMTFieldSpecified;
        
        private System.DateTime reasonTimeGMTField;
        
        private bool reasonTimeGMTFieldSpecified;
        
        private string reasonField;
        
        private string itemTagField;
        
        private System.DateTime receivedAtLTCField;
        
        private bool receivedAtLTCFieldSpecified;
        
        private System.DateTime recievedTimeInAgencyLTCField;
        
        private bool recievedTimeInAgencyLTCFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string PNRNumber
        {
            get
            {
                return this.pNRNumberField;
            }
            set
            {
                this.pNRNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string QueueNumber
        {
            get
            {
                return this.queueNumberField;
            }
            set
            {
                this.queueNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string QueueOfficeCode
        {
            get
            {
                return this.queueOfficeCodeField;
            }
            set
            {
                this.queueOfficeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public System.DateTime CreationDate
        {
            get
            {
                return this.creationDateField;
            }
            set
            {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string RevenueCompany
        {
            get
            {
                return this.revenueCompanyField;
            }
            set
            {
                this.revenueCompanyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public System.DateTime ReceivedAtGMT
        {
            get
            {
                return this.receivedAtGMTField;
            }
            set
            {
                this.receivedAtGMTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ReceivedAtGMTSpecified
        {
            get
            {
                return this.receivedAtGMTFieldSpecified;
            }
            set
            {
                this.receivedAtGMTFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public System.DateTime ReasonTimeGMT
        {
            get
            {
                return this.reasonTimeGMTField;
            }
            set
            {
                this.reasonTimeGMTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ReasonTimeGMTSpecified
        {
            get
            {
                return this.reasonTimeGMTFieldSpecified;
            }
            set
            {
                this.reasonTimeGMTFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string Reason
        {
            get
            {
                return this.reasonField;
            }
            set
            {
                this.reasonField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string ItemTag
        {
            get
            {
                return this.itemTagField;
            }
            set
            {
                this.itemTagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public System.DateTime ReceivedAtLTC
        {
            get
            {
                return this.receivedAtLTCField;
            }
            set
            {
                this.receivedAtLTCField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ReceivedAtLTCSpecified
        {
            get
            {
                return this.receivedAtLTCFieldSpecified;
            }
            set
            {
                this.receivedAtLTCFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public System.DateTime RecievedTimeInAgencyLTC
        {
            get
            {
                return this.recievedTimeInAgencyLTCField;
            }
            set
            {
                this.recievedTimeInAgencyLTCField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RecievedTimeInAgencyLTCSpecified
        {
            get
            {
                return this.recievedTimeInAgencyLTCFieldSpecified;
            }
            set
            {
                this.recievedTimeInAgencyLTCFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PNRRemovalStatus
    {
        
        private string pNRNumberField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string PNRNumber
        {
            get
            {
                return this.pNRNumberField;
            }
            set
            {
                this.pNRNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PNRQueueRemovalDetails
    {
        
        private string queueNumberField;
        
        private string queueNameField;
        
        private PNRRemovalStatus[] pNRRemovalStatusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string QueueNumber
        {
            get
            {
                return this.queueNumberField;
            }
            set
            {
                this.queueNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string QueueName
        {
            get
            {
                return this.queueNameField;
            }
            set
            {
                this.queueNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PNRRemovalStatus", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public PNRRemovalStatus[] PNRRemovalStatus
        {
            get
            {
                return this.pNRRemovalStatusField;
            }
            set
            {
                this.pNRRemovalStatusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class QueueDetailsInputType
    {
        
        private string queueNameField;
        
        private string queueNumberField;
        
        private string queuedOfficeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string QueueName
        {
            get
            {
                return this.queueNameField;
            }
            set
            {
                this.queueNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string QueueNumber
        {
            get
            {
                return this.queueNumberField;
            }
            set
            {
                this.queueNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string QueuedOffice
        {
            get
            {
                return this.queuedOfficeField;
            }
            set
            {
                this.queuedOfficeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PnrQueueDetailsInputType
    {
        
        private string[] pnrNumberField;
        
        private QueueDetailsInputType queueDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PnrNumber", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string[] PnrNumber
        {
            get
            {
                return this.pnrNumberField;
            }
            set
            {
                this.pnrNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public QueueDetailsInputType QueueDetails
        {
            get
            {
                return this.queueDetailsField;
            }
            set
            {
                this.queueDetailsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ErrorType
    {
        
        private string errorCodeField;
        
        private string errorValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string errorCode
        {
            get
            {
                return this.errorCodeField;
            }
            set
            {
                this.errorCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string errorValue
        {
            get
            {
                return this.errorValueField;
            }
            set
            {
                this.errorValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PNRQueueDetailsType
    {
        
        private string pNRNumberField;
        
        private string queueNumberField;
        
        private string queueOfficeCodeField;
        
        private string reasonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string PNRNumber
        {
            get
            {
                return this.pNRNumberField;
            }
            set
            {
                this.pNRNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string QueueNumber
        {
            get
            {
                return this.queueNumberField;
            }
            set
            {
                this.queueNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string QueueOfficeCode
        {
            get
            {
                return this.queueOfficeCodeField;
            }
            set
            {
                this.queueOfficeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string Reason
        {
            get
            {
                return this.reasonField;
            }
            set
            {
                this.reasonField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PlacePnrsInQueueRS
    {
        
        private string airlineCodeField;
        
        private string statusField;
        
        private PNRQueueDetailsType[] unqueuedPNRsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("UnqueuedPNRs", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public PNRQueueDetailsType[] UnqueuedPNRs
        {
            get
            {
                return this.unqueuedPNRsField;
            }
            set
            {
                this.unqueuedPNRsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class placeReservationsInQueueRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public AirlineQueuePortServiceReference.PlacePnrsInQueueRQ PlacePnrsInQueueRQ;
        
        public placeReservationsInQueueRequest()
        {
        }
        
        public placeReservationsInQueueRequest(AirlineQueuePortServiceReference.PlacePnrsInQueueRQ PlacePnrsInQueueRQ)
        {
            this.PlacePnrsInQueueRQ = PlacePnrsInQueueRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class placeReservationsInQueueResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public AirlineQueuePortServiceReference.PlacePnrsInQueueRS PlacePnrsInQueueRS;
        
        public placeReservationsInQueueResponse()
        {
        }
        
        public placeReservationsInQueueResponse(AirlineQueuePortServiceReference.PlacePnrsInQueueRS PlacePnrsInQueueRS)
        {
            this.PlacePnrsInQueueRS = PlacePnrsInQueueRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RemoveMultiplePnrsFromQueueRQ
    {
        
        private string airlineCodeField;
        
        private PnrQueueDetailsInputType[] pnrQueueDetailsField;
        
        private BookingChannelKeyType bookingChannelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PnrQueueDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public PnrQueueDetailsInputType[] PnrQueueDetails
        {
            get
            {
                return this.pnrQueueDetailsField;
            }
            set
            {
                this.pnrQueueDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RemoveMultiplePnrsFromQueueRS
    {
        
        private string airlineCodeField;
        
        private PNRQueueRemovalDetails[] pNRQueueRemovalDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PNRQueueRemovalDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public PNRQueueRemovalDetails[] PNRQueueRemovalDetails
        {
            get
            {
                return this.pNRQueueRemovalDetailsField;
            }
            set
            {
                this.pNRQueueRemovalDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class removeMultipleReservationsFromQueueRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public AirlineQueuePortServiceReference.RemoveMultiplePnrsFromQueueRQ RemoveMultiplePnrsFromQueueRQ;
        
        public removeMultipleReservationsFromQueueRequest()
        {
        }
        
        public removeMultipleReservationsFromQueueRequest(AirlineQueuePortServiceReference.RemoveMultiplePnrsFromQueueRQ RemoveMultiplePnrsFromQueueRQ)
        {
            this.RemoveMultiplePnrsFromQueueRQ = RemoveMultiplePnrsFromQueueRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class removeMultipleReservationsFromQueueResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public AirlineQueuePortServiceReference.RemoveMultiplePnrsFromQueueRS RemoveMultiplePnrsFromQueueRS;
        
        public removeMultipleReservationsFromQueueResponse()
        {
        }
        
        public removeMultipleReservationsFromQueueResponse(AirlineQueuePortServiceReference.RemoveMultiplePnrsFromQueueRS RemoveMultiplePnrsFromQueueRS)
        {
            this.RemoveMultiplePnrsFromQueueRS = RemoveMultiplePnrsFromQueueRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewPNRsInQueueRQ
    {
        
        private string airlineCodeField;
        
        private string officeCodeField;
        
        private string queueNumberField;
        
        private string queueNameField;
        
        private System.DateTime fromTimeField;
        
        private bool fromTimeFieldSpecified;
        
        private System.DateTime toTimeField;
        
        private bool toTimeFieldSpecified;
        
        private BookingChannelKeyType bookingChannelField;
        
        private long startIndexField;
        
        private bool startIndexFieldSpecified;
        
        private long endIndexField;
        
        private bool endIndexFieldSpecified;
        
        private string agencyCodeField;
        
        private string itemTagField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string OfficeCode
        {
            get
            {
                return this.officeCodeField;
            }
            set
            {
                this.officeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string QueueNumber
        {
            get
            {
                return this.queueNumberField;
            }
            set
            {
                this.queueNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string QueueName
        {
            get
            {
                return this.queueNameField;
            }
            set
            {
                this.queueNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime FromTime
        {
            get
            {
                return this.fromTimeField;
            }
            set
            {
                this.fromTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FromTimeSpecified
        {
            get
            {
                return this.fromTimeFieldSpecified;
            }
            set
            {
                this.fromTimeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public System.DateTime ToTime
        {
            get
            {
                return this.toTimeField;
            }
            set
            {
                this.toTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ToTimeSpecified
        {
            get
            {
                return this.toTimeFieldSpecified;
            }
            set
            {
                this.toTimeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public long StartIndex
        {
            get
            {
                return this.startIndexField;
            }
            set
            {
                this.startIndexField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool StartIndexSpecified
        {
            get
            {
                return this.startIndexFieldSpecified;
            }
            set
            {
                this.startIndexFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public long EndIndex
        {
            get
            {
                return this.endIndexField;
            }
            set
            {
                this.endIndexField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EndIndexSpecified
        {
            get
            {
                return this.endIndexFieldSpecified;
            }
            set
            {
                this.endIndexFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string AgencyCode
        {
            get
            {
                return this.agencyCodeField;
            }
            set
            {
                this.agencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string ItemTag
        {
            get
            {
                return this.itemTagField;
            }
            set
            {
                this.itemTagField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewPNRsInQueueRS
    {
        
        private string airlineCodeField;
        
        private string totalCountField;
        
        private QueuedPNRType[] queuedPNRField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string TotalCount
        {
            get
            {
                return this.totalCountField;
            }
            set
            {
                this.totalCountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("QueuedPNR", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public QueuedPNRType[] QueuedPNR
        {
            get
            {
                return this.queuedPNRField;
            }
            set
            {
                this.queuedPNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewReservationsInQueueRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public AirlineQueuePortServiceReference.ViewPNRsInQueueRQ ViewPNRsInQueueRQ;
        
        public viewReservationsInQueueRequest()
        {
        }
        
        public viewReservationsInQueueRequest(AirlineQueuePortServiceReference.ViewPNRsInQueueRQ ViewPNRsInQueueRQ)
        {
            this.ViewPNRsInQueueRQ = ViewPNRsInQueueRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewReservationsInQueueResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public AirlineQueuePortServiceReference.ViewPNRsInQueueRS ViewPNRsInQueueRS;
        
        public viewReservationsInQueueResponse()
        {
        }
        
        public viewReservationsInQueueResponse(AirlineQueuePortServiceReference.ViewPNRsInQueueRS ViewPNRsInQueueRS)
        {
            this.ViewPNRsInQueueRS = ViewPNRsInQueueRS;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface AirlineQueuePortChannel : AirlineQueuePortServiceReference.AirlineQueuePort, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class AirlineQueuePortClient : System.ServiceModel.ClientBase<AirlineQueuePortServiceReference.AirlineQueuePort>, AirlineQueuePortServiceReference.AirlineQueuePort
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public AirlineQueuePortClient() : 
                base(AirlineQueuePortClient.GetDefaultBinding(), AirlineQueuePortClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.AirlineQueuePort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AirlineQueuePortClient(EndpointConfiguration endpointConfiguration) : 
                base(AirlineQueuePortClient.GetBindingForEndpoint(endpointConfiguration), AirlineQueuePortClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AirlineQueuePortClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(AirlineQueuePortClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AirlineQueuePortClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(AirlineQueuePortClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AirlineQueuePortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<AirlineQueuePortServiceReference.placeReservationsInQueueResponse> AirlineQueuePortServiceReference.AirlineQueuePort.placeReservationsInQueueAsync(AirlineQueuePortServiceReference.placeReservationsInQueueRequest request)
        {
            return base.Channel.placeReservationsInQueueAsync(request);
        }
        
        public System.Threading.Tasks.Task<AirlineQueuePortServiceReference.placeReservationsInQueueResponse> placeReservationsInQueueAsync(AirlineQueuePortServiceReference.PlacePnrsInQueueRQ PlacePnrsInQueueRQ)
        {
            AirlineQueuePortServiceReference.placeReservationsInQueueRequest inValue = new AirlineQueuePortServiceReference.placeReservationsInQueueRequest();
            inValue.PlacePnrsInQueueRQ = PlacePnrsInQueueRQ;
            return ((AirlineQueuePortServiceReference.AirlineQueuePort)(this)).placeReservationsInQueueAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueResponse> AirlineQueuePortServiceReference.AirlineQueuePort.removeMultipleReservationsFromQueueAsync(AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueRequest request)
        {
            return base.Channel.removeMultipleReservationsFromQueueAsync(request);
        }
        
        public System.Threading.Tasks.Task<AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueResponse> removeMultipleReservationsFromQueueAsync(AirlineQueuePortServiceReference.RemoveMultiplePnrsFromQueueRQ RemoveMultiplePnrsFromQueueRQ)
        {
            AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueRequest inValue = new AirlineQueuePortServiceReference.removeMultipleReservationsFromQueueRequest();
            inValue.RemoveMultiplePnrsFromQueueRQ = RemoveMultiplePnrsFromQueueRQ;
            return ((AirlineQueuePortServiceReference.AirlineQueuePort)(this)).removeMultipleReservationsFromQueueAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<AirlineQueuePortServiceReference.viewReservationsInQueueResponse> AirlineQueuePortServiceReference.AirlineQueuePort.viewReservationsInQueueAsync(AirlineQueuePortServiceReference.viewReservationsInQueueRequest request)
        {
            return base.Channel.viewReservationsInQueueAsync(request);
        }
        
        public System.Threading.Tasks.Task<AirlineQueuePortServiceReference.viewReservationsInQueueResponse> viewReservationsInQueueAsync(AirlineQueuePortServiceReference.ViewPNRsInQueueRQ ViewPNRsInQueueRQ)
        {
            AirlineQueuePortServiceReference.viewReservationsInQueueRequest inValue = new AirlineQueuePortServiceReference.viewReservationsInQueueRequest();
            inValue.ViewPNRsInQueueRQ = ViewPNRsInQueueRQ;
            return ((AirlineQueuePortServiceReference.AirlineQueuePort)(this)).viewReservationsInQueueAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.AirlineQueuePort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.AirlineQueuePort))
            {
                return new System.ServiceModel.EndpointAddress("https://fhstg.ibsplc.aero/iRes_Booking_WS/services/AirlineQueuePort");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return AirlineQueuePortClient.GetBindingForEndpoint(EndpointConfiguration.AirlineQueuePort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return AirlineQueuePortClient.GetEndpointAddress(EndpointConfiguration.AirlineQueuePort);
        }
        
        public enum EndpointConfiguration
        {
            
            AirlineQueuePort,
        }
    }
}
