namespace Ubimecs.Infrastructure.Mailing.Constants;

public static class MailActionCodes
{
    /// <summary>Pnr Creation</summary>
    public const string Create = "CREATE";
    
    /// <summary>Add a Flight</summary>
    public const string AddOandD = "ADD_OANDD";

    /// <summary>Substitute Flight Action</summary>
    public const string Move = "MOVE";
    
    /// <summary>Cancel and Rebook With Ticket</summary>
    public const string CancelAndRebook = "CANCEL_AND_REBOOK";
    
    /// <summary>Cancel Flight</summary>
    public const string DeleteOandD = "DELETE_OANDD";
    
    /// <summary>Cancel PNR</summary>
    public const string CancelPnr = "CANCEL_PNR";
    
    /// <summary>Accept Reaccomodation</summary>
    public const string AcceptReaccommodation = "ACCEPT_REACCOMODATION";
    
    /// <summary>Reject Reaccomodation</summary>
    public const string RejectReaccommodation = "REJECT_REACCOMMODATION";
    
    /// <summary>Modify Pax</summary>
    public const string ModifyPax = "MODIFY_PAX";
    
    /// <summary>Add Infant</summary>
    public const string AddInfant = "ADD_INFANT";

    /// <summary>Add SSR</summary>
    public const string AddSsr = "ADD_SSR";
    
    /// <summary>Delete SSR</summary>
    public const string DeleteSsr = "DELETE_SSR";
    
    /// <summary>Delete Seat</summary>
    public const string DeleteSeat = "DELETE_SEAT";
    
    /// <summary>Add Seat</summary>
    public const string AddSeat = "ADD_SEAT";
    
    /// <summary>Schedule Change Action</summary>
    public const string ScheduleChange = "SCHEDULE_CHANGE";

    /// <summary>Segment Time Change</summary>
    public const string TimeChange = "TIME_CHANGE";
    
    /// <summary>Time Change Within Threshold</summary>
    public const string TimeChangeWithinThreshold = "TIME_CHANGE_WITHIN_THRESHOLD";

    /// <summary>Gift Certificate</summary>
    public const string GiftCertificate = "GIFT_CERTIFICATE";
    
    /// <summary>Modify SSR</summary>
    public const string ModifySsr = ""; 

}

