<div style="font-family: Arial, sans-serif; background-color: #1a4480; color: white; padding: 40px 24px;">
    <div style="position: relative; z-index: 2;">
        <h1 style="font-weight: 600; font-size: 36px; line-height: 44px; margin: 0; color: white;">Group Booking Confirmation</h1>
    </div>
    <div style="margin-top: 20px; position: relative; z-index: 2;">
        <p style="font-weight: 400; font-size: 36px; line-height: 44px; margin: 0; color: white;">Have a good trip!</p>
    </div>
    <div style="margin-top: 80px; display: flex; flex-wrap: wrap; width: 100%; position: relative; z-index: 2; gap: 40px;">
        <div style="width: 40%;">
            <div style="display: flex; margin-bottom: 15px;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Group Request Date:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@Model.RequestDate</p>
            </div>
            <div style="display: flex;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Group Name:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@Model.GroupName</p>
            </div>
        </div>
        <div style="width: calc(60% - 40px);">
            <div style="display: flex; margin-bottom: 15px;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Number of Passengers:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@Model.NumberOfPassengers</p>
            </div>
            <div style="display: flex; margin-bottom: 15px;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Group Type:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@Model.GroupType</p>
            </div>  
            <div style="display: flex;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Name:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@Model.FirstName @Model.LastName</p>
            </div>
        </div>
    </div>
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Flight Summary</h1>
  @foreach(var flight in Model.Flights){
        <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px;">
        <div style="display: flex; margin-bottom: 24px;">
            <!-- Outbound Column -->
            <div style="flex: 1; padding-right: 16px;">
                <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Outbound Flight</p>
                <h2 style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 24px 0;">@flight.DepartureDate</h2>
            </div>
            
            <!-- Inbound Column -->
            <div style="flex: 1; padding-left: 16px; border-left: 1px solid #EBF0F5;">
                <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Inbound Flight</p>
                <h2 style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 24px 0;">@flight.ArrivalDate</h2>
            </div>
        </div>
    </div>

}
</div>

<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Comment</h1>
    <p>@Model.GroupInformationMessage</p>
</div>


<div style="font-family: Arial, sans-serif; background-color: #f0f7ff; border-radius: 12px; padding: 24px; margin-top: 24px;">
    <p style="color: #4A5568; font-weight: 500; font-size: 18px; line-height: 26px; margin: 0 0 16px 0;">Dear Passenger,</p>
    
    <p style="color: #4A5568; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 24px 0;">Thank you for choosing @Model.AirlineName</p>
    
    <p style="color: #4A5568; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 16px 0;">
        Please be at the airport at <span style="color: #E67E22; font-weight: 500;">least @Model.HoursBeforeTime</span> prior to depature for international flights.
    </p>
</div>
