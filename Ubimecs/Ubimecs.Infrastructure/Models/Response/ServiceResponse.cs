using Ubimecs.Infrastructure.Models.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class ServiceResponse
    {
        public ServiceResponse()
        {

        }
        public object Data { get; set; }
        public string SessionID { get; set; }
        public string Message { get; set; }
        public ButtonInfoDTO Button1 { get; set; }
        public ButtonInfoDTO Button2 { get; set; }
        public bool Cancelable { get; set; }
        public string DeveloperMessage { get; set; }        
        public ResponseStatusCodeEnum StatusCode { get; set; } = ResponseStatusCodeEnum.SUCCESSFUL;
        
        public string ErrorCode {get; set;}
        public string ErrorPrefix { get; set; }
        public Dictionary<string,Dictionary<string,string>> ErrorGroupMessage  { get; set; }
        public TripSummaryDTO TripSummary { get; set; } = new();
    }

}
