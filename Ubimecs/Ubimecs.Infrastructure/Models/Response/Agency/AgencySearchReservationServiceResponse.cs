namespace Ubimecs.Infrastructure.Models.Response.Agency
{
    public class AgencySearchReservationServiceResponse
    {
        public List<ReservationDetail> Reservations { get; set; } = new();
        public int TotalCount { get; set; } // Toplam rezervasyon sayısı
        public int CurrentPage { get; set; } // Şu anki sayfa numarası
        public int TotalPages { get; set; } // Toplam sayfa sayısı
    }

    public class ReservationDetail
    {
        public DateTime ReservationDate { get; set; }
        public string PnrNumber { get; set; }
        public DateTime FlightDate { get; set; }
        public string FlightNumber { get; set; }
        public PassengerInfo Passenger { get; set; }
        public string Departure { get; set; }
        public string Arrival { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
    }

    public class PassengerInfo
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
    }

    public enum PaymentStatus
    {
        PAID,
        PARTIAL_PAID,
        UNPAID
    }


}
