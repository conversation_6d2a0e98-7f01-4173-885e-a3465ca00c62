using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.DTO.Baggage
{
    public class PassengerFlightSportEquipment
    {
        public string ServiceId { get; set; }
        public string FlightTmobId { get; set; }
        public string PassengerId { get; set; }
        public int Count { get; set; }
        public string ServiceName { get; set; } 
        public double FeeAmount { get; set; }
        public string Currency { get; set; }
    }
}
