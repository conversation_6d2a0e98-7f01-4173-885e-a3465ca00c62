using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Infrastructure.Models.DTO.Price;

namespace Ubimecs.Infrastructure.Models.DTO
{
    public class OrderRetrieveFlightDestination : BaseFlightDTO
    {

        public ServiceCategoryEnum SelectedBundleCategoryId { get; set; }
        public string SelectedBundleCode { get; set; }
        public bool IsCheckInOpen { get; set; }
        public bool CheckInTimeOut { get; set; }
        public bool IsUpgradePossible { get; set; }
        public bool IsCheckInRestricted { get; set; }
        public bool IsPlcRoute { get; set; }
        public string PlcUrl { get; set; }
        public string RemainingDepartureTime { get; set; }
        public string CheckInRemainingTime { get; set; }
        public int CheckInRemainingHour { get; set; }
        public decimal CheckInClosedTime { get; set; }
        public bool IsCheckInCompleted { get; set; }
        public bool IsDisabled { get; set; }
        public bool IsChangeFlightButtonDisabled { get; set; }
        public bool IsCancelFlightButtonDisabled { get; set; }
        public bool IsUpgradeFlightButtonDisabled { get; set; }
        public bool IsCheckInButtonDisabled { get; set; }
        public bool IsAddExtrasButtonDisabled { get; set; }
        public bool IsAddFlightButtonDisabled { get; set; }
        public bool IsFlightCountryCodeAllowed { get; set; }        
        public List<string> CheckedInPassengerIds { get; set; }
        public bool IsCheckinIncludeRestirctedSsr { get; set; }
        public string SegmentStatus { get; set; }
        public decimal FlightPrice { get; set; } 
        public int RemainingDepartureHour { get; set; }
        public bool IsAlternativeFlightOffered { get; set; }
        public List<BasePriceDTO> Prices { get; set; } = new List<BasePriceDTO>();
    }
}