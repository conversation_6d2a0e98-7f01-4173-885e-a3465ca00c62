using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.DTO.Flight
{
    public class FlightSegmentDTO
    {
        public string Id { get; set; }
        public string LocalId { get; set; }
        public string DepartureCode { get; set; }
        public string ArrivalCode { get; set; }
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
        public TimeSpan JourneyTime { get; set; }
        
        private string _departureTime;
        private string _arrivalTime;
        public string DepartureTime {
            get
            {
                if (_departureTime is null) return null;
                if (DateTime.TryParse(_departureTime, out DateTime parsedTime))
                {
                    // Başarılıysa, 24 saat formatında string olarak döndür
                    return parsedTime.ToString("HH:mm");
                }
                // Başarısızsa orijinal değeri döndür
                return _departureTime;
            }
            set
            {
                // Value'yu doğrudan yedek alana atıyoruz
                _departureTime = value;
            }
        }
        public string ArrivalTime {

            get
            {
                if (_arrivalTime is null) return null;
                if (DateTime.TryParse(_arrivalTime, out DateTime parsedTime))
                {
                    // Başarılıysa, 24 saat formatında string olarak döndür
                    return parsedTime.ToString("HH:mm");
                }
                // Başarısızsa orijinal değeri döndür
                return _arrivalTime;
            }
            set
            {
                // Value'yu doğrudan yedek alana atıyoruz
                _arrivalTime = value;
            }
        }
        public string FlightNumber { get; set; }
        public string AirlineId { get; set; }
        public string CarrierName { get; set; }
        public bool IsInternational { get; set; }
        public string FlightSegmentType { get; set; }
        public bool NotConfirmed { get; set; }
        public string FareBasisCode { get; set; }
        public string PriceClassName { get; set; }
        public int Stops { get; set; }
        public int DayChange { get; set; }
        public int SeatAvailablity { get; set; }
        public string CabinClass { get; set; }
        public string FlightSegmentGroupID { get; set; }
        public string SegmenReferenceId { get; set; }
        public AircraftInfo AircraftInfo { get; set; }
        public string TmobId
        {
            get
            {
                return DepartureCode + "|" + ArrivalCode + "|" + DepartureDate.ToString("dd.MM.yyyy") + "|" + DepartureTime;
            }
        }

        public DateTime CompleteDepartureTime
        {
            get
            {
                DateTime newCompleteDepartureDate = new DateTime(DepartureDate.Year, DepartureDate.Month, DepartureDate.Day, 0, 0, 0);
                return newCompleteDepartureDate.Date.Add(TimeSpan.TryParse(DepartureTime, out TimeSpan result) ? result : TimeSpan.Zero);
            }
        }

        public DateTime CompleteArrivalTime
        {
            get
            {
                DateTime newCompleteArrivalTime = new DateTime(ArrivalDate.Year, ArrivalDate.Month, ArrivalDate.Day, 0, 0, 0);
                return newCompleteArrivalTime.Date.Add(TimeSpan.TryParse(ArrivalTime, out TimeSpan result) ? result : TimeSpan.Zero);
            }
        }

        public string DepartureCity { get; set; }
        public string ArrivalCity { get; set; }
        public string FormattedDepartureDate { get; set; }


        public string CityDescription
        {
            get
            {
                return $"{DepartureCity} - {ArrivalCity}";
            }
        }

        public string TimeDescription
        {
            get
            {
                return $"{FormattedDepartureDate}, {CompleteDepartureTime.ToString("HH:mm")} - {CompleteArrivalTime.ToString("HH:mm")}";
            }
        }
    }

    public class AircraftInfo
    {
        public string Type { get; set; }
        public string Version { get; set; }
    }
}
