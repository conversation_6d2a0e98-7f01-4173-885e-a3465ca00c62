using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Ubimecs.Infrastructure.Utilities
{
    public class CommonSecurityFunctions
    {

        private static IHttpContextAccessor _httpContextAccessor;
        public CommonSecurityFunctions(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="key">Cryptography key</param>
        /// <returns></returns>
        private static RijndaelManaged GetCryptoParams(string key)
        {
            var keyBytes = new byte[16];
            var secretKeyBytes = Encoding.UTF8.GetBytes(key);
            Array.Copy(secretKeyBytes, keyBytes, Math.Min(keyBytes.Length, secretKeyBytes.Length));
            var aes = new RijndaelManaged
            {
                Mode = CipherMode.CBC,
                Padding = PaddingMode.PKCS7,
                KeySize = 128,
                BlockSize = 128,
                Key = keyBytes,
                IV = keyBytes
            };

            return aes;
        }

        /// <summary>
        /// Encodes input with key using AES
        /// </summary>
        /// <param name="key">Cryptography key</param>
        /// <param name="input">Plain text</param>
        /// <returns>Crypted Text</returns>
        public static string EncodeAes(string key, string input)
        {
            var aes = GetCryptoParams(key);
            var plainBytes = Encoding.UTF8.GetBytes(input);
            return Convert.ToBase64String(AesEncrypt(plainBytes, aes));
        }

        /// <summary>
        /// Decodes text with key using AES
        /// </summary>
        /// <param name="key">Cryptography key</param>
        /// <param name="thisDecode">Crypted Text</param>
        /// <returns>Plain Text</returns>
        public static string DecodeAes(string key, string thisDecode)
        {
            var aes = GetCryptoParams(key);
            try
            {
                var encryptedBytes = Convert.FromBase64String(thisDecode);
                return Encoding.UTF8.GetString(AesDecrypt(encryptedBytes, aes));
            }
            catch
            {
                return null;

            }

        }

        /// <summary>
        /// Sha1 algorithm 
        /// </summary>  
        /// <param name="input">Plain Text</param>
        /// <returns>Sha1 String</returns>
        public static string Sha1(string input)
        {
            SHA1 sha = new SHA1Managed();
            var data = Encoding.UTF8.GetBytes(input);
            var digest = sha.ComputeHash(data);

            return GetAsHexaDecimal(digest);
        }

        /// <summary>
        /// Byte array to Hex string
        /// </summary>
        /// <param name="bytes">Byte array which will be converted</param>
        /// <returns>Hex String</returns>
        private static string GetAsHexaDecimal(byte[] bytes)
        {
            var s = new StringBuilder();
            var length = bytes.Length;
            for (var n = 0; n < length; n++)
            {
                s.Append(string.Format("{0,2:x}", bytes[n]).Replace(" ", "0"));
            }
            return s.ToString();
        }

        /// <summary>
        /// MD5 hash algorithm
        /// </summary>
        /// <param name="phrase">plain text</param>
        /// <returns>crypted text</returns>
        public static string Md5Encrypt(string phrase)
        {
            var encoder = new UTF8Encoding();
            var md5Hasher = new MD5CryptoServiceProvider();
            var hashedDataBytes = md5Hasher.ComputeHash(encoder.GetBytes(phrase));
            return ByteArrayToString(hashedDataBytes);
        }

        /// <summary>
        /// Sha256 algorith
        /// </summary>
        /// <param name="phrase">plain text</param>
        /// <returns></returns>
        public static string Sha256(string phrase)
        {
            var encoder = new UTF8Encoding();
            var sha256Hasher = new SHA256Managed();
            var hashedDataBytes = sha256Hasher.ComputeHash(encoder.GetBytes(phrase));
            return ByteArrayToString(hashedDataBytes);
        }

        /// <summary>
        /// Sha 384 algorithm
        /// </summary>
        /// <param name="phrase">plain text</param>
        /// <returns></returns>
        public static string Sha384(string phrase)
        {
            var encoder = new UTF8Encoding();
            var sha384Hasher = new SHA384Managed();
            var hashedDataBytes = sha384Hasher.ComputeHash(encoder.GetBytes(phrase));
            return ByteArrayToString(hashedDataBytes);
        }

        /// <summary>
        /// Sha 512 algorithm
        /// </summary>
        /// <param name="phrase">plain text</param>
        /// <returns></returns>
        public static string Sha512(string phrase)
        {
            var encoder = new UTF8Encoding();
            var sha512Hasher = new SHA512Managed();
            var hashedDataBytes = sha512Hasher.ComputeHash(encoder.GetBytes(phrase));
            return ByteArrayToString(hashedDataBytes);
        }

        /// <summary>
        /// Byte array to sting
        /// </summary>
        /// <param name="inputArray">byte array</param>
        /// <returns></returns>
        private static string ByteArrayToString(byte[] inputArray)
        {
            var output = new StringBuilder("");
            for (var i = 0; i < inputArray.Length; i++)
            {
                output.Append(inputArray[i].ToString("X2"));
            }
            return output.ToString();
        }

        #region AES
        /// <summary>
        /// 
        /// </summary>
        /// <param name="plainBytes"></param>
        /// <param name="rijndaelManaged"></param>
        /// <returns></returns>
        private static byte[] AesEncrypt(byte[] plainBytes, RijndaelManaged rijndaelManaged)
        {
            return rijndaelManaged.CreateEncryptor()
                .TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="encryptedData"></param>
        /// <param name="rijndaelManaged"></param>
        /// <returns></returns>
        private static byte[] AesDecrypt(byte[] encryptedData, RijndaelManaged rijndaelManaged)
        {
            return rijndaelManaged.CreateDecryptor()
                .TransformFinalBlock(encryptedData, 0, encryptedData.Length);
        }

        #endregion
    }
}
