using PuppeteerSharp;
using Ubimecs.Infrastructure.FileService.Contracts;


namespace Ubimecs.Infrastructure.FileService.Services;

public class HtmlFileGenerator : IFileGenerator
{
    public string ContentType => "application/pdf";
    public string FileExtension => ".pdf";
    

    public async Task<Stream> GenerateFileStreamAsync<T>(T data)
    {
        if (data is not string htmlContent)
            throw new ArgumentException("HTML içeriği string formatında olmalıdır.", nameof(data));
        
        await new BrowserFetcher().DownloadAsync();

        using var browser = await Puppeteer.LaunchAsync(new LaunchOptions { Headless = true });
        using var page = await browser.NewPageAsync();
        await page.SetContentAsync(htmlContent);

        var pdfStream = new MemoryStream(await page.PdfDataAsync());
        pdfStream.Position = 0;

        return pdfStream;
        
    }
}