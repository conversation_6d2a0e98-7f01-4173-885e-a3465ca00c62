using Ubimecs.Infrastructure.FileService.Contracts;
using Ubimecs.Infrastructure.FileService.Models.Enums;

namespace Ubimecs.Infrastructure.FileService.Services.Exports;

public class PnrListExportHandler: IExportHandler
{
    private readonly IFileGeneratorFactory _fileGeneratorFactory;

    public ActionType ActionType => ActionType.PnrListReport;

    public PnrListExportHandler(IFileGeneratorFactory fileGeneratorFactory)
    {
        _fileGeneratorFactory = fileGeneratorFactory;
    }

    public async Task<Stream> HandleAsync(Func<Task<object>> callback,ExtentionType extentionType)
    {
        if (callback is null)
            throw new ArgumentNullException(nameof(callback));

        var data = await callback();

        if (data is null)
            throw new InvalidOperationException("No data returned for export.");
        
        var fileGenerator = _fileGeneratorFactory.GetGenerator(extentionType.ToString());

        return await fileGenerator.GenerateFileStreamAsync(data);
    }
}